<template>
  <common-dialog
    class="common-dialog"
    width="80%"
    title="赠送优惠券备注"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form ref="formModal" :model="form">
        <el-form-item prop="remark">
          <el-input
            v-model="form.remark"
            :autosize="{ minRows: 4, maxRows: 6 }"
            type="textarea"
            :placeholder="placeholder"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 弹框标题
    title: {
      type: String,
      default: '订单备注'
    },
    // 提示语
    placeholder: {
      type: String,
      default: '请输入备注'
    },
    currentRow: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      confirmLoading: false,
      show: false,
      form: {
        remark: ''
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框
    open() {
      if (this.currentRow.id) {
        this.form.remark = this.currentRow.remark;
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        this.confirmLoading = true;
        const form = {
          id: this.currentRow.id,
          remark: this.form.remark
        };

        this.$post('updateZMCouponGiveRemark', form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.show = false;
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.common-dialog {
  ::v-deep .yz-common-dialog {
    margin-top: 30vh !important;
  }
}
</style>
