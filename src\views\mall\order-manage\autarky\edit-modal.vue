<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    title="查看订单信息"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-descriptions>
        <el-descriptions-item label="订单号">{{ form.orderId }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">{{ form.status | logisticsStatusEnum }}</el-descriptions-item>
        <el-descriptions-item label="下单人姓名">{{ form.realName }}</el-descriptions-item>
        <el-descriptions-item label="下单人远智编号">{{ form.yzCode }}</el-descriptions-item>
        <el-descriptions-item label="下单人时间">{{ form.createTime }}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="商品信息" direction="vertical" :column="7" border>
        <el-descriptions-item label="商品名称">{{ form.productName }}</el-descriptions-item>
        <el-descriptions-item label="规格名称">{{ form.productSpecName }}</el-descriptions-item>
        <el-descriptions-item label="商品单价(元)">{{ form.marketPrice }}</el-descriptions-item>
        <el-descriptions-item label="购买数量">{{ form.amount }}</el-descriptions-item>
        <el-descriptions-item label="总价(元)">{{ form.totalPrice }}</el-descriptions-item>
        <el-descriptions-item label="运费(元)">{{ form.freightAmount }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ form.remark }}</el-descriptions-item>
      </el-descriptions>

      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="mini"
      >
        <p class="des-title">收货人信息 </p>
        <el-form-item label="收货人:" prop="consigneeName">
          <el-input v-if="form.status == 'WAIT_SEND'" v-model="form.consigneeName" placeholder="请输入收货人" />
          <span v-else>{{ form.consigneeName }}</span>
        </el-form-item>
        <el-form-item label="联系电话:" prop="consigneeMobile">
          <el-input v-if="form.status == 'WAIT_SEND'" v-model="form.consigneeMobile" placeholder="请输入联系电话" />
          <span v-else>{{ form.consigneeMobile }}</span>
        </el-form-item>

        <el-form-item label="收获省市:" prop="districtCode">
          <template v-if="form.status == 'WAIT_SEND'">
            <el-select v-model="form.provinceCode" placeholder="请选择省" clearable style="width: 120px" @change="provinceChange" @clear="provinceCodeClear">
              <el-option v-for="item in provinceList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
            </el-select>
            <el-select v-model="form.cityCode" placeholder="请选择市" clearable style="width: 120px" @change="cityChange" @clear="cityCodeClear">
              <el-option v-for="item in cityList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
            </el-select>
            <el-select v-model="form.districtCode" placeholder="请选择区" clearable style="width: 150px" @change="districtChange" @clear="districtCodeClear">
              <el-option v-for="item in districtList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
            </el-select>
            <el-select v-model="form.streetCode" placeholder="请选择街道" clearable style="width: 200px">
              <el-option v-for="item in streetList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
            </el-select>
          </template>
          <template v-else>
            {{ form.consigneeProvince }} - {{ form.consigneeCity }} - {{ form.consigneeDistrict }}  {{ form.consigneeStreet ? '-' + form.consigneeStreet : '' }}
          </template>
        </el-form-item>

        <el-form-item label="收获详细地址:" prop="consigneeAddress">
          <el-input v-if="form.status == 'WAIT_SEND'" v-model="form.consigneeAddress" placeholder="请输入收获详细地址" />
          <span v-else>{{ form.consigneeAddress }}</span>
        </el-form-item>
        <el-form-item label="物流名称:" prop="logisticsType">
          <el-select v-model="form.logisticsType" placeholder="请选择物流名称">
            <el-option v-for="item in logisticsType" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物流编号:" prop="logisticsNo">
          <el-input v-model="form.logisticsNo" placeholder="请输入物流编号" />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { logisticsStatus, logisticsType } from './../../type';
import { arrToEnum } from '@/utils';
const logisticsStatusEnum = arrToEnum(logisticsStatus);
export default {
  filters: {
    logisticsStatusEnum(val) {
      return logisticsStatusEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      confirmLoading: false,
      logisticsType: logisticsType, // 快递公司
      show: false,
      form: {},
      rules: {
        consigneeName: [
          { required: true, message: '请输入收货人', trigger: 'blur' }
        ],
        consigneeMobile: [
          { required: true, message: '请输入联系电话', trigger: 'change' }
        ],
        districtCode: [
          { required: true, message: '请选择收货省市区', trigger: 'change' }
        ],
        consigneeAddress: [
          { required: true, message: '请输入收货详细地址', trigger: 'blur' }
        ],
        logisticsType: [
          { required: true, message: '请选择物流名称', trigger: 'change' }
        ],
        logisticsNo: [
          { required: true, message: '请输入物流编号', trigger: 'blur' }
        ]
      },
      provinceList: [],
      cityList: [],
      districtList: [],
      streetList: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框
    async open() {
      if (this.currentId) {
        this.isEdit = true;

        this.$http.get(`/mallOrder/getById/${this.currentId}`, { json: true }).then(res => {
          const { fail, body } = res;
          if (!fail) {
            body.provinceCode = Number(body.provinceCode);
            body.cityCode = Number(body.cityCode);
            body.districtCode = Number(body.districtCode);
            body.streetCode = body.streetCode == '' ? undefined : Number(body.streetCode);
            this.form = body;

            // 待发货
            if (this.form.status == 'WAIT_SEND') {
              this.getProvinceList();
              this.getCityList(this.form.provinceCode);
              this.getDistrictList(this.form.cityCode);
              this.getStreetList(this.form.districtCode);
              this.rules.logisticsType = [];
              this.rules.logisticsNo = [];
            }
          }
        });
      }
    },
    // 省级清空
    provinceCodeClear() {
      this.form.cityCode = undefined;
      this.cityList = [];

      this.form.districtCode = undefined;
      this.districtList = [];

      this.form.townCode = undefined;
      this.townList = [];
    },
    // 市级清空
    cityCodeClear() {
      this.form.districtCode = undefined;
      this.districtList = [];

      this.form.townCode = undefined;
      this.townList = [];
    },
    // 地区清空
    districtCodeClear() {
      this.form.townCode = undefined;
      this.townList = [];
    },
    // 省份改变
    provinceChange(provinceCode) {
      this.getCityList(provinceCode);
    },
    // 城市改变
    cityChange(cityCode) {
      this.getDistrictList(cityCode);
    },
    // 地区改变
    districtChange(districtCode) {
      this.getStreetList(districtCode);
    },
    // 获取省份
    getProvinceList() {
      this.$http.get(`/mallOrder/getProvince`).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.provinceList = body;
        }
      });
    },
    // 获取城市
    getCityList(provinceCode) {
      if (!provinceCode) return;
      this.$http.get(`/mallOrder/getCity/${provinceCode}`).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.cityList = body;
        }
      });
    },
    // 获取地区
    getDistrictList(cityCode) {
      if (!cityCode) return;
      this.$http.get(`/mallOrder/getDistrict/${cityCode}`).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.districtList = body;
        }
      });
    },
    // 获取街道列表
    getStreetList(districtCode) {
      if (!districtCode) return;
      this.$http.get(`/mallOrder/getTown/${districtCode}`).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.streetList = body;
        }
      });
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        this.confirmLoading = true;

        const {
          orderId,
          consigneeName,
          consigneeMobile,
          consigneeAddress,
          logisticsType,
          logisticsNo,
          provinceCode,
          cityCode,
          districtCode,
          streetCode
        } = this.form;
        let params = {
          orderId,
          logisticsType,
          logisticsNo
        };
        // 待发货
        if (this.form.status == 'WAIT_SEND') {
          const provinceInfo = this.provinceList.find(item => item.areaId == provinceCode);
          const cityInfo = this.cityList.find(item => item.areaId == cityCode);
          const districtInfo = this.districtList.find(item => item.areaId == districtCode);
          const streetInfo = this.streetList.find(item => item.areaId == streetCode);
          params = {
            ...params,
            consigneeName,
            consigneeMobile,
            consigneeAddress,
            provinceCode,
            cityCode,
            districtCode,
            streetCode,
            consigneeProvince: provinceInfo.areaName,
            consigneeCity: cityInfo.areaName,
            consigneeDistrict: districtInfo.areaName,
            consigneeStreet: streetInfo ? streetInfo.areaName : ''
          };
        }
        this.$post('updateZMOrder', params, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>

<style lang = "scss" scoped>
.des-title {
  font-size: 16px;
  font-weight: 700;
  color: #303133;
  margin: 12px 0;
}
</style>
