import Layout from '@/layout';

// 助学榜单 routers
export default [
  {
    path: '/helpStudyRank',
    component: Layout,
    meta: {
      title: '助学榜单',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: 'helpStudyRank/helpStudyPkRank',
        component: () => import('@/views/helpStudyRank2/helpStudyPkRank/index'),
        meta: {
          title: '助学PK榜单'
        }
      },
      {
        path: 'helpStudyRank/pkActivityManage',
        component: () => import('@/views/helpStudyRank2/pkActivityManage/index'),
        meta: {
          title: '助学PK活动管理'
        }
      },
      {
        path: '/helpStudyRank/pkHistory',
        component: () => import('@/views/helpStudyRank2/pkHistory/list'),
        meta: {
          title: '助学PK活动历史'
        }
      },
      {
        path: '/helpStudyRank/pkHistory/report',
        component: () => import('@/views/helpStudyRank2/pkHistory/report'),
        hidden: true,
        meta: {
          title: '历史PK战绩报表'
        }
      }
    ]
  }

];
