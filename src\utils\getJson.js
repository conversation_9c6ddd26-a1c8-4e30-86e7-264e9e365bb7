import axios from 'axios';
import { Message } from 'element-ui';
import { contentType, timeout, uri } from '@/config/request';
import store from '@/store';
import { getToken } from '@/utils/auth';
import service from '@/utils/axios';

const http = axios.create({
  baseURL: uri, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout, // request timeout
  // headers['Content-Type'] = contentType.form;
  headers: {
    'Content-Type': contentType.form
  }
});

http.interceptors.request.use(
  config => {
    // do something before request is sent
    return config;
  },
  error => {
    // do something with request error
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

http.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */
  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  res => {
    // 在这里对返回的数据进行处理
    if (res.status === 200) {
      return { body: res.data, fail: false };
    } else {
      Message({
        message: '未知错误',
        type: 'error'
      });
      return { body: res.data, fail: true };
    }
  },
  error => {
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    });
    return Promise.reject(error);
  }
);

export default http;
