import FileSaver from 'file-saver';

const downFileScript = (data) => {
  let fillScript = `

  document.getElementById('jzzszd').value = "${data.jzzszd ? data.jzzszd : ''}";

  document.getElementById('zzmmdm').value = "${data.zzmmdm ? data.zzmmdm : ''}";

  document.getElementById('dybmks_wyyzdm').value = "${data.wyyzdm ? data.wyyzdm : ''}";

  document.getElementById('kslxdm').value = "${data.kslxdm ? data.kslxdm : ''}";

  document.getElementById('zgjfbj').checked = true;

  document.getElementById('kslbdm').value = "${data.kslbdm ? data.kslbdm : ''}";
  document.getElementById('kslbdm').onchange();

  document.getElementById('kmzdm').value = "${data.kmzdm ? data.kmzdm : ''}";

  document.getElementById('jhlbdm').value = "${data.jhlbdm ? data.jhlbdm : ''}";


  document.getElementById('xqdm').value = "${data.xqdm ? data.xqdm : ''}";
  document.getElementById('xqdm').onchange();

  document.getElementById('bmddm').value = "${data.bmddm ? data.bmddm : ''}";


  document.getElementById('kqxl').value = "${data.kqxl ? data.kqxl : ''}";


  document.getElementById('zydm').value = "${data.zydm ? data.zydm : ''}";


  document.getElementById('byxx').value = "${data.byxx ? data.byxx : ''}";


  document.getElementById('byrq').value = "${data.byrq ? data.byrq : ''}";


  document.getElementById('byzy').value = "${data.byzy ? data.byzy : ''}";

  document.getElementById('byzshm').value = "${data.byzshm ? data.byzshm : ''}";

  document.getElementById('yzbm').value = "${data.yzbm ? data.yzbm : ''}";

  document.getElementById('lxdh').value = "${data.lxdh ? data.lxdh : ''}";

  document.getElementById('txdz').value = "${data.txdz ? data.txdz : ''}";

  document.getElementById('sf').value = "${data.sf || ''}";
  document.getElementById('sf').onchange();
  document.getElementById('ds').value = "${data.ds || ''}";
  document.getElementById('ds').onchange();
  document.getElementById('xq').value = "${data.xq || ''}";

`;

  if (data.kslbdm === '1') {
    fillScript += `


    document.getElementById('zsbpc1bkyx1').value = "${data.zsbpc1bkyx1 ? data.zsbpc1bkyx1 : ''}";
    document.getElementById('zsbpc1bkyx1zy1').value = "${data.zsbpc1bkyx1zy1 ? data.zsbpc1bkyx1zy1 : ''}";
    document.getElementById('zsbpc1bkyx1zy2').value = "${data.zsbpc1bkyx1zy2 ? data.zsbpc1bkyx1zy2 : ''}";



  `;
  }

  if (data.kslbdm === '5') {
    fillScript += `

    document.getElementById('gqgpc4bkyx1').value = "${data.gqgpc4bkyx1 ? data.gqgpc4bkyx1 : ''}";
    document.getElementById('gqgpc4bkyx1zy1').value = "${data.gqgpc4bkyx1zy1 ? data.gqgpc4bkyx1zy1 : ''}";
    document.getElementById('gqgpc4bkyx1zy2').value = "${data.gqgpc4bkyx1zy2 ? data.gqgpc4bkyx1zy2 : ''}";




  `;
  }

  return fillScript;
};
export default downFileScript;
