<template>
    <div>
        <el-dialog :title="title" :visible.sync="show" width="50%" :before-close="handleClose" top="5vh">
            <el-form ref="form" :model="form" label-width="120px" :rules="rules">
                <el-form-item label="发送方式：" prop="sendType">
                    <el-select v-model="form.sendType" placeholder="请选择发送方式" disabled>
                        <el-option label="远智成教君" value="1"></el-option>
                        <el-option label="远智开放君" value="2"></el-option>
                        <el-option label="远智全日制" value="3"></el-option>
                        <el-option label="远智自考君" value="4"></el-option>
                        <el-option label="远智研究生" value="5"></el-option>
                        <!-- <el-option label="远智中专" value="6"></el-option> -->
                    </el-select>
                </el-form-item>
                <el-form-item label="消息类型：" prop="msgType">
                    <el-select v-model="form.msgType" placeholder="消息类型" :disabled="title=='查看详情'?true:false">
                        <el-option label="任务通知" value="1"></el-option>
                        <!-- <el-option label="缴费通知" value="2"></el-option> -->
                        <el-option label="作业提醒" value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="计划发送时间：" prop="sendTime">
                    <el-date-picker v-model="form.sendTime" type="datetime" placeholder="选择日期时间" :disabled="title=='查看详情'?true:false">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="消息名称：" prop="msgName">
                    <el-input v-model.trim="form.msgName" type="textarea" maxlength="20" show-word-limit :disabled="title=='查看详情'?true:false"></el-input>
                </el-form-item>
                <el-form-item label="消息详情：" prop="msgContent">
                    <el-input type="textarea" v-model.trim="form.msgContent" maxlength="20" show-word-limit :disabled="title=='查看详情'?true:false"></el-input>
                </el-form-item>
                <el-form-item v-if="$route.fullPath.indexOf('type=2') == -1">
                    <div>重点注意：</div>
                    <div>1、模板消息仅支持展示前20个字，请提炼重点内容。</div>
                    <div>2、不允许换行，否则影响展示效果。</div>
                </el-form-item>
                <el-form-item label="跳转URL：" prop="url">
                    <el-input v-model="form.url" :disabled="title=='查看详情'?true:false"></el-input>
                </el-form-item>
                <el-form-item v-if="$route.fullPath.indexOf('type=2') == -1">
                    <div>{{form.sendType==1?'成教':form.sendType==2?'国开':form.sendType==3?'全日制':form.sendType==4?'自考':'研究生'}}服务号-【我的任务】链接为https://zm.yzou.cn/student/mytask?recruitType={{ form.sendType }}</div>
                </el-form-item>
                <el-form-item label="备注：" prop="remark">
                    <el-input type="textarea" v-model.trim="form.remark" maxlength="100" show-word-limit :disabled="title=='查看详情'?true:false"></el-input>
                </el-form-item>
                <el-form-item style="text-align: center;" v-if="$route.fullPath.indexOf('type=2') !== -1">
                    <el-button type="primary" @click="remind('确认审核通过？',`（消息目标人数 ${formContent.studentCount}学员）`,)" v-if="btnAdopt">通过</el-button>
                    <el-button @click="reject('form')" v-if="btnReject">驳回</el-button>
                </el-form-item>
                <el-form-item style="text-align: center;" v-if="$route.fullPath.indexOf('type=2') == -1 && title!=='查看详情'">
                    <el-button type="primary" @click="onSubmit('form')">提交</el-button>
                    <el-button @click="cancel('form')">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <el-dialog title="填写驳回原因" :visible.sync="dialogVisible" width="30%" :before-close="handleClose2">
            <el-input type="textarea" v-model="remark" maxlength="30" show-word-limit
                placeholder="请至少输入1个文字说明"></el-input>
            <div style="margin-top:20px;text-align: right;">
                <el-button type="primary" @click="submitClick">提交</el-button>
                <el-button @click="cal()">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment'
export default {
    props: {
        show: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        },
        formContent: {
            type: Object,
            default: () => { }
        }
    },
    watch: {
        show(newVal){
            if(newVal && this.formContent.mpMsgId){
                this.getJurisdiction()
                this.getList()
            }
        },
        title(newVal) {
            console.log(newVal,'newVal');
            this.form = this.formContent
            if (newVal.indexOf('成教') !== -1) {
                this.form.sendType = '1'
            } else if (newVal.indexOf('自考') !== -1) {
                this.form.sendType = '4'
            } else if (newVal.indexOf('国开') !== -1) {
                this.form.sendType = '2'
            } else if (newVal.indexOf('研究生') !== -1) {
                this.form.sendType = '5'
            }
            
        }
    },
    data() {
        return {
            remark: '',
            form: {
                sendType: '',
                msgType: '',
                sendTime: '',
                msgName: '',
                msgContent: '',
                url: '',
                remark: ''
            },
            rules: {
                sendType: [
                    { required: true, message: '请输入发送方式', trigger: 'blur' }
                ],
                msgType: [
                    { required: true, message: '请选择消息类型', trigger: 'blur' }
                ],
                sendTime: [
                    { required: true, message: '请选择计划发送时间', trigger: 'blur' }
                ],
                msgName: [
                    { required: true, message: '请输入消息名称', trigger: 'blur' },
                    { max: 21, message: '消息名称仅支持20个字', trigger: 'change' }
                ],
                msgContent: [
                    { required: true, message: '请输入消息详情', trigger: 'blur' },
                    { max: 21, message: '消消息详情仅支持20个字', trigger: 'change' }
                ],
            },
            dialogVisible: false,
            btnReject:false,
            btnAdopt:false
        }
    },
    methods: {
        getJurisdiction(){
            this.$http.post('/puGoodsInstallment/getPermission')
            .then(res=>{
                let arr  = []
                arr.push(res.body)
                arr.forEach(item=>{
                    item.forEach(items=>{
                        if(items.indexOf('msgManage:approve')!=-1){
                            this.btnAdopt = true
                        }
                        if(items.indexOf('msgManage:abort')!=-1){
                            this.btnReject = true
                        }
                    })
                })
            })
        },        handleClose2(){
            this.dialogVisible = false
        },
        getList() {
            this.$http.get(`/msgManage/detail?mpMsgId=${this.formContent.mpMsgId}`, { json: true }).then(res => {
                if (res.ok) {
                    res.body.msgType = String(res.body.msgType)
                    res.body.sendTime = moment(res.body.sendTime).format('YYYY-MM-DD HH:mm:ss')
                    this.form = res.body
                }
            })
        },
        onSubmit(form) {
            this.$refs[form].validate((valid) => {
                if (valid) {
                    // if(title!=='查看详情'){
                    //     this.form.mpMsgId = 
                    // }
                    this.form.sendTime = moment(this.form.sendTime).format('YYYY-MM-DD HH:mm:ss')
                    this.$http.post(this.form.mpMsgId?'/msgManage/update':'/msgManage/add', this.form, { json: true }).then(res => {
                        if (res.ok) {
                            if(this.form.mpMsgId){
                                this.$message({ type: 'success', message: '编辑成功!' });
                            }else{
                                this.$message({ type: 'success', message: '添加成功!' });

                            }
                            this.$refs[form].resetFields();
                            this.$parent.getMessageConfigurationList()
                            this.$emit('show', false);
                        }
                    })
                }
            });
        },
        remind(text, num, type, key) {
            this.$confirm('', '', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                dangerouslyUseHTMLString: true,
                center: true,
                message: `<div style="font-size:18px"><i class="el-icon-warning" style="color: ${type == 'primary' ? '#1890FF' : '#FAAD14'}"></i> ${text}</div><div>${num}</div>`
            }).then(() => {
                this.adopt()
            }).catch(() => {
                // this.$message({
                //     type: 'info',
                //     message: '清空成功！'
                // });
            });
        },
        adopt() {
            let data = {
                mpMsgId: this.formContent.mpMsgId
            }
            this.$http.post('/msgManage/approve', data, { json: true }).then(res => {
                if (res.ok) {
                    this.$message({ type: 'success', message: '通过成功!' });
                    this.$refs.form.resetFields();
                    this.$parent.getMessageConfigurationList()
                }
            })
            this.$emit('show', false);

        },
        reject() {
            this.dialogVisible = true
        },
        submitClick() {
            if(this.remark==''){
                this.$message({ message: '填写驳回原因!',type: 'warning' });
                return
            }
            let data = {
                mpMsgId: this.formContent.mpMsgId,
                remark: this.remark 
            }
            this.$http.post('/msgManage/abort', data, { json: true }).then(res => {
                if (res.ok) {
                    this.$message({ type: 'success', message: '驳回成功!' });
                    // this.$refs[form].resetFields();
                    this.$parent.getMessageConfigurationList()
                    this.dialogVisible = false
                    this.remark = ''
                    this.$emit('show', false);
                }
            })
        },
        cal(){
            this.dialogVisible = false
            this.remark = ''
        },
        cancel(form) {
            this.$refs[form].resetFields();
            this.$emit('show', false);


        },
        handleClose(done) {
            this.form = {}
            this.$emit('show', false);
            this.$emit('title','')
        }
    }
}
</script>

<style></style>