<template>
  <div class="table" :style="{'min-width': styleWidth}">
    <div class="head">
      <div v-width="100">pk分组</div>
      <div v-width="150">战队</div>
      <div v-width="100">总指挥</div>
      <div v-width="190">部门</div>
      <div v-width="80">分校长</div>
      <div v-width="50">人力</div>
      <div v-if="pkRange.includes('1')" v-width="80">今日成教</div>
      <div v-if="pkRange.includes('1')" v-width="80">活动成教</div>
      <div v-if="pkRange.includes('2')" v-width="80">今日国开</div>
      <div v-if="pkRange.includes('2')" v-width="80">活动国开</div>
      <div v-if="pkRange.includes('3')" v-width="80">今日全日制</div>
      <div v-if="pkRange.includes('3')" v-width="80">活动全日制</div>
      <div v-if="pkRange.includes('4')" v-width="80">今日自考</div>
      <div v-if="pkRange.includes('4')" v-width="80">活动自考</div>
      <div v-if="pkRange.includes('5')" v-width="80">今日研究生</div>
      <div v-if="pkRange.includes('5')" v-width="80">活动研究生</div>
      <div v-if="pkRange.includes('6')" v-width="80">今日职业教育课程</div>
      <div v-if="pkRange.includes('6')" v-width="80">活动职业教育课程</div>
      <div v-if="pkRange.includes('7')" v-width="80">今日海外教育</div>
      <div v-if="pkRange.includes('7')" v-width="80">活动海外教育</div>
      <div v-width="80">今日合计</div>
      <div v-width="80">调整业绩</div>
      <div v-width="80">活动合计</div>
      <div v-for="aim in moreAim" :key="aim.kes" v-width="100" :prop="aim.kes">{{ aim.label }}</div>
      <div v-width="80">{{ pkType | type }}</div>
    </div>
    <div class="body">
      <div v-for="(item, index) in data" :key="'info1-'+index" class="tr">
        <!-- 第一列 -->
        <div v-width="100" class="td" style="border-left: 1px solid #ebeef5;">
          <div class="center pk">pk</div>
        </div>
        <!-- 第二列 -->
        <div>
          <!-- A队 -->
          <div class="flex">
            <div v-width="150" class="td cell" label="战队">
              <div class="center">
                <el-avatar :size="100" :src="item.pkTeamDetailsVOS[0].teamAvatar | formatOssImgUrl ">
                  <img src="../../../../assets/imgs/helpStudyPkRank/defaultPK1.png" />
                </el-avatar>
                <br />
                {{ item.pkTeamDetailsVOS[0].teamName }}
              </div>
            </div>
            <div v-width="100" class="td cell">
              <div class="center">{{ item.pkTeamDetailsVOS[0].empName }}</div>
            </div>
            <div v-width="190" label="部门">
              <div
                v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS"
                :key="force.teamName"
                class="td"
              >
                {{ force.teamName }}
              </div>
            </div>
            <div v-width="80" label="分校长">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.empName + generateKey()" class="td">
                <div class="center">{{ force.empName }}</div>
              </div>
            </div>
            <div v-width="50" label="人力" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.manPower + generateKey()" class="td">
                <div class="center">{{ force.manPower }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('1')" v-width="80" label="今日成教" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.tdCj + generateKey()" class="td">
                <div class="center">{{ force.tdCj }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('1')" v-width="80" label="活动成教" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.actCjView + generateKey()" class="td">
                <div class="center">{{ force.actCjView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('2')" v-width="80" label="今日国开" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.tdGk + generateKey()" class="td">
                <div class="center">{{ force.tdGk }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('2')" v-width="80" label="活动国开" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.actGkView + generateKey()" class="td">
                <div class="center">{{ force.actGkView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('3')" v-width="80" label="今日全日制" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.tdQrz + generateKey()" class="td">
                <div class="center">{{ force.tdQrz }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('3')" v-width="80" label="活动全日制" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.actQrzView + generateKey()" class="td">
                <div class="center">{{ force.actQrzView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('4')" v-width="80" label="今日自考" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.tdZk + generateKey()" class="td">
                <div class="center">{{ force.tdZk }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('4')" v-width="80" label="活动自考" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.actZkView + generateKey()" class="td">
                <div class="center">{{ force.actZkView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('5')" v-width="80" label="今日研究生" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.tdYjs + generateKey()" class="td">
                <div class="center">{{ force.tdYjs }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('5')" v-width="80" label="活动研究生" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.actYjsView + generateKey()" class="td">
                <div class="center">{{ force.actYjsView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('6')" v-width="80" label="今日职业教育课程" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.tdZyjy + generateKey()" class="td">
                <div class="center">{{ force.tdZyjy }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('6')" v-width="80" label="活动职业教育课程" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.actZyjyView + generateKey()" class="td">
                <div class="center">{{ force.actZyjyView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('7')" v-width="80" label="今日海外教育" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.tdHw + generateKey()" class="td">
                <div class="center">{{ force.tdHw }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('7')" v-width="80" label="活动海外教育" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.actHwView + generateKey()" class="td">
                <div class="center">{{ force.actHwView }}</div>
              </div>
            </div>
            <div v-width="80" label="今日合计" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.tdAllJx + generateKey()" class="td">
                <div class="center">{{ force.tdAllJx }}</div>
              </div>
            </div>
            <div v-width="80" label="调整业绩" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.reduceJx + generateKey()" class="td">
                <div class="center">{{ force.reduceJx }}</div>
              </div>
            </div>
            <div v-width="80" label="活动合计" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.actAllJx + generateKey()" class="td">
                <div class="center">{{ force.actAllJx }}</div>
              </div>
            </div>
            <!-- 底线目标 -->
            <div v-for="(aim,i) in moreAim" :key="aim.kes" v-width="100" :label="aim.label" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.manPower + generateKey()" class="td">
                <div class="center">{{ force['bottomLine'+i] }}</div>
              </div>
            </div>
            <div v-width="80" label="活动人均" class="cell">
              <div v-for="force in item.pkTeamDetailsVOS[0].pkTeamDetailsVOS" :key="force.actAllJx + generateKey()" class="td">
                <div class="center">{{ force.pkScore }}</div>
              </div>
            </div>
          </div>
          <div class="flex sum">
            <div v-width="520" class="td">
              <div class="center">合计</div>
            </div>
            <div v-width="50" class="td" label="人力">
              <div class="center">{{ item.pkTeamDetailsVOS[0].manPower }}</div>
            </div>
            <div v-if="pkRange.includes('1')" v-width="80" class="td" label="今日成教">
              <div class="center">{{ item.pkTeamDetailsVOS[0].tdCj }}</div>
            </div>
            <div v-if="pkRange.includes('1')" v-width="80" class="td" label="活动成教">
              <div class="center">{{ item.pkTeamDetailsVOS[0].actCjView }}</div>
            </div>
            <div v-if="pkRange.includes('2')" v-width="80" class="td" label="今日国开">
              <div class="center">{{ item.pkTeamDetailsVOS[0].tdGk }}</div>
            </div>
            <div v-if="pkRange.includes('2')" v-width="80" class="td" label="活动国开">
              <div class="center">{{ item.pkTeamDetailsVOS[0].actGkView }}</div>
            </div>
            <div v-if="pkRange.includes('3')" v-width="80" class="td" label="今日全日制">
              <div class="center">{{ item.pkTeamDetailsVOS[0].tdQrz }}</div>
            </div>
            <div v-if="pkRange.includes('3')" v-width="80" class="td" label="活动全日制">
              <div class="center">{{ item.pkTeamDetailsVOS[0].actQrzView }}</div>
            </div>
            <div v-if="pkRange.includes('4')" v-width="80" class="td" label="今日自考">
              <div class="center">{{ item.pkTeamDetailsVOS[0].tdZk }}</div>
            </div>
            <div v-if="pkRange.includes('4')" v-width="80" class="td" label="活动自考">
              <div class="center">{{ item.pkTeamDetailsVOS[0].actZkView }}</div>
            </div>
            <div v-if="pkRange.includes('5')" v-width="80" class="td" label="今日研究生">
              <div class="center">{{ item.pkTeamDetailsVOS[0].tdYjs }}</div>
            </div>
            <div v-if="pkRange.includes('5')" v-width="80" class="td" label="活动研究生">
              <div class="center">{{ item.pkTeamDetailsVOS[0].actYjsView }}</div>
            </div>
            <div v-if="pkRange.includes('6')" v-width="80" class="td" label="今日职业教育课程">
              <div class="center">{{ item.pkTeamDetailsVOS[0].tdZyjy }}</div>
            </div>
            <div v-if="pkRange.includes('6')" v-width="80" class="td" label="活动职业教育课程">
              <div class="center">{{ item.pkTeamDetailsVOS[0].actZyjyView }}</div>
            </div>
            <div v-if="pkRange.includes('7')" v-width="80" class="td" label="今日海外教育">
              <div class="center">{{ item.pkTeamDetailsVOS[0].tdHw }}</div>
            </div>
            <div v-if="pkRange.includes('7')" v-width="80" class="td" label="活动海外教育">
              <div class="center">{{ item.pkTeamDetailsVOS[0].actHwView }}</div>
            </div>
            <div v-width="80" class="td" label="今日合计">
              <div class="center">{{ item.pkTeamDetailsVOS[0].tdAllJx }}</div>
            </div>
            <div v-width="80" class="td" label="调整业绩">
              <div class="center">{{ item.pkTeamDetailsVOS[0].reduceJx }}</div>
            </div>
            <div v-width="80" class="td" label="活动合计">
              <div class="center">{{ item.pkTeamDetailsVOS[0].actAllJx }}</div>
            </div>
            <!-- 底线目标 -->
            <div v-for="(aim,i) in moreAim" :key="aim.kes" v-width="100" :label="aim.label" class="td">
              <div class="center">{{ item.pkTeamDetailsVOS[0]['bottomLine'+i] }}</div>
            </div>
            <div v-width="80" class="td" :label="pkType | type">
              <div class="center">{{ item.pkTeamDetailsVOS[0].pkScore }}</div>
            </div>
          </div>
          <!-- B队 -->
          <div class="flex">
            <div v-width="150" class="td" label="战队">
              <div class="center">
                <el-avatar :size="100" :src="item.pkTeamDetailsVOS[1].teamAvatar | formatOssImgUrl">
                  <img src="../../../../assets/imgs/helpStudyPkRank/defaultPK2.png" />
                </el-avatar>
                <br />
                {{ item.pkTeamDetailsVOS[1].teamName }}
              </div>
            </div>
            <div v-width="100" class="td">
              <div class="center">{{ item.pkTeamDetailsVOS[1].empName }}</div>
            </div>
            <div v-width="190" label="部门">
              <div
                v-for="force in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS"
                :key="force.teamName"
                class="td"
              >
                {{ force.teamName }}
              </div>
            </div>
            <div v-width="80" label="分校长" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info0-'+i" class="td">
                <div class="center">{{ force.empName }}</div>
              </div>
            </div>
            <div v-width="50" label="人力" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info3-'+i" class="td">
                <div class="center">{{ force.manPower }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('1')" v-width="80" label="今日成教" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info4-'+i" class="td">
                <div class="center">{{ force.tdCj }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('1')" v-width="80" label="活动成教" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info5-'+i" class="td">
                <div class="center">{{ force.actCjView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('2')" v-width="80" label="今日国开" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info6-'+i" class="td">
                <div class="center">{{ force.tdGk }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('2')" v-width="80" label="活动国开" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info7-'+i" class="td">
                <div class="center">{{ force.actGkView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('3')" v-width="80" label="今日全日制" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info8-'+i" class="td">
                <div class="center">{{ force.tdQrz }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('3')" v-width="80" label="活动全日制" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info9-'+i" class="td">
                <div class="center">{{ force.actQrzView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('4')" v-width="80" label="今日自考" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info10-'+i" class="td">
                <div class="center">{{ force.tdZk }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('4')" v-width="80" label="活动自考" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info11-'+i" class="td">
                <div class="center">{{ force.actZkView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('5')" v-width="80" label="今日研究生" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info12-'+i" class="td">
                <div class="center">{{ force.tdYjs }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('5')" v-width="80" label="活动研究生" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info13-'+i" class="td">
                <div class="center">{{ force.actYjsView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('6')" v-width="80" label="今日职业教育课程" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info12-'+i" class="td">
                <div class="center">{{ force.tdZyjy }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('6')" v-width="80" label="活动职业教育课程" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info13-'+i" class="td">
                <div class="center">{{ force.actZyjyView }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('7')" v-width="80" label="今日海外教育" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info12-'+i" class="td">
                <div class="center">{{ force.tdHw }}</div>
              </div>
            </div>
            <div v-if="pkRange.includes('7')" v-width="80" label="活动海外教育" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info13-'+i" class="td">
                <div class="center">{{ force.actHwView }}</div>
              </div>
            </div>
            <div v-width="80" label="今日合计" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info14-'+i" class="td">
                <div class="center">{{ force.tdAllJx }}</div>
              </div>
            </div>
            <div v-width="80" label="调整业绩" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info15-'+i" class="td">
                <div class="center">{{ force.reduceJx }}</div>
              </div>
            </div>
            <div v-width="80" label="活动合计" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info16-'+i" class="td">
                <div class="center">{{ force.actAllJx }}</div>
              </div>
            </div>
            <!-- 底线目标 -->
            <div v-for="(aim,i) in moreAim" :key="aim.kes" v-width="100" :label="aim.label" class="cell">
              <div v-for="(force,j) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info17-'+j" class="td">
                <div class="center">{{ force['bottomLine'+i] }}</div>
              </div>
            </div>
            <div v-width="80" :label="pkType | type" class="cell">
              <div v-for="(force, i) in item.pkTeamDetailsVOS[1].pkTeamDetailsVOS" :key="'info18-'+i" class="td">
                <div class="center">{{ force.pkScore }}</div>
              </div>
            </div>
          </div>
          <div class="flex sum">
            <div v-width="520" class="td">
              <div class="center">合计</div>
            </div>
            <div v-width="50" class="td" label="人力">
              <div class="center">{{ item.pkTeamDetailsVOS[1].manPower }}</div>
            </div>
            <div v-if="pkRange.includes('1')" v-width="80" class="td" label="今日成教">
              <div class="center">{{ item.pkTeamDetailsVOS[1].tdCj }}</div>
            </div>
            <div v-if="pkRange.includes('1')" v-width="80" class="td" label="活动成教">
              <div class="center">{{ item.pkTeamDetailsVOS[1].actCjView }}</div>
            </div>
            <div v-if="pkRange.includes('2')" v-width="80" class="td" label="今日国开">
              <div class="center">{{ item.pkTeamDetailsVOS[1].tdGk }}</div>
            </div>
            <div v-if="pkRange.includes('2')" v-width="80" class="td" label="活动国开">
              <div class="center">{{ item.pkTeamDetailsVOS[1].actGkView }}</div>
            </div>
            <div v-if="pkRange.includes('3')" v-width="80" class="td" label="今日全日制">
              <div class="center">{{ item.pkTeamDetailsVOS[1].tdQrz }}</div>
            </div>
            <div v-if="pkRange.includes('3')" v-width="80" class="td" label="活动全日制">
              <div class="center">{{ item.pkTeamDetailsVOS[1].actQrzView }}</div>
            </div>
            <div v-if="pkRange.includes('4')" v-width="80" class="td" label="今日自考">
              <div class="center">{{ item.pkTeamDetailsVOS[1].tdZk }}</div>
            </div>
            <div v-if="pkRange.includes('4')" v-width="80" class="td" label="活动自考">
              <div class="center">{{ item.pkTeamDetailsVOS[1].actZkView }}</div>
            </div>
            <div v-if="pkRange.includes('5')" v-width="80" class="td" label="今日研究生">
              <div class="center">{{ item.pkTeamDetailsVOS[1].tdYjs }}</div>
            </div>
            <div v-if="pkRange.includes('5')" v-width="80" class="td" label="活动研究生">
              <div class="center">{{ item.pkTeamDetailsVOS[1].actYjsView }}</div>
            </div>
            <div v-if="pkRange.includes('6')" v-width="80" class="td" label="今日职业教育课程">
              <div class="center">{{ item.pkTeamDetailsVOS[1].tdZyjy }}</div>
            </div>
            <div v-if="pkRange.includes('6')" v-width="80" class="td" label="活动职业教育课程">
              <div class="center">{{ item.pkTeamDetailsVOS[1].actZyjyView }}</div>
            </div>
            <div v-if="pkRange.includes('7')" v-width="80" class="td" label="今日海外教育">
              <div class="center">{{ item.pkTeamDetailsVOS[1].tdHw }}</div>
            </div>
            <div v-if="pkRange.includes('7')" v-width="80" class="td" label="活动海外教育">
              <div class="center">{{ item.pkTeamDetailsVOS[1].actHwView }}</div>
            </div>
            <div v-width="80" class="td" label="今日合计">
              <div class="center">{{ item.pkTeamDetailsVOS[1].tdAllJx }}</div>
            </div>
            <div v-width="80" class="td" label="调整业绩">
              <div class="center">{{ item.pkTeamDetailsVOS[1].reduceJx }}</div>
            </div>
            <div v-width="80" class="td" label="活动合计">
              <div class="center">{{ item.pkTeamDetailsVOS[1].actAllJx }}</div>
            </div>
            <!-- 底线目标 -->
            <div v-for="(aim, i) in moreAim" :key="aim.kes" v-width="100" :label="aim.label" class="td">
              <div class="center">{{ item.pkTeamDetailsVOS[1]['bottomLine'+i] }}</div>
            </div>
            <div v-width="80" class="td" :label="pkType | type">
              <div class="center">{{ item.pkTeamDetailsVOS[1].pkScore }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { uuid } from '@/utils';
export default {
  components: {},
  filters: {
    type(val) {
      if (!val) return;
      const data = {
        1: '助学积分',
        2: '助学人数',
        3: '活动人均'
      };
      return data[val];
    }
  },
  props: {
    data: { type: Array, default: () => [] },
    moreAim: { type: Array, default: () => [] },
    pkType: { type: Number, default: 1 },
    pkRange: { type: Array, default: () => [] },
    type: { type: [Number, String], default: 0 }
  },
  data() {
    return {};
  },
  computed: {
    styleWidth() {
      const les = this.moreAim.length + this.pkRange.length;
      return 1080 + (les * 120) + 'px';
    }
  },
  watch: {
    pkRange(val) {
      this.pkRange = val;
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    generateKey() {
      return uuid();
    }
  }
};
</script>

<style lang='scss' scoped>
.table {
  min-width: 1020px;
  .head {
    width: max-content;
    border: 1px solid #ebeef5;
    display: flex;
    div {
      color: #909399;
      font-size: 14px;
      text-align: center;
      padding: 12px 0;
      border-right: 1px solid #ebeef5;
    }

    div:last-child {
      border-right: none;
    }
  }
  .body {
    min-height: 44px;
    .tr {
      display: flex;
      .td {
        width: 100%;
        padding: 12px 0;
        min-height: 44px;
        text-align: center;
        border: 1px solid #ebeef5;
        display: table;
        flex-shrink: 0;
      }
    }
  }
}

.pk {
  font-size: 28px;
  color: #FF6699;
}

.flex {
  display: flex;

  .cell {
    &:last-child {
      border-right: 1px solid #ebeef5;
    }
  }

}

.padding {
  padding: 12px 0;
}

.center {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}
.sum{
  color: #ff6699;
}
.teamAvatar{
  width: 100px;
  height: 100px;
}
::v-deep .el-avatar--circle{
  border-radius: 0;
}
::v-deep .el-avatar{
  background: #fff;
  img{
    background: #fff;
    width: 100%;
    height: 100%;
  }
}
</style>
