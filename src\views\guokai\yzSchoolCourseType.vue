<template>
  <div class="preview-main">
    <div class="preview-header">
      <img src="../../assets/imgs/guokai/moblie-head.png" alt="" />
    </div>
    <div class="preview-content">
      <h3>{{ courseInfo.customGuidanceTitle }}</h3>
      <div v-html="courseInfo.customGuidanceContent"></div>
    </div>
    <div class="preview-bot">
      <div v-if="courseInfo.gotoThirdTeachPlatform" class="preview-btn" @click="toThirdTeachVidsUrl">进入网站学习</div>
      <img src="../../assets/imgs/guokai/moblie-bot.png" alt="" />
    </div>
    <!-- <i class="el-icon-circle-close" @click="isShowPreview = false" /> -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      courseInfo: {}
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      const params = {
        cpId: this.$route.query.cpId,
        recruitType: this.$route.query.recruitType,
        cpType: this.$route.query.cpType
      };
      this.$post('getTeachTypeConf', params).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.courseInfo = body;
          console.log(body, 'body');
          console.log(this.courseInfo, 'courseInfo');
        }
      });
    },
    toThirdTeachVidsUrl() {
      window.open(this.courseInfo.thirdTeachVidsUrl);
    }
  }
};
</script>

<style lang='scss' scoped>
::v-deep html{
  font-size: 100px !important;
}
  .preview-main{
    margin: 120px auto;
    width: 375px;
    position: relative;
    border-radius: 8px;
    height: 600px;
    .preview-header{
      width: 100%;
      height: 85px;
      img{
        width: 100%;
        height: 85px;
      }
    }
    .preview-bot{
      position: absolute;
      // bottom: 20%;
      // width: 100%;
      width: 375px;
      .preview-btn{
        border-top: 5px solid #FFFFFF;
        cursor: pointer;
        height: 50px;
        line-height: 45px;
        background: linear-gradient(135deg, #F09190 0%, #F07877 66%, #F06E6C 100%);
        font-size: 15px;
        text-align: center;
        font-weight: 600;
        color: #FFFFFF;
      }
      img{
        width: 100%;
        height: 35px;
      }
    }
    .preview-content{
      padding:0px 16px 22px;
      overflow: auto;
      // overflow-y: scroll;
      width: 375px;
      height: 435px;
      background-color: #fff;
      h3{
        margin: 16px 0 22px;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        color: #333333;
      }
    }
    i{
      cursor: pointer;
      font-size: 30px;
      color: #ddd;
      position:absolute;
      bottom: -45px;
      left: 50%;
      transform: translate(-50%);
    }
  }
</style>
