<template>
    <div>
        <el-dialog :title="title" :visible.sync="show" width="90%" :before-close="handleClose">
            <el-form ref="form" :model="form" label-width="120px" inline>
                <el-form-item label="学业编码：" prop="learnId">
                    <el-input v-model="form.learnId" placeholder="输入学业编码" style="width:187px;"></el-input>
                </el-form-item>
                <el-form-item label="远智编码：" prop="yzCode">
                    <el-input v-model="form.yzCode" placeholder="输入远智编码" style="width:187px;"></el-input>
                </el-form-item>
                <el-form-item label="学服任务" prop="taskId">
                    <el-select v-model="form.taskId" placeholder="请选择" style="width:187px;" filterable clearable :filter-method="getTaskInfoByAllow" @change="changeSelect">
                        <el-option v-for="task in taskInfoByAllowList" :key="task.task_id" :label="task.task_title"
                            :value="task.task_id"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="是否完成：" prop="isDone">
                    <el-select v-model="form.isDone" placeholder="请选择" style="width:187px;" clearable :disabled="disabledSelect">
                        <el-option label="是" value="1"></el-option>
                        <el-option label="否" value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否选中：" prop="isSelected">
                    <el-select v-model="form.isSelected" placeholder="请选择" style="width:187px;" clearable>
                        <el-option label="是" value="true"></el-option>
                        <el-option label="否" value="false"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="报考层次：" prop="pfsnLevel">
                    <el-select v-model="form.pfsnLevel" placeholder="请选择" style="width:187px;">
                        <el-option v-for="pfsnLevel in pfsLevelList" :key="pfsnLevel.dictValue" :label="pfsnLevel.dictName"
                            :value="pfsnLevel.dictValue"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="年级：" prop="grade" >
                    <el-select v-model="form.grade" placeholder="请选择" style="width:187px;" filterable clearable>
                        <el-option v-for="year in grade" :key="year.dictValue" :label="year.dictName"
                            :value="year.dictValue"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="院校：" prop="unvsId">
                    <el-select v-model="form.unvsId" placeholder="请选择" style="width:187px;" @change="handleUnvsChange" filterable clearable>
                        <el-option v-for="item in unvsList" :key="item.unvsId" :label="item.unvsName"
                            :value="item.unvsId"></el-option>
                    </el-select>
                </el-form-item>
                <template v-if="formList">
                    <el-form-item label="专业：" prop="pfsnId">
                        <el-select v-model="form.pfsnId" placeholder="请选择" style="width:187px;"
                            @change="handlePfsnChange" filterable clearable>
                            <el-option v-for="item in pfsnList" :key="item.pfsnId" :label="[item.taCode] + item.pfsnName"
                                :value="item.pfsnId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="考区：" prop="taId">
                        <el-select v-model="form.taId" placeholder="请选择" style="width:187px;" filterable clearable>
                            <el-option v-for="item in taList" :key="item.taId" :label="item.taName"
                                :value="item.taId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="学员姓名：" prop="lnStdName">
                        <el-input v-model="form.lnStdName" placeholder="输入学员姓名" style="width:187px;"></el-input>
                    </el-form-item>
                    <el-form-item label="证件号码：" prop="idCard">
                        <el-input v-model="form.idCard" placeholder="输入证件号码" style="width:187px;"></el-input>
                    </el-form-item>
                    <el-form-item label="手机号：" prop="mobile">
                        <el-input v-model="form.mobile" placeholder="输入手机号" style="width:187px;"></el-input>
                    </el-form-item>
                    <el-form-item label="班主任：" prop="tutorName">
                        <el-input v-model="form.tutorName" placeholder="输入班主任姓名" style="width:187px;"></el-input>
                    </el-form-item>
                    <el-form-item label="招生类型：">
                        <el-select v-model="form.recruitType" placeholder="请选择" style="width:187px;" disabled>
                            <el-option v-for="item in recruitTypeList" :key="item.dictValue" :label="item.dictName"
                            :value="item.dictValue"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="优惠类型：" prop="scholarship">
                        <el-select v-model="form.scholarship" placeholder="请选择" style="width:187px;" filterable clearable>
                            <el-option v-for="item in scholarshipList" :key="item.dictValue" :label="item.dictName"
                            :value="item.dictValue"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="招生分校：" prop="recruitCampus">
                        <el-select v-model="form.recruitCampus" placeholder="请选择" style="width:187px;" @change="handleCampusChange" filterable clearable>
                            <el-option v-for="item in campusList" :key="item.campusId" :label="item.campusName"
                            :value="item.campusId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="招生部门：" prop="recruitDepartment">
                        <el-select v-model="form.recruitDepartment" placeholder="请选择" style="width:187px;" @change="handleCampusChange" filterable clearable>
                            <el-option v-for="item in departmentInfo" :key="item.dpId" :label="item.dpName" :value="item.dpId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="招生老师：" prop="recruitName">
                        <el-input v-model="form.recruitName" placeholder="输入招生老师姓名" style="width:187px;"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="缴费时间起：" prop="startPayTime">
                        <el-date-picker v-model="form.startPayTime" type="date" placeholder="选择日期"
                            style="width:187px;"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="缴费时间止：" prop="endPayTime">
                        <el-date-picker v-model="form.endPayTime" type="date" placeholder="选择日期"
                            style="width:187px;"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="是否缴费：" prop="examPayStatus">
                        <el-select v-model="form.examPayStatus" placeholder="请选择" style="width:187px;">
                            <el-option label="是" value="true"></el-option>
                            <el-option label="否" value="false"></el-option>
                        </el-select>
                    </el-form-item> -->
                    <el-form-item label="报读时间起：" prop="enrollStartTime">
                        <el-date-picker v-model="form.enrollStartTime" type="date" placeholder="选择日期"
                            style="width:187px;" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="报读时间止：" prop="enrollEndTime">
                        <el-date-picker v-model="form.enrollEndTime" type="date" placeholder="选择日期"
                            style="width:187px;" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="学员状态：" prop="stdStages">
                        <el-select v-model="form.stdStages" placeholder="请选择" style="width:220px;" multiple>
                                <el-option v-for="item in studentStatusList" :key="item.dictValue" :label="item.dictName"
                            :value="item.dictValue"></el-option>

                        </el-select>
                    </el-form-item>
                </template>
                <el-form-item>
                    <el-button @click="formList = !formList">{{ formList ? '收起' : '展开' }}</el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                    <el-button type="primary" @click="search(1)">搜索</el-button>
                    <el-button @click="search()">重置</el-button>
                </el-form-item>
                <!-- <div>
                    <el-form-item label="学员状态：">
                        <el-checkbox-group v-model="form.type">
                            <el-checkbox :label="item" :name="item" v-for="(item, index) in studentStatusList"
                                :key="index">{{ item
                                }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>

                </div> -->
            </el-form>
            <div class="filter-btn">
                <div>正在勾选 {{ multipleSelection.length }} 条记录</div>
                <div>
                    <span>共添加选中 {{ selectedCount }} 学员</span>
                    <span >
                        <el-button @click="btnClick(item.key)" v-for="item in btnList" :key="item.key" :type="item.type" :disabled="disabledShow">{{
            item.name }}</el-button>
                    </span>
                </div>
            </div>

            <!-- 表格部分 -->
            <el-table :data="tableData" style="width: 100%" border @selection-change="handleSelectionChange"  v-loading="loading">
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column prop="isSelected" label="是否选中" align="center">
                    <template #default="{ row }">
                        {{ row.selected ? '是' : '否' }}
                    </template>
                </el-table-column>
                <el-table-column prop="learnId" label="学业编码" align="center"></el-table-column>
                <el-table-column prop="yzCode" label="远智编码" align="center"></el-table-column>
                <el-table-column prop="lnStdName" label="学员姓名" align="center"></el-table-column>
                <el-table-column prop="idCard" label="身份证" align="center"></el-table-column>
                <el-table-column prop="gradeName" label="年级" align="center"></el-table-column>
                <el-table-column prop="recruitType" label="招生类型" align="center">
                    <template #default="{ row }">
                        {{ row.recruitType==1?'成教':row.recruitType==2?'国家开放大学':row.recruitType==3?'全日制':row.recruitType==4?'自考':row.recruitType==5?'硕士研究生':row.recruitType==6?'中专':'国际项目' }}
                    </template>
                </el-table-column>
                <el-table-column prop="pfsnName" label="院校专业" align="center" width="350">
                    <template #default="{row}">
                    {{ (row.unvsName || '') + ' ' + (row.pfsnLevelName || '') + ' ' + (row.pfsnName || '')}}
                </template>
                </el-table-column>
                <el-table-column prop="stdStageName" label="学员阶段" align="center"></el-table-column>
            </el-table>
            <div class="block">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page.sync="form.pageNum" layout="prev, pager, next, sizes,jumper"  :page-sizes="[10, 20, 30, 40]"
                    :total="page.total">
                </el-pagination>
            </div>

        </el-dialog>

        <taskImportPop :showInfo="showInfo" @showInfo="showInfo=false" :id="sendTypePop.mpMsgId"></taskImportPop>
    </div>
</template>
<script>
import taskImportPop from './ImportStudents.vue'
import moment from 'moment';
export default {
    components: { taskImportPop },
    props: {
        show: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        },
        sendTypePop: {
            type: Object,
            default: () => { }
        },
        stateType:{
            type:String,
            default:'edit'
        }
    },
    data() {
        return {
            loading: true,
            studentStatusList: [],
            formList: false,
            latestGrades: ['2023', '2024', '2025'], // 假设这是最新的年级选项
            grade: [],
            unvsList: [],
            pfsnList: [],
            taList: [],
            recruitTypeList:[],
            scholarshipList:[],
            recruitCampusList:[],
            departmentInfo:[],
            campusList:[],
            pfsLevelList:[],
            form: {
                learnId: '',
                yzCode: '',
                isSelected: 'false',
                pfsnLevel: '',
                grade: '',
                unvsId: '',
                pfsnId: '',
                taId: '',
                lnStdName: '',
                idCard: '',
                mobile: '',
                tutorName: '',
                scholarship: '',
                recruitType: '',
                recruitCampus: '',
                recruitDepartment: '',
                recruitName: '',
                // startPayTime: '',
                // endPayTime: '',
                // examPayStatus: '',
                enrollEndTime: '',
                examinationId: '',
                taskId: '',
                stdStages: [],
                pageNum:1,
                pageSize:10,
                mpMsgId:'',
                isDone:''
            },
            tableData: [],
            multipleSelection: [],
            page: {
                total: 0,
                pageNum: 1,
                pageSize: 10
            },
            taskInfoByAllowList: [],
            btnList: [
                { name: '添加选中', key: '1' ,type:'primary' },
                { name: '清空选中', key: '2' ,type:'danger'},
                { name: '按搜索结果添加全部', key: '3',type:'primary' },
                { name: '按搜索结果清空全部', key: '4' ,type:'danger'},
                { name: '导入学员', key: '5',type:'primary' }
            ],
            gradeList: [],
            showInfo: false,
            selectedCount:0,
            disabledShow:false,
            disabledSelect:true,
        };
    },
    watch: {
        sendTypePop(val) {
            if (val) {
                this.page.pageSize = 10
                this.form.pageNum=1
                this.form.recruitType = String(val.sendType)
                this.getTaskImportList()
                this.getSelectedCount()
            }
        },
        stateType(newVal){
            if(newVal == 'edit'){
                this.form.isSelected = 'false'
            }else{
                this.form.isSelected = 'true'
            }

        }
    },
    created() {
        this.studentStatusList = this.$dictJson.stdStage;
        this.pfsLevelList = this.$dictJson.pfsnLevel;
        this.recruitCampusList = this.$dictJson.recruitCampus;
        this.scholarshipList = this.$dictJson.scholarship;
        this.recruitTypeList = this.$dictJson.recruitType;
        this.grade = this.$dictJson.grade;
        this.getTaskInfoByAllow()
        // this.getGradeList()
        this.getUnvsId()
        this.getCampusList()
        if(this.$route.fullPath.indexOf('type=2') !== -1 || this.$route.fullPath.indexOf('type=3') !== -1){
            this.disabledShow = true
        }else{
            this.disabledShow = false
        }
    },
    methods: {
        changeSelect(e){
            if(e){
                this.disabledSelect = false
            }else{
                this.form.isDone = ''
                this.disabledSelect = true
            }
        },
        getSelectedCount(){
            this.$http.get(`/msgManage/selectedCount?mpMsgId=${this.sendTypePop.mpMsgId}`,{json:true}).then(res=>{
                if(res.ok){
                    this.selectedCount = res.body
                }
            })
        },
        search(key) {
            this.page.pageSize = 10
            this.form.pageNum=1
            if(key){
                this.getTaskImportList()
            }else{

                // this.$refs[form].resetFields();
                this.$refs.form.resetFields()
                this.getTaskImportList()
            }
        },
        getTaskImportList() {
            this.loading = true
//             this.form.enrollStartTime = this.form.enrollStartTime ? moment(this.form.enrollStartTime).format('YYYY-MM-DD HH:mm:ss') : '',
//             this.form.enrollEndTime =  this.form.enrollEndTime ? moment(this.form.enrollEndTime).format('YYYY-MM-DD HH:mm:ss') : '',
            this.form.mpMsgId = this.sendTypePop.mpMsgId
            // let data = {
            //     mpMsgId: this.sendTypePop.mpMsgId,
            //     learnId: this.mpMsgId.learnId,
            //     yzCode: this.form.yzCode,
            //     isSelected: this.form.isSelected,
            //     taskId: this.form.taskId,
            //     grade: this.form.grade,
            //     grade: this.form.grade,
            //     recruitType: this.form.recruitType,
            //     pageNum: this.page.pageNum,
            //     pageSize: this.page.pageSize
            // }
            this.$http.post('/msgManage/findStudentList', this.form, { json: true }).then(res => {
                if (res.ok) {
                    res.body.data.forEach(item=>{
                        this.studentStatusList.forEach(val=>{
                            if(item.stdStage == val.dictValue){
                                item.stdStageName = val.dictName
                            }

                        })
                        this.pfsLevelList.forEach(val=>{
                            if(item.pfsnLevel == val.dictValue){
                                item.pfsnLevelName = val.dictName
                            }
                        })
                        item.gradeName = this.grade.find(v => v.dictValue == item.grade)?.dictName ?? item.grade
                    })
                    this.tableData = res.body.data;
                    this.page.total = res.body.recordsTotal
                    this.loading = false;
                }
            });
        },
        // 获取年级列表
        getGradeList() {
            this.$post('getDictInfoList', { name: 'grade' }).then((res) => {
                const { fail, body } = res;
                if (!fail) {
                    this.gradeList = body;
                }
            });
        },
        getTaskInfoByAllow(name) {
            // let params = {
            //     page: 1,
            //     rows: 10000,
            //     sName:name
            // }
            this.$http.get(`/studyActivity/findAllTaskInfoByAllow.do?page=1&rows=100&sName=${name||''}`)
                .then((res) => {
                    if (res.ok) {
                        this.taskInfoByAllowList = res.body.data;
                    }
                })
        },
        handleClose() {
            this.$refs.form.resetFields()
            this.$emit('show', false);
        },
        handleSubmit() {
            this.page.pageNum = 1
            this.getTaskImportList()
        },
        handleReset() {
            this.form.learnId = ''
            this.form.yzCode = ''
            this.form.isSelected = ''
            this.form.taskId = ''
            this.form.grade = ''
            this.getTaskImportList()
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        remind(text, num, type, key) {
            this.$confirm('', '', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                dangerouslyUseHTMLString: true,
                center: true,
                message: `<div style="font-size:18px"><i class="el-icon-warning" style="color: ${type == 'primary' ? '#1890FF' : '#FAAD14'}"></i> ${text}</div><div>${num}</div>`
            }).then(() => {
                this.loading = true
                if (key == 1) {
                    this.addStudent()
                } else if (key == 2) {
                    this.delStudent()

                } else if (key == 3) {
                    this.screenAddStudent()

                } else if (key == 4) {
                    this.screenDelStudent()
                }
            }).catch(() => {
            });
        },

        btnClick(key) {
            if (this.multipleSelection.length == 0 && key != 5 && key != 3 &&key != 4) {
                this.$message({ message: key == 1 || key == 3 ? '请先勾选需要添加的学员' : '请先勾选需要取消选中的学员', type: 'warning' });
                return
            }
            if (key === '1') {
                this.remind(`确认要添加已勾选的学员吗？`, `（已勾选 ${this.multipleSelection.length}学员）`, 'primary', key)
            } else if (key === '2') {
                this.remind('确认要清空已勾选的学员吗？', `（共清空  ${this.multipleSelection.length}学员）`, 'warning', key)
            } else if (key === '3') {
                this.remind('确认要添加搜索结果中的所有学员吗？', `（搜索结果共有 ${this.page.total}学员）`, 'primary', key)
            } else if (key === '4') {
                this.remind('确认要清空搜索结果中的所有学员吗？', `（共清空 ${this.page.total}学员）`, 'warning', key)
            } else {
                this.showInfo = true
            }
        },
        addStudent() {
            let arr = []
            this.multipleSelection.forEach(item => {
                arr.push(item.learnId)
            })
            let data = {
                mpMsgId: this.sendTypePop.mpMsgId,
                learnIdList: arr
            }
            this.$http.post('/msgManage/addSelected', data, { json: true }).then((res) => {
                if (res.ok) {
                    this.$message({ type: 'success', message: `添加成功！` });
                    this.getSelectedCount()
                    this.getTaskImportList()
                    this.$parent.getMessageConfigurationList()

                }else{
                    this.loading = false
                }
            })
        },
        screenAddStudent() {
            // let data = {
            //     mpMsgId: this.sendTypePop.mpMsgId,
            //     learnId: this.form.learnId,
            //     yzCode: this.form.yzCode,
            //     isSelected: this.form.isSelected,
            //     taskId: this.form.taskId,
            //     grade: this.form.grade,
            //     recruitType: this.form.recruitType,
            // }
            this.$http.post('/msgManage/addBySearchCondition',  this.form, { json: true }).then((res) => {
                if (res.ok) {
                    this.$message({ type: 'success', message: `添加成功！` });
                    this.getSelectedCount()
                    this.getTaskImportList()
                    this.$parent.getMessageConfigurationList()

                }else{
                    this.loading = false
                }
            })
        },
        delStudent() {
            let arr = []
            this.multipleSelection.forEach(item => {
                arr.push(item.learnId)
            })
            let data = {
                mpMsgId: this.sendTypePop.mpMsgId,
                learnIdList: arr
            }
            this.$http.post('/msgManage/delSelected', data, { json: true }).then((res) => {
                if (res.ok) {
                    this.$message({ type: 'success', message: `清空成功！` });
                    this.getSelectedCount()
                    this.getTaskImportList()
                    this.$parent.getMessageConfigurationList()

                }else{
                    this.loading = false
                }
            })
        },
        screenDelStudent() {
            // let data = {
            //     mpMsgId: this.sendTypePop.mpMsgId,
            //     learnId: this.form.learnId,
            //     yzCode: this.form.yzCode,
            //     isSelected: this.form.isSelected,
            //     taskId: this.form.taskId,
            //     grade: this.form.grade,
            //     recruitType: this.form.recruitType,
            // }
            this.$http.post('/msgManage/delBySearchCondition',  this.form, { json: true }).then((res) => {
                if (res.ok) {
                    this.$message({ type: 'success', message: `清空成功！` });
                    this.getSelectedCount()
                    this.getTaskImportList()
                    this.$parent.getMessageConfigurationList()
                }else{
                    this.loading = false
                }
            })
        },
        handleSizeChange(val) {
            this.loading = true;
            this.form.pageSize = val
            this.getTaskImportList()
        },
        handleCurrentChange(val) {
            this.loading = true;
            this.form.pageNum = val
            this.getTaskImportList()
        },
        handleUnvsChange(key) {
            if (key) {
                this.getPfsnId()
            }
        },
        handlePfsnChange(key) {
            if (key) {
                this.getTaId()
            }
        },
        handleCampusChange(key){
            this.departmentList(key)
        },
        getUnvsId() {
            this.$post('getUnvsList', { page: 1, rows: 999 }).then((res) => {
                const { fail, body } = res;
                if (!fail) {
                    this.unvsList = body.data;
                }
            });
        },
        getPfsnId() {
            this.$post('getPfsnsList', { page: 1, rows: 999, sId: this.form.unvsId }).then((res) => {
                const { fail, body } = res;
                if (!fail) {
                    this.pfsnList = body.data;
                }
            });
        },
        getTaId() {
            this.$post('getSTaList', { page: 1, rows: 999 }).then((res) => {
                const { fail, body } = res;
                if (!fail) {
                    this.taList = body.data;
                }
            });
        },
        getCampusList() {
            this.$post('getCampusList').then((res) => {
            if (res.code === '00') {
                this.campusList = res.body;
            }
            });
        },
        departmentList(value) {
            const params = {
                // campusName: this.form.campusName
                campusId: value
            };
            this.$http.post('/dep/selectAllList.do', params).then(res => {
                if (res.ok) {
                    this.departmentInfo = res.body;
                }
            });
        }
    }
}
</script>
<style lang="scss" scoped>
.filter-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    span {
        margin-right: 10px;
    }
}

.block {
    margin-top: 10px;
    text-align: right;
}
</style>