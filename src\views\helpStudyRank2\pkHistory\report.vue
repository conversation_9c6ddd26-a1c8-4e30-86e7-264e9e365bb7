<template>
  <div class="pk-report">
    <div class="title">
      {{ title }}
    </div>

    <div v-if="componentName" class="table-wrap">
      <component :is="componentName" ref="report" :row="query" />
    </div>
  </div>
</template>

<script>
import PersonRank from '../helpStudyPkRank/zxPersonRank'; // 个人
import DepartRank from '../helpStudyPkRank/zxDepartRank'; // 部门
import WarRank from '../helpStudyPkRank/zxWarRank'; // 战区
// import Cookies from 'js-cookie';

export default {
  components: {
    PersonRank,
    DepartRank,
    WarRank
  },
  data() {
    return {
      title: '',
      query: {
        pkActId: '19', // 主活动id
        pkChildId: '164975105976757526', // 子活动id 个人
        pkChildType: ''
      }
    };
  },
  computed: {
    componentName() {
      const data = {
        '1': 'PersonRank',
        '2': 'DepartRank',
        '3': 'WarRank'
      };
      return data[this.query.pkChildType];
    }
  },
  // beforeCreate() {
  //   Cookies.set('SESSION', '7cbf1342-0fa6-4bcb-95d6-5313b93b9018');
  // },
  mounted() {
    const { pkActId, pkChildId, pkChildType, title } = this.$route.query;
    this.query.pkActId = pkActId;
    this.query.pkChildId = pkChildId;
    this.query.pkChildType = pkChildType;
    this.title = title;
    document.title = (title || '') + '-榜单';
    this.$nextTick(() => {
      this.$refs.report.refresh();
    });
  }
};
</script>

<style scoped lang="scss">
.pk-report {
  min-height: 100vh;
  color: #fff;
  background-color: #180E36;

  .title {
    text-align: center;
    padding: 20px 25px 0 25px;
    font-family: PingFangSC, PingFang SC !important;
    background: linear-gradient(180deg, #FAEEE3 0%, #D9B271 100%);
    background-clip: text;
    -webkit-background-clip: text;
    font-size: 36px;
    color: transparent;
  }
}
</style>
