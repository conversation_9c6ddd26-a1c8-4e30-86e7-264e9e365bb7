import axios from '@/utils/axios';

export const api = {
  // 公共api
  getScholarship: '/baseinfo/scholarship.do', // 获取优惠类型
  // 招生
  recruitAdd: '/recruit/recruitAdd.do', // 新增/编辑学员
  ifExistInfo: '/recruit/ifExistInfo.do', // 通过身份证检测用户信息
  validateBaseInfo: '/recruit/validateBaseInfo.do', // 这个是判断他的新读信息存不存在,检重的
  validateRecruit: '/recruit/validateRecruit.do', // 是判断有没有享受优惠资质的
  getRecruitInfo: '/recruit/getRecruitInfo.do', // 赠送信息
  showFeeList: '/recruit/showFeeList.do', // 收费标准
  getStudentInfoByMobile: '/recruit/getStudentInfoByMobile.do', // 根据手机号获取信息
  getTaByCityCode: '/baseinfo/getTaByCityCode.do', // 根据层次专业城市获取考区
  getScholarshipsByTaPfsn: '/recruit/getScholarships.do', // 根据专业和区域获取优惠类型
  getUnvs: '/bdUniversity/findAllKeyValue.do', // 还是获取院校接口
  getSpecialty: '/baseinfo/sPfsn.do', // 获取专业
  login: '/loginByMobile.do', // 登录接口

  // 毕业论文及报告提交
  getAllPaperList: '/graduatePaper/findAllPaperList.do', // 获取毕业论文列表
  webuploaderNew: '/graduatePaper/webuploaderNew.do', // 上传附件
  lookPaperHistory: '/graduatePaper/selectStudentAttachmentDetail.do', // 获取历史点评列表数据
  paperMasterRemark: '/graduatePaper/addRemark.do', // 编辑/添加班主任备注
  paperTeacherRemark: '/graduatePaper/addRemarkTwo.do', // 编辑/添加助学老师备注
  findAllThesisManagementList:
    '/thesisManagement/findAllThesisManagementList.do', // 毕业论文选题列表
  startOrStopThesis: '/thesisManagement/startOrStopThesis.do', // 论文题目启停
  graduatePaperEdit: '/graduatePaper/graduatePaperEdit.do', // 编辑学生信息
  getPaperDataStatus: '/graduatePaper/paperDataStatusNew.do', // 获取纸质状态信息
  updatePaperStatus: '/graduatePaper/updatePaperStatus.do', // 更新纸质资料状态信息
  getReplyRecord: '/graduatePaper/getReplyRecord.do', // 获取答辩结果信息
  updateReplyResult: '/graduatePaper/updateReplyResult.do', // 更新答辩结果信息
  paperAttachmentNew: '/graduatePaper/paperAttachmentNew.do', // 获取资料状态信息
  updateAttachment: '/graduatePaper/updateAttachment.do', // 更新资料审核状态
  getPhraseInfo: '/graduatePaper/toEditComment.do', // 获取审核评语信息
  updatePhrase: '/graduatePaper/updateComment.do', // 更新审核评语片语
  exportPaperInfo: '/graduatePaper/exportPaperInfo.do', // 导出毕业论文列表
  uploadThesis: '/thesisManagement/uploadThesis.do', // 批量导入论文题目
  uploadImgOss: '/graduatePaper/uploadPicture.do', // 上传图片到oss
  getAttachmentById: '/graduatePaper/getAttachmentById.do', // 附件id
  exportStudentThesisGuidanceRecord:
    '/graduatePaper/exportStudentThesisGuidanceRecord', // 论文指导记录导出

  // 分配老师
  getStudentPaperList: '/graduatePaper/findStudentPaperDistribution', // 毕业论文分配列表
  getUserEmpList: '/user/userListUsAsDistributionStudent', // 在职员工列表
  assginTeacher: '/graduatePaper/distributionTeacher', // 分配/批量分配老师

  // 内部任务通知
  getSysRemindList: '/workItem/getSysRemindList.do', // 内部任务通知列表
  updateRemindStatus: '/workItem/updateRemindStatus.do', // 任务通知的禁用启用
  sendRemind: '/workItem/sendRemind.do', // 任务通知的发送
  removeRemind: '/workItem/removeRemind.do', // 删除任务通知
  getCampusList: '/campus/selectAllList.do', // 校区列表
  planFinishList: '/workItem/planFinishList.do', // 完成情况列表
  empFinishList: '/workItem/empFinishList.do', // 完成情况列表
  exportStuInfo: '/workItem/exportStuInfo.do', // 导出学员详情
  exportPlanInfo: '/workItem/exportPlanInfo.do', // 导出完成情况
  remindManagerPerson: '/workItem/remindManagerPerson.do', // 提醒负责人
  remindEmployeePerson: '/workItem/remindEmployeePerson.do', // 提醒负责人
  getPerformerTree: '/innerMessage/getAllEmployee.do', // 获取执行者树
  addRemind: '/sysRemind/add.do', // 新增任务通知
  editRemind: '/sysRemind/update.do', // 编辑任务通知
  getDepList: '/dep/selectAllList.do', // 部门列表
  getRemindDetail: '/innerMessage/getDetail.do', // 通知详情
  uploadFiles: '/sysRemind/upload.do', // 上传文件
  getRemindStudentTotal: '/sysRemind/getRemindStudentTotal.do', // 任务关联的学员信息统计
  getRemindTaskStudentList: '/sysRemind/getRemindTaskStudentList.do', // 查询学服任务学生列表
  getRemindOrdinaryStudentList: '/sysRemind/getRemindOrdinaryStudentList.do', // 查询(非学服任务)学生列表
  getDictInfoList: '/innerMessage/getDictInfoList.do', // 查询字典
  getRemindStudentList: '/sysRemind/getRemindStudentList.do', // 任务关联的学员列表
  getTaskList: '/innerMessage/getTaskList.do', // 学服任务列表
  delAllRemindStudentTask: '/sysRemind/delAllRemindStudentTask.do', // 删除全部学员（有学服任务）
  addAllRemindStudentTask: '/sysRemind/addAllRemindStudentTask.do', // 添加全部学员（有学服任务）
  delRemindBindStudent: '/sysRemind/delRemindBindStudent.do', //  清空某个任务下的学员
  delAllRemindStudent: '/sysRemind/delAllRemindStudent.do', // 删除全部学员（非学服任务）
  addAllRemindStudent: '/sysRemind/addAllRemindStudent.do.do', // 添加全部学员（非学服任务）
  addRemindTaskStudent: '/sysRemind/addRemindTaskStudent.do', // 添加选中学员
  delRemindStudent: '/sysRemind/delRemindStudent.do', // 删除选中学员
  getRemindBindStudentType: '/sysRemind/getRemindBindStudentType', // 查看任务绑定学员类型
  getUnvsList: '/baseinfo/sUnvs.do', // 获取院校列表
  getPfsnsList: '/baseinfo/sPfsn.do', // 获取专业列表
  getSTaList: '/baseinfo/sTa.do', // 获取考区列表

  // 助学pk活动管理
  getActivityList: '/pkActivity/pkActivityList.do', // PK活动列表展示
  addActivity: '/pkActivity/addActivity.do', // 添加PK活动
  getActivity: '/pkActivity/getActivity.do', // 查询单个PK活动
  updateActivity: '/pkActivity/updateActivity.do', // 更新PK活动
  updateActivityEnable: '/pkActivity/updateActivityEnable.do', // 启用禁用
  getClanList: '/pkTeam/warList', // 获取子活动pk战区列表数据
  pkChildActivityPerformanceExport:
    '/pkChildActivity/pkChildActivityPerformanceExport', // 导出子活动绩效

  // pk子活动
  addChildActivity: '/pkChildActivity/addChildActivity.do', // 子活动添加
  importPersonal: '/pkTeam/importPersonal.do', // 人员导入
  personalList: '/pkTeam/personalList.do', // 个人列表展示
  getChildActivityInfo: '/pkChildActivity/getChildActivityInfo.do', // 子活动查询
  updateChildActivity: '/pkChildActivity/updateChildActivity.do', // 子活动编辑
  pkChildActivityList: '/pkChildActivity/pkChildActivityList.do', // 子活动列表展示
  personDelete: '/pkTeam/personDelete.do', // 个人子活动人员删除
  deleteTeam: '/pkTeam/deleteDepartToDepart',
  uploadTeamAvater: '/pkTeam/teamAvatarImport', // 上传战队头像
  getPkPerformance: '/pkPerformance/getPerformanceStreamFile', // 获取绩效
  getNewResult: '/pkPerformance/getIncrementPerformanceStreamGroup', // 获取新增的绩效成果
  getEmpAvatarv: '/pkChildActivity/getEmpAvatar', // 获取子活动员工头像
  personList: '/pkSinglePerson/personList',
  toggleParticipate: '/pkSinglePerson/singlePersonJoinActivity',
  clanPersonList: '/pkSingleClan/clanPersonList',
  pkSingleClanPersonList: '/pkSingleClan/pkSingleClanPersonList',
  pkMoreClanPersonList: '/pkSingleClan/pkSingleClanPersonListV2',
  reducePerformanceDetail: '/pkSingleClan/reducePerformanceDetail',
  pkSingleReducePerformanceDetail: '/pkSingleClan/pkSingleReducePerformanceDetail',
  pkActivityHistoryList: '/pkActivity/pkActivityHistoryList',

  // 新版本pk活动
  getMyPkStream: '/newPk/getMyPkStream', // 获取我的pk流水（助学榜单用）
  getPkStreamV2: '/newPk/getPkStreamV2', // 获取pk流水（助学榜单用）
  getAreaGroupStream: '/newPk/getAreaGroupStream', // 获取战队分组流水（助学榜单用）
  getTotalPkStream: '/newPk/getTotalPkStream', // 获取总的pk流水（活动管理用）
  getPkStream: '/newPk/getPkStream', // 获取pk流水（活动管理用）
  pkChildActivityPerformanceExportV2:
    '/pkChildActivity/pkChildActivityPerformanceExportV2', // 导出子活动绩效

  // 部门活动
  departList: '/pkTeam/departList.do', // 部门子活动列表展示
  departPersonList: '/pkTeam/departPersonList.do', // 部门子活动内部人员展示列表
  dpPersonJoinActivity: '/pkTeam/dpPersonJoinActivity.do', // 部门人员参加活动
  dpAllPersonJoinActivity: '/pkTeam/teamPersonJoinActivity', // 部门所有人员参加活动
  dpPersonManPower: '/pkTeam/dpPersonManPower.do', // 部门人员是否计算人力
  deleteDepartToDepart: '/pkTeam/deleteDepartToDepart.do', // 删除参加部门
  toAttachmentDetail: '/graduatePaper/toAttachmentDetail.do',
  findTaskInfo: '/studyActivity/findTaskInfo.do', // 获取论文资料任务下拉数据

  // 网报
  getSudentInfo: '/newWork/get.do', // 报读资料
  getTeacherList: '/sendCoupon/getTeacherList.do', // 获取老师信息
  insertRegisterNo: '/bdSceneConfirm/insertRegisterNo.do', // 新增预报名号接口
  updateConfirmInfo: '/bdSceneConfirm/updateConfirmInfoOrRegisterNo.do', // 修改预报名
  updateExamNo: '/bdSceneConfirm/updateExamNo.do', // 更新考生号
  upRemark: '/bdSceneConfirm/upRemark.do', // 更新备注
  getConfirmInfoById: '/bdSceneConfirm/getConfirmInfoById.do', // 获取考试号
  getRemark: '/sceneConfirm/getRemark.do', // 获取备注
  getSceneRegisterList: '/bdSceneConfirm/getSceneRegisterList.do', // 获取预报名列表信息
  // getannexlist: '/sceneConfirm/getannexlist.do', // 获取学员居住证信息
  getannexlist: '/newWork/getannexlist.do', // 获取学员居住证信息
  yzUpdateExamNo: '/sceneConfirm/updateExamNo.do', // 更新考生号（远智学堂）
  yzUpdateStudentRemark: '/sceneConfirm/updateStudentRemark.do', // 更新备注（远智学堂）
  yzGetSceneRegisterList: '/sceneConfirm/getSceneRegisterList.do', // 获取预报名列表信息（远智学堂）
  yzGetExamNoModifyRecord: '/sceneConfirm/getExamNoModifyRecord.do', // 获取变更记录信息（远智学堂）
  yzGetExamNoByLearnId: '/sceneConfirm/getExamNoByLearnId.do', // 获取考试号状态（远智学堂）

  setAvailabe: '/bdSceneConfirm/setAvailabe.do', // 设置有效预报名信息
  updatePassword: '/bdSceneConfirm/updatePassword.do', // 修改密码接口
  updateRemark: '/bdSceneConfirm/updateRemark.do', // 修改备注接口
  getExamNoModifyRecord: '/bdSceneConfirm/getExamNoModifyRecord.do', // 获取变更记录信息
  yzInsertRegisterNo: '/sceneConfirm/insertRegisterNo.do', // pc远智学堂新增预报名号接口
  // getAnnexList: '/recruit/getAnnexList.do', // 学生资料下载
  getAnnexList: '/newWork/getAnnexList.do', // 学生资料下载
  manualNetworkUpdateStatus: '/bdSceneConfirm/manualNetworkUpdateStatus.do', // 更新网报状态
  autonomousUpdate: '/newWork/autonomousUpdate.do',
  registrationConfirmedSuccess: '/newWork/registrationConfirmedSuccess.do',

  // bst
  getTeachTypeConf: '/getTeachTypeConf.do', // 获取上课方式配置

  // 知识库
  selectQuestionBranch:
    '/question/branch/selectQuestionBranchListGroupParentId', // 问题分支分层列表
  updateBatchQuestion: '/question/branch/updateBatchQuestionBranchById', // 问题分支上移下移的操作
  addQuestionBranch: '/question/branch/addQuestionBranch', //  新建员工知识库分支
  addBatchQuestion: '/question/responsible/addBatchQuestionResponsible', // 批量添加责任人
  deleteById: '/question/branch/deleteById', //  根据id删除问题分支
  updateQuestionBranchById: '/question/branch/updateQuestionBranchById', //  根据id更新问题分支
  selectAllEmployeeUser: '/user/selectAllEmployeeUser', // 问题分支责任人分页列表接口
  schoolAllList: '/campus/selectAllList.do', // 得到所有的校区接口
  departmentAllList: '/dep/selectAllList.do', // 得到所有的部门接口
  delectByResponsibleId: '/question/responsible/delectByResponsibleId', // 根据responsibleId删除责任人接口

  // 财务管理
  addEditPaymentItem: '/payment/addEditPaymentItem.do', // 创建收费项目
  getEditPaymentList: '/payment/getEditPaymentList.do', // 获取收费项目详情

  getTagGroupSelectList: '/tagGroup/queryListForSelect.do', // 用户分群名字

  // 职业
  jobTreeList: `/job/jobTreeList`,

  // 智米商城
  getZMProductList: '/product/page', // 商品管理列表
  updateZMProductStatus: '/product/batchUpdateStatus', // 商品批量上下架
  addZMProduct: '/product/add', // 新增商品
  updateZMProduct: '/product/updateById', // 修改商品信息
  getJdPageNum: '/exchange/getJdPageNum', // 京东商品类型
  getSkuDetail: '/jdProduct/getSkuDetail', // 查询: skuId商品详情
  getZMActivityConfigList: '/productActivityConfig/page', // 商城活动链接配置列表
  addZMActivityLink: '/productActivityConfig/add', // 新增商品活动链接配置
  updateZMActivityLink: '/productActivityConfig/updateById', // 修改商品活动链接配置
  getZMBannerList: '/mallBannerConfig/page', // 商城banner配置列表
  addZMBanner: '/mallBannerConfig/add', // 新增商城banner
  updateZMBanner: '/mallBannerConfig/updateById', // 修改商城banner
  getZMPorcelainList: '/mallPorcelainConfig/page', // 商城瓷片区配置列表
  addZMPorcelain: '/mallPorcelainConfig/add', // 新增商城瓷片区配置
  updateZMPorcelain: '/mallPorcelainConfig/updateById', // 修改商城瓷片区配置列表
  getZMCornerMarkList: '/productMarkConfig/page', // 商品角标配置列表
  addZMCornerMark: '/productMarkConfig/add', // 新增商品角标配置
  updateZMCornerMark: '/productMarkConfig/updateById', // 修改商品角标配置
  getZMHotSearchWordsList: '/mallHotSearchConfig/page', // 热门搜索词列表
  addZMHotSearchWords: '/mallHotSearchConfig/add', // 新增热门搜索词
  updateZMHotSearchWords: '/mallHotSearchConfig/updateById', // 修改热门搜索词列表
  getZMOrderList: '/mallOrder/page', // 订单列表
  updateZMOrder: '/mallOrder/updateById', // 修改订单
  getZMOrderRecord: '/mallOrder/pageOperateRecord', // 订单操作记录列表
  zMOrderDelivery: '/mallOrder/delivery', // 订单发货
  getRefundList: '/mallRefund/page', // 退款列表
  getRefundApplyList: '/mallOrder/page', // 退款申请列表
  zMRefundApply: '/mallRefund/add', // 退款申请
  zMRefundAudit: '/mallRefund/audit', // 退款审核
  zmLogisticsInfo: '/mallOrder/getJdOrderTrace', // 物流信息
  getZMproductCouponList: '/productCoupon/page', // 查询: 商品优惠券列表
  addZMproductCoupon: '/productCoupon/add', // 新增：商品优惠券信息
  updateZMproductCoupon: '/productCoupon/updateById', // 修改: 商品优惠券信息
  updateZMproductCouponCount: '/productCoupon/updateCount', // 修改: 优惠券数量
  addZMCouponCenterConfig: '/couponCenterConfig/add', // 新增：优惠券中心配置表信息
  updateZMCouponCenterConfig: '/couponCenterConfig/updateById', // 修改: 优惠券中心配置表信息
  getZMUsProductCouponList: '/usProductCoupon/page', // 查询: 优惠券领取明细
  getZMCouponGiveRecordList: '/couponGiveRecord/page', // 查询: 优惠券赠送记录列表
  updateZMCouponGiveRemark: '/couponGiveRecord/updateRemark', // 编辑: 优惠券赠送记录备注
  getZMCanSendCouponList: '/couponGiveRecord/getCanSendCouponList', // 查询: 可以赠送的优惠券列表
  getZMgCouponSummaryUsInfo: '/couponGiveRecord/getSummaryUsInfo', // 查询: 赠送用户信息
  addZMCouponGiveRecord: '/couponGiveRecord/add', // 新增：优惠券赠送记录信息
  instalmentList: '/instalment/config/queryByPage', // 获取海尔金融分期配置贴息列表
  instalmentUpdate: '/instalment/config/update' // 更新海尔金融分期配置贴息
};
// const optionDefaultParams = { page: 1, rows: 10 };

export const getScholarship = () => axios.post(api.getScholarship, {});

export const request = (key, data, config) =>
  axios.post(api[key], data, config);
