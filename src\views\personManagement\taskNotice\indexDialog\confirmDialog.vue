<template>
  <el-dialog
    width="402px"
    top="35vh"
    :visible.sync='show'
    :tipsTitle='tipsTitle'
    :destroy-on-close="true"
    :show-close="false"
    @close='close'
  >
    <div class="tips">
      <div class="tips-title">{{ tipsTitle }}</div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tipsTitle: {
      type: String,
      require: true,
      default: ''
    }
  },
  data() {
    return {
      show: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    confirm() {
      this.close();
      switch (this.tipsTitle) {
        case '确认要发送吗？':
          this.$emit('sendRemind');
          break;
        case '确认要删除选中的提醒吗？':
          this.$emit('removeRemind');
          break;
        case '确认要执行导出Excel操作吗？该操作需要等待一定时间':
          this.$emit('exportExcel');
          break;
        default :
          this.$emit('updateRemindStatus');
          break;
      }
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  border-radius: 10px;
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
    .tips{
       padding: 43px 78px 0 78px;
      .tips-title {
        margin-bottom: 11px;
        font-size: 18px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #1A1B1D;
        line-height: 25px;
        text-align: center;
      }
    }
    .tips-confirm {
      width: 100%;
      height: 52px;
      text-align: center;
      font-size: 16px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FF6445;
      line-height: 52px;
      border-top:1px solid #f0eeee;
    }
  }
}
</style>
