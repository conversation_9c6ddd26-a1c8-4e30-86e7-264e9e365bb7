<template>
  <div>
    <!-- 顶部筛选 -->
    <el-form
      ref="searchForm"
      :model="form"
      label-width="120px"
      class="yz-search-form"
      size="mini"
      @submit.native.prevent='search'
    >
      <el-form-item label="商品id:" prop="productId">
        <el-input v-model="form.productId" placeholder="请输入商品id" clearable />
      </el-form-item>
      <el-form-item label="商品名称:" prop="productName">
        <el-input v-model="form.productName" placeholder="请输入商品名称" clearable />
      </el-form-item>
      <el-form-item label="订单号:" prop="orderId">
        <el-input v-model="form.orderId" placeholder="请输入订单号" clearable />
      </el-form-item>
      <el-form-item label="下单人远智编号:" prop="yzCode">
        <el-input v-model="form.yzCode" placeholder="请输入下单人远智编号" clearable />
      </el-form-item>
      <el-form-item label="下单人手机号:" prop="mobile">
        <el-input v-model="form.mobile" placeholder="请输入下单人手机号" clearable />
      </el-form-item>
      <el-form-item label="快递单号:" prop="logisticsNo">
        <el-input v-model="form.logisticsNo" placeholder="请输入快递单号" clearable />
      </el-form-item>
      <el-form-item label="下单起:" prop="startTime">
        <el-date-picker
          v-model="form.startTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          placeholder="请选择"
        />
      </el-form-item>
      <el-form-item label="下单止:" prop="endTime">
        <el-date-picker
          v-model="form.endTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          placeholder="请选择"
        />
      </el-form-item>
      <el-form-item label="订单状态:" prop="status">
        <el-select v-model="form.status" placeholder="请选择订单状态" clearable>
          <el-option v-for="item in logisticsStatus" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          native-type="submit"
          size="mini"
        >查询</el-button>
        <el-button size="mini" @click="search(0)">重置</el-button>
      </div>
    </el-form>
    <!-- 新增栏 -->
    <div class="option-box">
      <div class="left">
        <p>
          1、订单总数（实物）：{{ orderInfo.totalEntityCount }} ，
          购买商品数（实物）：{{ orderInfo.totalEntityAmount }}，
          订单总额（实物）：{{ orderInfo.totalEntityPrice }}，
          订单总数（京东卡）：{{ orderInfo.totalCardCount }}，
          购买商品数（京东卡）：{{ orderInfo.totalCardAmount }}，
          订单总额（京东卡）：{{ orderInfo.totalCardPrice }}
        </p>
        <p>
          2、账户余额（实物）：{{ orderInfo.entityBalance }}，
          账户余额（京东卡）：{{ orderInfo.cardBalance }}
        </p>
      </div>
      <div>
        <el-button type="warning" size="mini" @click="handleExport">EXCEL导出</el-button>
      </div>
    </div>
    <el-table ref="table" v-loading="tableLoading" size="small" :data="tableData" style="width: 100%; margin: 25px 0" border>
      <el-table-column prop="orderId" label="订单号" align="center" />
      <el-table-column prop="productId" label="商品id" align="center" />
      <el-table-column prop="productName" label="商品名称" align="center" />
      <el-table-column label="商品类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.productType | goodsTypeEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="productSpecName" label="规格名称" align="center" />
      <el-table-column prop="marketPrice" label="商品单价（元）" align="center" />
      <el-table-column prop="amount" label="购买数量" align="center" />
      <el-table-column prop="totalPrice" label="商品总价（元）" align="center" />
      <el-table-column prop="freightAmount" label="运费（元）" align="center" />
      <el-table-column prop="couponDiscount" label="优惠券抵扣（元）" align="center" width="125">
        <template slot-scope="scope">
          <p>-{{ scope.row.couponDiscount }}</p>
          <el-button v-if="scope.row.couponId" type="primary" size="mini" @click="lookCouponId(scope.row)">查看优惠券id</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="zmDiscount" label="智米抵扣（元）" align="center" />
      <el-table-column prop="balanceDiscount" label="余额抵扣（元）" align="center" />
      <el-table-column prop="paymentPrice" label="现金支付（元）" align="center" />
      <el-table-column label="下单用户信息" align="center">
        <template slot-scope="scope">
          <p>远智编号: {{ scope.row.yzCode }}</p>
          <p>真实姓名: {{ scope.row.realName }}</p>
          <p>手机号: {{ scope.row.mobile }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="下单时间" align="center" />
      <el-table-column label="收货信息" align="center">
        <template slot-scope="scope">
          <p>收货人: {{ scope.row.consigneeName }}</p>
          <p>联系电话: {{ scope.row.consigneeMobile }}</p>
          <p>收货地址: {{ scope.row.consigneeAddress }}</p>
        </template>
      </el-table-column>
      <el-table-column label="快递公司" align="center">
        <template slot-scope="scope">
          {{ scope.row.logisticsType | logisticsTypeEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="logisticsNo" label="快递单号" align="center" width="130">
        <template slot-scope="scope">
          <el-button type="text" @click="handleLookLogisticsInfo(scope.row)">{{ scope.row.logisticsNo }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.status | logisticsStatusEnum }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="handleLook(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <edit-modal :visible.sync="editVisible" :currentId="currentId" />
    <logistics-info-modal :visible.sync="logisticsInfoVisible" :row="currentRow" />
    <!-- 批量导入 -->
    <!-- 优惠券id -->
    <couponIdModal :visible.sync="couponIdVisible" :currentId="currentCouponId" />
  </div>
</template>

<script>
import editModal from './edit-modal';
import logisticsInfoModal from './logistics-info-modal';
import couponIdModal from './../components/coupon-id-modal';
import { downUri } from '@/config/request';
import { arrToEnum, SplicingParams } from '@/utils';
import { logisticsStatus, goodsType, logisticsType } from './../../type';
const logisticsStatusEnum = arrToEnum(logisticsStatus);
const goodsTypeEnum = arrToEnum(goodsType);
const logisticsTypeEnum = arrToEnum(logisticsType);
export default {
  components: {
    editModal,
    logisticsInfoModal,
    couponIdModal
  },
  filters: {
    yesOrNoEnum(val) {
      const JudgeEnum = { 0: '禁用', 1: '启用' };
      return JudgeEnum[val] || '/';
    },
    logisticsStatusEnum(val) {
      return logisticsStatusEnum[val] || '/';
    },
    goodsTypeEnum(val) {
      const jdOther = {
        JD_ENTITY_PRODUCT: '京东百货',
        JD_CARD_PRODUCT: '京东百货'
      };
      return goodsTypeEnum[val] || jdOther[val] || '/';
    },
    logisticsTypeEnum(val) {
      return logisticsTypeEnum[val] || '/';
    }
  },
  data() {
    return {
      couponIdVisible: false, // 查看优惠券id弹框
      logisticsStatus: logisticsStatus,
      editVisible: false, // 编辑弹框
      currentId: '',
      currentCouponId: '',
      form: {
        productType: 'JD_PRODUCT', // SELF_ENTITY_PRODUCT：自营好物；SELF_VIRTUAL_PRODUCT：虚拟产品；JD_PRODUCT：京东百货
        productId: '',
        productName: '',
        orderId: '',
        yzCode: '',
        mobile: '',
        logisticsNo: '',
        startTime: '',
        endTime: '',
        status: ''
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableLoading: false,
      tableData: [],
      orderInfo: {},
      logisticsInfoVisible: false, // 物流信息弹框
      currentRow: {} // 当前row
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    // 查看优惠券id
    lookCouponId(row) {
      this.currentCouponId = row.couponId;
      this.couponIdVisible = true;
    },
    // 查看物流信息
    handleLookLogisticsInfo(row) {
      this.logisticsInfoVisible = true;
      this.currentRow = row;
    },
    // 导出
    handleExport() {
      const params = SplicingParams(this.handleQueryParams());
      const exportUrl = downUri + '/mallOrder/exportOrder?' + params;
      window.location.href = exportUrl;
    },
    // 查看
    handleLook(row) {
      this.currentId = row.orderId;
      this.editVisible = true;
    },
    // 查询和重置
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 处理筛选参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQueryParams();
      this.$post('getZMOrderList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
          this.orderInfo = body.jdStatisticsOrderVO;
        }
      });
    }
  }
};
</script>

<style lang = "scss" scoped>
.option-box {
  margin: 15px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    color:red;
  }
}
</style>
