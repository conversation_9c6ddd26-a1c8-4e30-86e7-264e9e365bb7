<!-- 弹窗-不同任务类型 -->
<template>
  <el-dialog :visible.sync="visible" append-to-body width="46%" :close-on-click-modal="false" @open="openInit" @close="closeBtn(0)">
    <el-form ref="ruleForm" :model="querys" :rules="rules" :size="'mini'" label-width="210px">
      <el-form-item label="任务类型：" :required="true" :prop="!isEdit?'type':''">
        <el-select v-model="querys.type" filterable clearable placeholder="请选择" :disabled="isEdit" @change="changeSelect">
          <el-option label="注册" value="1" />
          <el-option label="测评" value="2" />
          <el-option label="报名活动" value="3" />
          <el-option label="首次发帖" value="4" />
          <el-option label="添加老师" value="5" />
          <el-option label="报读" value="6" />
          <el-option v-if="isEdit" label="缴费" value="7" />
        </el-select>
      </el-form-item>
      <div class="form-p">
        <p v-if="querys.type==1">备注：通过<span>【邀约有礼】渠道</span>邀请新用户完成注册，邀约人可获得智米奖励</p>
        <p v-if="querys.type==2">备注：通过<span>【邀约有礼】渠道注册的用户，</span>完成指定的测评题目，邀约人可获得对应奖励</p>
        <p v-if="querys.type==3">备注：通过<span>【邀约有礼】渠道注册的用户，</span>报名指定的上进活动后，邀约人可获得对应奖励</p>
        <p v-if="querys.type==4">备注：通过<span>【邀约有礼】渠道注册的用户，</span>完成APP首次发帖，邀约人可获得对应奖励</p>
        <p v-if="querys.type==5">备注：通过<span>【邀约有礼】渠道注册的用户，</span>添加企微老师，邀约人可获得对应奖励</p>
        <p v-if="querys.type==6">备注：通过<span>【邀约有礼】渠道注册的用户，</span>完成学历报读（自考/成教/国开/研究生）后，邀约人可获得智米奖励</p>
        <p v-if="querys.type==7">备注：通过<span>任何渠道</span>注册的用户，完成学历缴费（自考/成教/国开/研究生）后，邀约人可获得智米奖励</p>
      </div>
      <el-form-item label="任务图标：" :required="true" prop="picture">
        <upload-file ref="uploasdd" :max-limit="1" :size="2" accept="image/jpg,image/png,image/gif,image/jpeg" exts="jpg|png|gif|jpeg" :file-list="querys.pictureArrs" @remove="removePicture" @success="successPicture" />
        <p style="font-size: 12px;color: #7c7c7c;">仅支持上传JPG/PNG/JPEG，推荐尺寸为 44*44（px）</p>
      </el-form-item>
      <el-form-item label="任务标题：" :required="true" prop="title">
        <el-input v-model.trim="querys.title" placeholder="用于前端展示" clearable maxlength="5" show-word-limit />
      </el-form-item>
      <el-form-item label="任务明细：" :required="true" prop="detail">
        <el-input v-model.trim="querys.detail" placeholder="用于前端展示" clearable maxlength="12" show-word-limit />
      </el-form-item>      
      <el-form-item label="对未缴费的邀约人是否生效：" :required="true" prop="isSupportUnpaid" >
        <el-radio-group v-model="querys.isSupportUnpaid" :disabled="isEdit">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="querys.type==2" label="请选择测评题目：" :required="querys.type==2" :prop="querys.type==2?'evalId':''">
        <el-select v-model="querys.evalId" placeholder="请选择" filterable clearable :remote="true" :disabled="isEdit" :loading="evalLoading" :remote-method="getEvalList">
          <el-option v-for="item in evalData" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="querys.type==3" label="请选择活动名称：" :required="querys.type==3" :prop="querys.type==3?'actId':''">
        <el-select v-model="querys.actId" placeholder="请选择" filterable clearable :remote="true" :disabled="isEdit" :loading="actLoading" :remote-method="getActiveList">
          <el-option v-for="item in actData" :key="item.id" :label="item.actName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="querys.type==4" label="引导海报：" :required="querys.type==4" :prop="querys.type==4?'guidePicture':''">
        <upload-file :max-limit="1" :size="2" accept="image/jpg,image/png,image/gif,image/jpeg" exts="jpg|png|gif|jpeg" :file-list="querys.guideImgArrs" @remove="removeGuidePicture" @success="successGuidePicture" />
        <p style="font-size: 12px;color: #7c7c7c;">仅支持上传JPG/PNG/JPEG，推荐尺寸为 281*420（px）</p>
      </el-form-item>
      <div v-if="querys.type==5" style="padding-left: 46px;">
        <p style="margin-bottom: 20px;">导流老师企微：对应跟进人的企微</p>
        <el-form-item class="eval-zlabel" label="若用户无跟进人，请选择导流的老师：" :prop="querys.type==5?'guideEmpId':''">
          <el-select v-model="querys.guideEmpId" placeholder="请选择" filterable clearable :disabled="isEdit">
            <el-option v-for="item in teacherData" :key="item.empId" :label="item.empName" :value="item.empId" />
          </el-select>
        </el-form-item>
      </div>
      <el-form-item v-if="querys.type==6" label="报读海报：" :required="querys.type==6" :prop="querys.type==6?'readPicture':''">
        <upload-file :max-limit="1" :size="2" accept="image/jpg,image/png,image/gif,image/jpeg" exts="jpg|png|gif|jpeg" :file-list="querys.readImgArrs" @remove="removeReadPicture" @success="successReadPicture" />
        <p style="font-size: 12px;color: #7c7c7c;">仅支持上传JPG/PNG/JPEG，推荐尺寸为 281*420（px）</p>
      </el-form-item>
      <div v-if="querys.type==7">
        <p style="display: flex;margin: 10px 0 15px 50px;">
          <span>赠送智米数：30000 </span>
          <a href="https://doc.weixin.qq.com/sheet/e3_ALYAogYtAKgm8UEWUjPQUW9ikYajc?scode=AN8AXQd9AA415eSHG8AbwAogYYAGk&tab=BB08J2">【点击查看智米赠送规则】</a>
        </p>
        <el-form-item label="缴费海报：" :required="isEdit&&querys.type==7" :prop="isEdit?'paidPicture':''">
          <upload-file :max-limit="1" :size="2" accept="image/jpg,image/png,image/gif,image/jpeg" exts="jpg|png|gif|jpeg" :file-list="querys.paidImgArrs" @remove="removePaidPicture" @success="successPaidPicture" />
          <p style="font-size: 12px;color: #7c7c7c;">仅支持上传JPG/PNG/JPEG，推荐尺寸为 281*420（px）</p>
        </el-form-item>
      </div>
      <el-form-item v-else label="智米奖励数：" :required="true" :prop="!isEdit?'giveZhimi':''">
        <el-input-number v-model="querys.giveZhimi" :disabled="isEdit" :min="1" :max="30000" :precision="0" :controls="false" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeBtn(0)">取 消</el-button>
      <el-button type="primary" native-type="submit" @click.stop="submitBtn">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { ossUri } from '@/config/request';

export default {
  components: {},
  props: {
    visible: { type: Boolean, default: false },
    edits: { type: Object, default: () => null }
  },
  // 这里存放数据
  data() {
    return {
      querys: {
        type: '',
        picture: '',
        pictureArrs: [],
        title: '',
        detail: '',
        isSupportUnpaid: 0,
        giveZhimi: '',
        evalId: '',
        actId: '',
        param: {},
        guidePicture: '',
        guideImgArrs: [],
        guideEmpId: '',
        readPicture: '',
        readImgArrs: [],
        paidPicture: '',
        paidImgArrs: []
      },
      rules: {
        type: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        picture: [{ required: true, message: '请上传图片', trigger: 'change' }],
        title: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        detail: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        giveZhimi: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        evalId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        actId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        guidePicture: [{ required: true, message: '请上传图片', trigger: 'change' }],
        guideEmpId: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        readPicture: [{ required: true, message: '请上传图片', trigger: ['blur', 'change'] }],
        paidPicture: [{ required: true, message: '请上传图片', trigger: ['blur', 'change'] }],
        isSupportUnpaid:[{required:true,message:'请选择',trigger: ['blur', 'change']}]
      },
      evalLoading: false,
      evalData: [],
      actLoading: false,
      actData: [],
      teacherLoading: false,
      teacherData: []
    };
  },
  computed: {
    isEdit() {
      return Boolean(this.edits);
    }
  },
  methods: {
    changeSelect(e){
      if(e==7){
        this.querys.isSupportUnpaid = 1
      }else{
        this.querys.isSupportUnpaid = 0
      }
    },
    // 获取编辑表单数据
    async openInit() {
      await this.getQWList();
      await this.getEvalList();
      await this.getActiveList();
      if (this.isEdit) {
        const obs = JSON.parse(JSON.stringify(this.edits));
        if (obs.picture) obs.pictureArrs = [{ url: ossUri + obs.picture }];
        if (obs.guidePicture) obs.guideImgArrs = [{ url: ossUri + obs.guidePicture }];
        if (obs.readPicture) obs.readImgArrs = [{ url: ossUri + obs.readPicture }];
        if (obs.paidPicture) obs.paidImgArrs = [{ url: ossUri + obs.paidPicture }];
        if (obs.param) {
          obs.param = JSON.parse(obs?.param || '{}');
          obs['evalId'] = obs.param?.evalId || '';
          obs['actId'] = obs.param?.actId || '';
        }
        this.querys = obs;
        this.$refs['ruleForm']?.resetFields();
        console.log('获取任务编辑表单数据', { ...this.querys });
      }
    },
    // 删除 任务 图标图片上传
    removePicture() {
      this.querys.picture = '';
      this.querys.pictureArrs = [];
    },
    // 添加 任务 图标图片上传
    successPicture(evt) {
      this.querys.picture = evt?.response;
    },
    // 删除 引导 海报图片上传
    removeGuidePicture() {
      this.querys.guidePicture = '';
      this.querys.guideImgArrs = [];
    },
    // 添加 引导 海报图片上传
    successGuidePicture(evt) {
      this.querys.guidePicture = evt?.response;
    },
    // 删除 报读 海报图片上传
    removeReadPicture() {
      this.querys.readPicture = '';
      this.querys.readImgArrs = [];
    },
    // 添加 报读 海报图片上传
    successReadPicture(evt) {
      this.querys.readPicture = evt?.response;
    },
    // 删除 缴费 海报图片上传
    removePaidPicture() {
      this.querys.paidPicture = '';
      this.querys.paidImgArrs = [];
    },
    // 添加 缴费 海报图片上传
    successPaidPicture(evt) {
      this.querys.paidPicture = evt?.response;
    },
    // 获取测评题目数据
    getEvalList(names = '') {
      this.$http.post('/cp/eval/list', { title: names })
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            this.evalData = body?.data || [];
          }
        });
    },
    // 获取活动名称数据
    getActiveList(names = '') {
      console.log('获取活动名称数据', names);
      this.$http.post('/upwardActivity/list.do', {
        actStatus: 1,
        actName: names
      })
        .then((res) => {
          this.actData = [];
          const { code, body } = res;
          if (code === '00') {
            const data = body?.data || [];
            const cuts = new Date().valueOf();
            data.forEach((item) => {
              const enrollEndTime = new Date(item.enrollEndTime).valueOf();
              const enrollStartTime = new Date(item.enrollStartTime).valueOf();
              // console.log('在活动报名期间', { ...item });
              // console.log('cuts,enrollEndTime,enrollStartTime,：', cuts, item.enrollEndTime, item.enrollStartTime);
              // 在活动报名期间
              if (cuts >= enrollStartTime && cuts <= enrollEndTime) {
                this.actData.push(item);
              }
            });
          }
        });
    },
    // 获取企微老师数据
    getQWList() {
      this.$http.post('/inviteTaskConfig/getOnjobEmpList.do')
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            this.teacherData = body || [];
          }
        });
    },
    // 任务表单提交
    submitBtn() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.querys.param = {};
          const urs = `/inviteTaskConfig/${this.isEdit ? 'update' : 'insert'}InviteTaskConfig.do`;
          const obs = JSON.parse(JSON.stringify(this.querys));
          obs.isSupportUnpaid = JSON.stringify(obs.isSupportUnpaid)
          for (const key in obs) {
            if (obs[key] == '' || obs[key] == null) delete obs[key];
          }
          if (this.querys.type == 2) {
            obs.param.evalId = Number(this.querys?.evalId || '');
          }
          if (this.querys.type == 3) {
            obs.param.actId = Number(this.querys?.actId || '');
          }
          obs.param = JSON.stringify(obs.param);
          if (obs.picture) delete obs.pictureArrs;
          if (obs.guidePicture) delete obs.guideImgArrs;
          if (obs.readPicture) delete obs.readImgArrs;
          if (obs.paidPicture) delete obs.paidImgArrs;
          console.log('表单提交-obs', obs);
          this.$http.post(urs, obs)
            .then((res) => {
              const { code } = res;
              if (code === '00') this.closeBtn(1);
              this.tableLoading = false;
            })
            .catch((err) => {
              this.tableLoading = false;
              console.log('表格数据-err', err);
            });
        }
      });
    },
    // 关闭
    closeBtn(type) {
      this.$refs.uploasdd?.handleRemoveImg('', []);
      this.querys = { type: '', picture: '', isSupportUnpaid:0, pictureArrs: [], title: '', detail: '', giveZhimi: '', evalId: '', actId: '', param: {}, guidePicture: '', guideImgArrs: [], guideEmpId: '', readPicture: '', readImgArrs: [], paidPicture: '', paidImgArrs: [] };
      this.$refs['ruleForm']?.resetFields();
      this.$emit('on-close', type);
    }
  }
};
</script>

<style lang="scss">
.form-p {
  margin: 0 0 15px 60px;
  font-size: 13px;
  color: #8a8989;
  span {
    color: rgb(248, 122, 122);
  }
}
.eval-zlabel {
  display: flex;
  .el-form-item__content {
    margin-left: 0 !important;
  }
  .el-form-item__label {
    width: auto !important;
  }
}
</style>
