<template>
  <common-dialog
    is-full
    :title="query.dialogTitle + '战队业绩情况'"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main team-member">
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        border
        size="small"
        style="width: 100%"
        height="calc(100vh - 80px)"
        header-cell-class-name='table-header-cell'
        :data="tableData"
      >
        <el-table-column type="index" label="序号" align="center" />
        <el-table-column label="部门" prop="dpName" align="center" />
        <el-table-column label="助学老师姓名" prop="empName" align="center" />
        <el-table-column label="职位" prop="jobTitle" align="center" />
        <el-table-column v-if="quePkRange.includes('1')" label="今日成教" prop="tdCj" align="center" />
        <el-table-column v-if="quePkRange.includes('1')" label="活动成教" prop="actCjView" align="center" />
        <el-table-column v-if="quePkRange.includes('2')" label="今日国开" prop="tdGk" align="center" />
        <el-table-column v-if="quePkRange.includes('2')" label="活动国开" prop="actGkView" align="center" />
        <el-table-column v-if="quePkRange.includes('3')" label="今日全日制" prop="tdQrz" align="center" />
        <el-table-column v-if="quePkRange.includes('3')" label="活动全日制" prop="actQrzView" align="center" />
        <el-table-column v-if="quePkRange.includes('4')" label="今日自考" prop="tdZk" align="center" />
        <el-table-column v-if="quePkRange.includes('4')" label="活动自考" prop="actZkView" align="center" />
        <el-table-column v-if="quePkRange.includes('5')" label="今日研究生" prop="tdYjs" align="center" />
        <el-table-column v-if="quePkRange.includes('5')" label="活动研究生" prop="actYjsView" align="center" />
        <el-table-column v-if="quePkRange.includes('6')" label="今日职业教育" prop="tdZyjy" align="center">
          <template v-slot="scope">
            <el-link class="link" @click="openTdActDialog({...scope.row,zdyType:1})">{{ scope.row.tdZyjy ||0 }}</el-link>
            <i v-if="!scope.row.pit" class="icon-arrow-right"></i>
          </template>
        </el-table-column>
        <el-table-column v-if="quePkRange.includes('6')" label="活动职业教育" prop="actZyjyView" align="center">
          <template v-slot="scope">
            <el-link class="link" @click="openTdActDialog({...scope.row,zdyType:0})">{{ scope.row.actZyjyView||0 }}</el-link>
            <i v-if="!scope.row.pit" class="icon-arrow-right"></i>
          </template>
        </el-table-column>
        <el-table-column v-if="quePkRange.includes('7')" label="今日海外教育" prop="tdHw" align="center" />
        <el-table-column v-if="quePkRange.includes('7')" label="活动海外教育" prop="actHwView" align="center" />
        <el-table-column label="调整业绩" prop="reduce" align="center" />
        <el-table-column label="活动合计" prop="actAll" align="center" />
        <el-table-column :label="pkScoreLabel" prop="pkScore" align="center" />
        <template slot="empty">
          <div class="data-null">
            <img src="../../../assets/imgs/helpStudyPkRank/data-null.png" alt="" />
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

      <!-- 职业教育详情弹窗 -->
      <zyjy-details :visible.sync="resultsDialogShow" :query="currentLookTeam" />
    </div>
  </common-dialog>
</template>

<script>
import ZyjyDetails from './zyjy-details';

export default {
  components: { ZyjyDetails },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    query: {
      type: Object,
      default: function() {
        return { pkRange: [], pkType: '', dialogTitle: '' };
      }
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      resultsDialogShow: false,
      tableData: [],
      currentLookTeam: {}
    };
  },
  computed: {
    pkScoreLabel() {
      // pkType 1 ：助学积分; pkType 2 ：助学人数;  pkType3：活动人均;
      const data = {
        1: '助学积分',
        2: '助学人数',
        3: '活动人均'
      };
      return data[String(this.query.pkType)];
    },
    quePkRange() {
      return this.query?.pkRange || [];
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      console.log('query', this.query);
      this.tableLoading = true;
      const params = {
        pkActId: this.query.pkActId,
        pkChildId: this.query.pkChildId,
        teamId: this.query.newTeamId
      };
      const urs = this.query.isPerson == 1 ? 'pkMoreClanPersonList' : 'pkSingleClanPersonList';
      this.$post(urs, params).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body;
        }
        this.tableLoading = false;
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    openTdActDialog(row) {
      const source = JSON.parse(JSON.stringify(this.query));
      this.currentLookTeam = {
        ...row,
        ...source,
        empId: row?.empId || source?.empId,
        pkActId: source.pkActId,
        pkType: source.pkType,
        pkChildId: source.pkChildId,
        dialogTitle: row?.dialogTitle || ''
      };
      this.resultsDialogShow = true;
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .yz-common-dialog__header {
  background: #2B1F54;
  color: #fff;
  border: none;

  .title {
    color: #fff !important;
  }
}

::v-deep .yz-common-dialog__content {
  overflow: hidden;
}

.team-member {
  background: #180E36;

.icon-arrow-right {
  width: 3px;
  height: 6px;
  background: url("../../../assets/imgs/helpStudyPkRank/icon-arrow-right.png") no-repeat;
  background-size: 100% 100%;
  display: inline-block;
  margin-left: 6px;
}

.link {
  color: #fff;

  &:hover {
    color: #fff;
  }

  &:hover:after {
    position: absolute;
    left: 0;
    right: 0;
    height: 0;
    bottom: 0;
    border-bottom: 1px solid #fff;
  }

}

  // 表格
  ::v-deep .el-table {
    color: #fff;
    background: none;
  }

  ::v-deep .el-table tr {
    background: #2B1F54;
    color: #fff;
  }

  ::v-deep .el-table--border {
    border: 1px solid rgba(#fff, 0.1);
  }

  ::v-deep .el-table::before {
    height: 0;
  }

  ::v-deep .el-table th.el-table__cell.is-leaf {
    border: none;
  }

  ::v-deep .table-header-cell {
    background: #4731A6;
    color: #FFFFFF;
  }

  ::v-deep .el-table__cell {
    border-right: 1px solid rgba(#fff, 0.1);
    border-bottom: 1px solid rgba(#fff, 0.1);
  }

  ::v-deep .el-table__row:hover {
    background: #39287C;
  }

  ::v-deep .el-table__body tr:hover>td.el-table__cell {
    background-color: #39287C;
  }

  // 分页容器
  ::v-deep .el-pager li.active {
    color: #409EFF;
    background: linear-gradient(90deg, rgba(95, 153, 254, 0.5) 0%, #396BFF 100%);
    border: none;
  }

  ::v-deep .el-pager li {
    background-color: #180E36;
    border: 1px solid rgba(255, 255, 255, 0.6);
  }

  ::v-deep .el-pagination__total, ::v-deep .el-pagination__jump {
    color: #fff;
  }

  .yz-table-pagination {
    margin-top: 20px;
  }
}
</style>
