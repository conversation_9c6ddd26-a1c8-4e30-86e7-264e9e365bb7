<template>
  <div>
    <!-- 顶部筛选 -->
    <el-form
      ref="searchForm"
      :model="form"
      label-width="120px"
      class="yz-search-form"
      size="mini"
      @submit.native.prevent='search'
    >
      <el-form-item label="优惠券名称:" prop="couponName">
        <el-input v-model="form.couponName" placeholder="请输入优惠券名称" clearable />
      </el-form-item>
      <el-form-item label="优惠券ID:" prop="couponId">
        <el-input v-model="form.couponId" placeholder="请输入优惠券ID" clearable />
      </el-form-item>
      <el-form-item label="优惠券类型:" prop="couponType">
        <el-select v-model="form.couponType" placeholder="请选择优惠券类型" clearable>
          <el-option v-for="item in couponType" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="上架状态:" prop="status">
        <el-select v-model="form.status" placeholder="请选择上架状态" clearable>
          <el-option v-for="item in couponShelfStatus" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label='可领券时间:' prop='couponTime'>
        <el-date-picker
          v-model="form.couponTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="适用用户:" prop="applyUser">
        <el-select v-model="form.applyUser" placeholder="请选择适用用户" clearable>
          <el-option v-for="item in couponUserType" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="详情页是否展示:" prop="ifDetailShow">
        <el-select v-model="form.ifDetailShow" placeholder="请选择详情页是否展示" clearable>
          <el-option v-for="item in couponDetailShowStatus" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button size="mini" @click="search(0)">重置</el-button>
      </div>
    </el-form>

    <!-- 新增栏 -->
    <div style="float:right;margin:15px 25px 15px 0;">
      <el-button v-if="isFinishedcenterConfigId" type="primary" size="mini" @click="couponCenterConfigVisible = true">领券中心配置</el-button>
      <el-button type="primary" size="mini" @click="handleAddEdit('add')">创建优惠券</el-button>
    </div>

    <el-table ref="table" v-loading="tableLoading" size="small" :data="tableData" style="width: 100%; margin: 25px 0" border>
      <el-table-column prop="couponId" label="优惠券id" align="center" />
      <el-table-column prop="couponName" label="优惠券名称" align="center" width="135" />
      <el-table-column label="优惠券类型" align="center" width="130">
        <template slot-scope="scope">
          {{ scope.row.couponType | couponTypeEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="couponPrice" label="优惠券额度" align="center" width="105" />
      <el-table-column label="适用用户" align="center">
        <template slot-scope="scope">
          {{ scope.row.applyUser | couponUserTypeEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="limitGetNum" label="每人限领" align="center" />
      <el-table-column prop="availableScope" label="适用商品" align="center" width="105" />
      <el-table-column prop="couponTime" label="可领券时间" align="center" width="135">
        <template slot-scope="scope">
          <p> {{ scope.row.couponStartTime }}</p>
          <p> {{ scope.row.couponEndTime }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="validTimeRange" label="优惠券有效时间" align="center" width="157">
        <template slot-scope="scope">
          <p style="white-space: pre-wrap;">{{ scope.row.validTimeRange }}</p>
        </template>
      </el-table-column>
      <el-table-column label="详情页是否展示" align="center">
        <template slot-scope="scope">
          {{ scope.row.ifDetailShow | couponDetailShowStatusEnum }}
        </template>
      </el-table-column>
      <el-table-column label="总数量" align="center" width="175">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleStock(scope.row, 'reduce')">-</el-button>
          <span class="amount-btn">{{ scope.row.couponNum }}</span>
          <el-button type="primary" size="mini" @click="handleStock(scope.row, 'increase')">+</el-button>
        </template>
      </el-table-column>
      <el-table-column label="已领取" align="center">
        <template slot-scope="scope">
          <el-link class="el-link-always-underline" type="primary" :underline="false" @click="handleCenterConfig(scope.row)">{{ scope.row.couponReceiveNum }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="couponUseNum" label="已使用" align="center" />
      <el-table-column label="上架状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.status | couponShelfStatusEnum }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="140">
        <template slot-scope="scope">
          <div style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
            <el-button class="mt10" type="primary" size="small" @click="handleAddEdit('edit',scope.row)">编辑</el-button>
            <el-button class="mt10" style="margin-left: 0;" :type="scope.row.status == 1 ? 'info': 'primary'" size="small" @click="handleShelfStatus(scope.row)">{{ scope.row.status == 1 ? '下架': '上架' }}</el-button>
            <el-button class="mt10" style="margin-left: 0;" type="primary" size="small" @click="handleOperateLog(scope.row)">查询操作记录</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 库存弹框 -->
    <stock-modal :visible.sync="stockVisible" :stockType="stockType" :currentRow="currentRow" />
    <!-- 操作记录弹框 -->
    <operate-log-modal :visible.sync="operateLogVisible" :currentRow="currentRow" />
    <!-- 新增和编辑弹框 -->
    <add-edit-modal :visible.sync="addEditVisible" :currentRow="currentRow" />
    <!-- 领券中心配置弹框 -->
    <coupon-center-config-model :visible.sync="couponCenterConfigVisible" :currentRow="currentRow" :centerConfigId="centerConfigId" />
    <!-- 领取详情弹框 -->
    <coupon-collection-details-model :visible.sync="couponCollectionDetailsVisible" :currentRow="currentRow" />
  </div>
</template>

<script>
import { arrToEnum } from '@/utils';
import { couponType, couponShelfStatus, couponUserType, couponDetailShowStatus } from './../../type';
const couponTypeEnum = arrToEnum(couponType);
const couponShelfStatusEnum = arrToEnum(couponShelfStatus);
const couponUserTypeEnum = arrToEnum(couponUserType);
const couponDetailShowStatusEnum = arrToEnum(couponDetailShowStatus);

import stockModal from './stock-modal.vue';
import operateLogModal from './operate-log-modal.vue';
import addEditModal from './add-edit-model.vue';
import couponCenterConfigModel from './coupon-center-config-modal.vue';
import couponCollectionDetailsModel from './coupon-collection-details-model.vue';
export default {
  components: { stockModal, operateLogModal, addEditModal, couponCenterConfigModel, couponCollectionDetailsModel },
  filters: {
    couponTypeEnum(val) {
      return couponTypeEnum[val] || '/';
    },
    couponShelfStatusEnum(val) {
      return couponShelfStatusEnum[val] || '/';
    },
    couponUserTypeEnum(val) {
      return couponUserTypeEnum[val] || '/';
    },
    couponDetailShowStatusEnum(val) {
      return couponDetailShowStatusEnum[val] || '/';
    }
  },
  data() {
    return {
      couponType: couponType, // 优惠券类型
      couponShelfStatus: couponShelfStatus, // 优惠券上架状态
      couponUserType: couponUserType, // 优惠券适用用户
      couponDetailShowStatus: couponDetailShowStatus, // 优惠券详情页是否展示

      operateLogVisible: false, // 操作记录弹框
      stockVisible: false, // 库存弹框
      stockType: '', // 库存弹框类型
      addEditVisible: false, // 新增和编辑弹框
      couponCenterConfigVisible: false, // 领券中心配置弹框
      couponCollectionDetailsVisible: false, // 领取详情弹框

      currentRow: {}, // 当前行数据
      form: {
        couponName: '', // 优惠券名称
        couponId: '', // 优惠券ID
        couponType: '', // 优惠券类型
        status: '', // 上架状态
        couponTime: [], // 可领券时间范围
        applyUser: '', // 适用用户
        ifDetailShow: '' // 详情页是否展示
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableLoading: false,
      tableData: [],
      isFinishedcenterConfigId: false, // 是否请求了领券中心配置
      centerConfigId: '' // 领券中心配置Id
    };
  },
  created() {
    this.getTableList();
    this.getCouponCenterConfig();
  },
  methods: {
    handleCenterConfig(row) {
      this.couponCollectionDetailsVisible = true;
      this.currentRow = row;
    },
    // 获取最新配置Id
    getCouponCenterConfig() {
      this.$http.get('/couponCenterConfig/getConfigId').then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.isFinishedcenterConfigId = true;
          this.centerConfigId = body ?? '';
        }
      });
    },
    // 新增和编辑
    handleAddEdit(type, row) {
      this.currentRow = type == 'add' ? {} : row;
      this.addEditVisible = true;
    },
    // 查看操作记录
    handleOperateLog(row) {
      this.currentRow = row;
      this.operateLogVisible = true;
    },
    // 上下架
    handleShelfStatus(row) {
      const { status, couponEndTime, couponNum, couponReceiveNum, auto } = row;

      // 当前是下架状态
      if (status != 1) {
        if (new Date().getTime() > new Date(couponEndTime).getTime()) {
          // 当前时间 > 优惠券领取截止时间
          return this.$message.error('上架失败！优惠券领取时间已结束');
        } else if (couponNum - couponReceiveNum <= 0 && auto == 0) {
          // 优惠券总数量-已领取 < 0 且 不自动下架
          return this.$message.error('上架失败！优惠券数量已领完');
        }
      }

      let tip = '';
      // 上架 -> 下架
      if (row.status == 1) tip = '优惠券下架后，已领券用户可正常使用，未领取的无法领取';
      // 下架 -> 上架
      else tip = '是否上架优惠券？';

      this.$confirm(tip, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.post(`/productCoupon/updateStatus/${row.couponId}/${row.status == 1 ? 0 : 1}`, {}, { json: true }).then((res) => {
          const { fail } = res;
          if (!fail) {
            this.$message.success('操作成功');
            this.getTableList();
          }
        });
      });
    },
    // 库存弹框
    handleStock(row, stockType) {
      this.currentRow = row;
      this.stockType = stockType;
      this.stockVisible = true;
    },
    // 查询和重置
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 处理筛选参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      formData.couponStartTime = formData.couponTime[0] ?? '';
      formData.couponEndTime = formData.couponTime[1] ?? '';
      delete formData.couponTime;
      return {
        ...formData,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQueryParams();
      this.$post('getZMproductCouponList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>

<style lang = "scss" scoped>
.text-left {
  text-align: left;
}
.amount-btn {
  padding: 0 5px;
}
.el-link-always-underline {
  text-decoration: underline !important;
}
</style>
