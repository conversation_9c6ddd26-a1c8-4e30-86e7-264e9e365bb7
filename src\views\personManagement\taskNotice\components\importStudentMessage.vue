<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    confirmText="开始导入"
    title="配置学员"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        size='mini'
        :model='form'
        label-width='120px'
      >
        <el-form-item label='模板：'>
          <a :href="templateUrl" download>
            <el-button type="primary" plain>下载模板</el-button>
          </a>
        </el-form-item>

        <el-form-item label='选择文件：'>
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            :action="action"
            :data="uploadData"
            :before-upload="beforeUpload"
            :on-exceed="handleExceed"
            :on-success="uploadSuccess"
            :name="field"
            :file-list="fileList"
            multiple
            :limit="1"
            :auto-upload="false"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击选择文件</em></div>
            <div slot="tip" class="el-upload__tip">
              <p>说明：</p>
              <p>（学业编码、学员姓名、年级）为关键列</p>
              <p style="color: red;">请严格按模板填写，否则可能导致无法准确导入。</p>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      require: true,
      default: ''
    },
    remindId: {
      type: Number,
      require: true,
      default: 0
    }
  },
  inject: ['parentVm'],
  data() {
    return {
      show: false,
      form: {},
      fileList: [],
      field: '',
      templateUrl: '',
      action: '',
      config: {
        templateUrl: './template/messageStudent.xlsx',
        action: ' /sysRemind/importStudent.do',
        field: 'file'
      }
    };
  },
  computed: {
    uploadData() {
      return {
        remindId: JSON.stringify(this.remindId)
      };
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {
      this.templateUrl = this.config.templateUrl;
    },
    submit() {
      this.$refs.upload.submit();
    },
    uploadSuccess(response, file, fileList) {
      this.$refs.upload.clearFiles();
      var txet = '';
      if (response.code === '00') {
        if (response.body === true) {
          txet = '导入成功！';
        } else {
          txet = response.body;
        }
      } else {
        txet = response.msg;
      }
      this.$alert(txet, '提示', {
        dangerouslyUseHTMLString: true
      }).then(() => {
        this.show = false;
        this.parentVm.getTableList();
      });
    },
    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        this.$nextTick(() => {
          this.field = this.config.field;
          this.action = this.config.action;
          resolve();
        });
      });
    },
    handleExceed() {
      this.$message.error('每次只能上传一个文件');
    },
    close() {
      this.$emit('closeExcelImport');
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
