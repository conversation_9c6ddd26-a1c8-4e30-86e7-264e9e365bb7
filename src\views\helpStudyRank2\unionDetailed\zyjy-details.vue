<template>
  <common-dialog
    is-full
    :title="query.dialogTitle + '职业教育业绩情况'"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main team-member">
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        border
        size="small"
        height="calc(100vh - 80px)"
        style="width: 100%"
        header-cell-class-name='table-header-cell'
        :data="tableData"
      >
        <el-table-column type="index" label="序号" align="center" />
        <el-table-column label="部门" prop="dpName" align="center" />
        <el-table-column label="员工号" prop="staffNo" align="center" />
        <el-table-column label="助学老师姓名" prop="empName" align="center" />
        <el-table-column label="职位" prop="jobTitle" align="center" />
        <el-table-column label="课程编码" prop="joinId" align="center" />
        <el-table-column label="课程名称" prop="joinName" align="center" />
        <el-table-column label="远智编码" prop="yzCode" align="center" />
        <el-table-column label="购买人" prop="realName" align="center" />
        <el-table-column label="付费时间" prop="payTime" align="center" />
        <template slot="empty">
          <div class="data-null">
            <img src="../../../assets/imgs/helpStudyPkRank/data-null.png" alt="" />
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

    </div>
  </common-dialog>
</template>

<script>
import moment from 'moment';

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    query: {
      type: Object,
      default: function() {
        return { pkRange: [], pkType: '', dialogTitle: '' };
      }
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      this.tableLoading = true;
      // zdyType：1-今日 0-活动
      const params = {
        pkChildId: this.query.pkChildId,
        empId: this.query.empId
      };
      const usr = Number(this.query?.zdyType) ? '/pkPerformance/getZyjyTodayPerformanceList' : '/pkPerformance/getZyjyAllPerformanceList';
      this.$http.post(usr, params).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          body.map(item => {
            item.payTime = item.payTime && moment(Number(item.payTime)).format('YYYY-MM-DD  hh:mm:ss');
          });
          this.tableData = body;
        }
        this.tableLoading = false;
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .yz-common-dialog__header {
  background: #2B1F54;
  color: #fff;
  border: none;

  .title {
    color: #fff !important;
  }
}

::v-deep .yz-common-dialog__content {
  overflow: hidden;
}

.team-member {
  background: #180E36;

  // 表格
  ::v-deep .el-table {
    color: #fff;
    background: none;
  }

  ::v-deep .el-table tr {
    background: #2B1F54;
    color: #fff;
  }

  ::v-deep .el-table--border {
    border: 1px solid rgba(#fff, 0.1);
  }

  ::v-deep .el-table::before {
    height: 0;
  }

  ::v-deep .el-table th.el-table__cell.is-leaf {
    border: none;
  }

  ::v-deep .table-header-cell {
    background: #4731A6;
    color: #FFFFFF;
  }

  ::v-deep .el-table__cell {
    border-right: 1px solid rgba(#fff, 0.1);
    border-bottom: 1px solid rgba(#fff, 0.1);
  }

  ::v-deep .el-table__row:hover {
    background: #39287C;
  }

  ::v-deep .el-table__body tr:hover>td.el-table__cell {
    background-color: #39287C;
  }

  // 分页容器
  ::v-deep .el-pager li.active {
    color: #409EFF;
    background: linear-gradient(90deg, rgba(95, 153, 254, 0.5) 0%, #396BFF 100%);
    border: none;
  }

  ::v-deep .el-pager li {
    background-color: #180E36;
    border: 1px solid rgba(255, 255, 255, 0.6);
  }

  ::v-deep .el-pagination__total, ::v-deep .el-pagination__jump {
    color: #fff;
  }

  .yz-table-pagination {
    margin-top: 20px;
  }

}

</style>
