<template>
  <common-dialog v-loading="showLoading" class="add_career" is-full title="添加职业教育课程" :visible.sync="showVisible" :show-footer="true" @open="init" @close="careerClose" @confirm="careerClose(1)">
    <div class="add_career-main">
      <!-- 左边 - 添加 -->
      <div class="main-left">
        <div class="main-input">
          <el-input v-model="searchKye" maxlength="16" clearable show-word-limit @clear="getTableList">
            <el-button slot="append" icon="el-icon-search" @click.stop="getTableList" />
          </el-input>
          <el-button v-if="tableLeftData&&tableLeftData.length" @click.stop="addList(1)">添加当页全部</el-button>
        </div>
        <el-table v-if="tableLeftData&&tableLeftData.length" :data="tableLeftData" :show-header="false">
          <el-table-column>
            <template slot-scope="scope">
              <span>{{ scope.row.joinName }}（{{ scope.row.joinId }}）</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80px">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" :disabled="scope.row.isEdits" @click="addList(0,scope)">添加</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-empty v-else description="查无数据" />
        <!-- 分页区 -->
        <div class="yz-table-pagination">
          <pagination :total='pagination.total' :page.sync="pagination.page" :limit.sync="pagination.limit" @pagination="getTableList" />
        </div>
      </div>
      <!-- 右边 - 删除 -->
      <div class="main-right">
        <el-button v-if="tableRightData&&tableRightData.length" class="main-input" type="danger" @click.stop="deleteList(1)">删除全部</el-button>
        <el-table v-if="tableRightData&&tableRightData.length" :data="tableRightData" :show-header="false">
          <el-table-column>
            <template slot-scope="scope">
              <span>{{ scope.row.joinName }}（{{ scope.row.joinId }}）</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80px">
            <template slot-scope="scope">
              <div class="main-btns" @click="deleteList(0,scope)">删除</div>
            </template>
          </el-table-column>
        </el-table>
        <el-empty v-else description="查无数据" />
        <!-- 分页区 -->
        <div class="yz-table-pagination">
          <pagination :total='rgihtPagination.total' :page.sync="rgihtPagination.page" :limit.sync="rgihtPagination.limit" @pagination="changeTableList" />
        </div>
      </div>
    </div>
  </common-dialog>
</template>

<script>

export default {
  props: {
    visible: { type: Boolean, default: false },
    list: { type: Array, default: () => [] }
  },
  data() {
    return {
      showVisible: false,
      showLoading: false,
      searchKye: '',
      tableLeftData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      rightCopyData: [],
      rgihtPagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  computed: {
    tableRightData() {
      const news = JSON.parse(JSON.stringify(this.rightCopyData));
      return news.splice((this.rgihtPagination.page - 1) * this.rgihtPagination.limit, this.rgihtPagination.limit);
    }
  },
  watch: {
    visible(val) {
      this.showVisible = val;
    }
  },
  methods: {
    init() {
      this.list.map(item => {
        item.isEdits = true;
      });
      this.rightCopyData = JSON.parse(JSON.stringify(this.list));
      this.rgihtPagination.total = this.rightCopyData.length;
      this.getTableList();
    },
    // 查询
    getTableList() {
      this.showLoading = true;
      const data = {
        searchKye: this.searchKye || '',
        start: this.pagination.page,
        length: this.pagination.limit
      };
      this.$http.post('/pkChildActivity/getVocationalCourse', data)
        .then(res => {
          const { body } = res;
          body?.data?.map(item => {
            item.isEdits = false;
            item.score = 0;
            item.joinId = item.id;
            item.joinName = item.name;
            delete item.id;
            delete item.name;
            // 除去已经被选中的
            this.rightCopyData.map(sed => {
              if (sed.joinId == item.joinId) item.isEdits = true;
            });
          });
          this.tableLeftData = body?.data || [];
          this.pagination.total = body?.recordsTotal || 0;
          this.showLoading = false;
        }).catch(() => {
          this.tableLeftData = [];
          this.showLoading = false;
        });
    },
    // 添加
    addList(type, obs) {
      if (!this.tableLeftData?.length) {
        this.$message({ type: 'warning', message: '暂无可添加数据' });
        return;
      }
      // 全部添加
      if (type == 1) {
        let num = 0;
        this.tableLeftData.map(item => {
          item.score = 0;
          if (!item.isEdits) {
            item.isEdits = true;
            this.rightCopyData.push(item);
            this.rgihtPagination.total = this.rightCopyData.length;
          }
          item.isEdits && num++;
        });
        // 当前十条已经全部添加
        if (num == 10) {
          this.$message({ type: 'warning', message: '已添加当前页全部数据！' });
        }
      } else {
        // 单个添加
        const { row } = obs;
        row.score = 0;
        row.isEdits = true;
        this.rightCopyData.push(row);
        this.rgihtPagination.total = this.rightCopyData.length;
      }
    },
    // 删除部分的分页处理
    changeTableList(item) {
      this.rgihtPagination = { ...this.rgihtPagination, ...item };
    },
    // 删除已添加的
    deleteList(type, scope) {
      if (!this.rightCopyData?.length) {
        this.$message({ type: 'warning', message: '请先选择删除数据' });
        return;
      }
      this.$confirm('确认取消此商品进入PK？', '删除确认框', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        // 全部删除
        if (type == 1) {
          this.tableLeftData.map(item => {
            item.isEdits = false;
          });
          this.rightCopyData = [];
          this.rgihtPagination.total = 0;
        } else {
        // 单个删除
          const { $index, row } = scope;
          this.tableLeftData.map(item => {
            if (row.joinId == item.joinId) item.isEdits = false;
          });
          this.rightCopyData.splice($index, 1);
          this.rgihtPagination.total = this.rightCopyData.length;
        }
        this.$message({ type: 'success', message: '删除成功！' });
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消删除' });
      });
    },
    // 关闭弹窗
    careerClose(type = 0) {
      this.$emit('close', type && this.rightCopyData);
      this.searchKye = '';
      this.pagination = {
        total: 0,
        page: 1,
        limit: 10
      };
      this.rgihtPagination = {
        total: 0,
        page: 1,
        limit: 10
      };
      this.tableLeftData = [];
      this.rightCopyData = [];
    }
  }
};
</script>

<style lang="scss">
  .add_career {
    &-main {
      margin: 20px 40px;
      display: flex;
      justify-content: space-between;
      .main-left {
        padding: 20px 0 0 16px;
        width: 49%;
        min-height: 240px;
        max-height: 660px;
        border: 1px solid #d7e9f7;
        .el-table {
          height: 84%;
          overflow: hidden;
        }
        .main-input {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          .el-input-group__append,
          .el-input-group__prepend {
            color: #ffffff;
            border: 1px solid #d7e9f7;
            background-color: #62affd;
          }
        }
        .el-input-group {
          width: 84%;
          margin-right: 1%;
        }
      }
      .main-right {
        padding-bottom: 20px;
        width: 49%;
        min-height: 240px;
        max-height: 660px;
        border: 1px solid #d7e9f7;
        .el-table {
          width: 98%;
          height: 87%;
          overflow: hidden;
          overflow-y: scroll;
        }
        .main-input {
          float: right;
          margin-bottom: 10px;
        }
        .main-btns {
          display: inline-block;
          line-height: 1;
          white-space: nowrap;
          cursor: pointer;
          border: 1px solid #F56C6C;
          background: #F56C6C;
          color: #FFF;
          appearance: none;
          -webkit-appearance: none;
          text-align: center;
          -webkit-box-sizing: border-box;
          box-sizing: border-box;
          outline: 0;
          margin: 0;
          transition: .1s;
          -webkit-transition: .1s;
          font-weight: 500;
          padding: 7px 15px;
          font-size: 12px;
          border-radius: 3px;
        }
        .main-btns:focus,
        .main-btns:hover {
          background: #f78989;
          border-color: #f78989;
          color: #FFF;
        }
      }
    }
    .yz-common-dialog__footer {
      text-align: center;
    }
  }
</style>

