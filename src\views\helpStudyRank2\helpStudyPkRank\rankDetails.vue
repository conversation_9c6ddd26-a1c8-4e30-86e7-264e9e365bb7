<template>
  <div class="yz-base-container">
    <div class="header">
      <div class="l">
        <el-button class="pkDetails" type="primary" @click="comeBack">
          返回
        </el-button>
      </div>
      <h1 class="center">{{ pkChildName }}</h1>
      <div class="r">
        <div class="refresh" @click="refresh">刷新</div>
        <p class="text">截止{{ nowDate }}数据</p>
      </div>
    </div>
    <div v-if="endTime" class="countdown-container">
      <countdown :endTime="endTime" />
    </div>
    <div v-loading="tableLoading" element-loading-background="rgba(0, 0, 0, 0.8)" class="connect">
      <div class="block">
        <PkProgress :list="warList" :title="pkType | pkTypeToText" />
        <!-- 表格 -->
        <div v-if="warList.length" class="table-box">
          <el-table
            v-sticky-scroller
            border
            size="small"
            header-cell-class-name='table-cell-header'
            :data="warList[0].areaStreamVO"
            class="first-table"
            :header-row-class-name="headerStyle"
            :row-class-name="tableRowClassName"
            :span-method="objectSpanMethod"
          >
            <el-table-column prop="dpName" label="部门" width="140" align="center" fixed />
            <el-table-column prop="dpEmpName" label="分校长" align="center" fixed />
            <el-table-column prop="manPower" label="人力" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('tmDialogShow', scope.row)">{{ scope.row.manPower
                }}</el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>

            <template v-if="pkRange.includes('1')">
              <el-table-column prop="adultEduToday" label="今日成教" align="center" />
              <el-table-column prop="adultEduTotal" label="活动成教" align="center" />
            </template>
            <template v-if="pkRange.includes('2')">
              <el-table-column prop="nationalOpenToday" label="今日国开" align="center" />
              <el-table-column prop="nationalOpenTotal" label="活动国开" align="center" />
            </template>
            <template v-if="pkRange.includes('3')">
              <el-table-column prop="fullTimeToday" label="今日全日制" align="center" />
              <el-table-column prop="fullTimeTotal" label="活动全日制" align="center" />
            </template>
            <template v-if="pkRange.includes('4')">
              <el-table-column prop="selfStudyToday" label="今日自考" align="center" />
              <el-table-column prop="selfStudyTotal" label="活动自考" align="center" />
            </template>
            <template v-if="pkRange.includes('5')">
              <el-table-column prop="postgraduateToday" label="今日研究生" align="center" />
              <el-table-column prop="postgraduateTotal" label="活动研究生" align="center" />
            </template>
            <template v-if="pkRange.includes('6')">
              <el-table-column prop="vocationalEduToday" label="今日职业教育" align="center" width="92" />
              <el-table-column prop="vocationalEduTotal" label="活动职业教育" align="center" width="92" />
            </template>
            <template v-if="pkRange.includes('7')">
              <el-table-column prop="overseasEduToday" label="今日海外教育" align="center" width="92" />
              <el-table-column prop="overseasEduTotal" label="活动海外教育" align="center" width="92" />
            </template>

            <el-table-column prop="todayOrders" label="今日合计" align="center" />
            <el-table-column prop="performanceDeduction" label="调整业绩" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('lrDialogShow', scope.row)">{{
                  scope.row.performanceDeduction }}</el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>
            <el-table-column prop="totalOrders" label="活动合计" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('resultsDialogShow', scope.row)">{{
                  scope.row.totalOrders }}</el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>

            <!-- 普通目标 普通目标可以存在多个-->
            <template v-if="targetType === 1 && targetConfigList.length">
              <el-table-column
                v-for="(item, index) in targetConfigList"
                :key="index"
                :prop="targetConfigListKey + index"
                :label="item"
                align="center"
                width="150"
              >
                <template slot-scope="scope">
                  <target-progress
                    :target="scope.row.totalScore"
                    :total="scope.row.targetConfigList &&
                      scope.row.targetConfigList[index]
                    "
                  />
                </template>
              </el-table-column>
            </template>

            <!-- 助学积分、助学人数、活动人均 -->
            <el-table-column prop="totalScore" align="center">
              <template slot="header">
                <span class="score-column-color">{{ pkType | pkTypeToText }}</span>
              </template>

              <template slot-scope="scope">
                <span class="score-column-color">{{ scope.row.totalScore }}</span>
              </template>
            </el-table-column>
          </el-table>

          <el-table
            v-sticky-scroller
            border
            size="small"
            header-cell-class-name='table-cell-header'
            :data="warList[1].areaStreamVO"
            class="second-table"
            :header-row-class-name="headerStyle"
            :row-class-name="tableRowClassName"
            :span-method="objectSpanMethod"
          >
            <el-table-column prop="dpName" label="部门" width="140" align="center" fixed />
            <el-table-column prop="dpEmpName" label="分校长" align="center" fixed />
            <el-table-column prop="manPower" label="人力" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('tmDialogShow', scope.row)">{{ scope.row.manPower
                }}</el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>

            <template v-if="pkRange.includes('1')">
              <el-table-column prop="adultEduToday" label="今日成教" align="center" />
              <el-table-column prop="adultEduTotal" label="活动成教" align="center" />
            </template>
            <template v-if="pkRange.includes('2')">
              <el-table-column prop="nationalOpenToday" label="今日国开" align="center" />
              <el-table-column prop="nationalOpenTotal" label="活动国开" align="center" />
            </template>
            <template v-if="pkRange.includes('3')">
              <el-table-column prop="fullTimeToday" label="今日全日制" align="center" />
              <el-table-column prop="fullTimeTotal" label="活动全日制" align="center" />
            </template>
            <template v-if="pkRange.includes('4')">
              <el-table-column prop="selfStudyToday" label="今日自考" align="center" />
              <el-table-column prop="selfStudyTotal" label="活动自考" align="center" />
            </template>
            <template v-if="pkRange.includes('5')">
              <el-table-column prop="postgraduateToday" label="今日研究生" align="center" />
              <el-table-column prop="postgraduateTotal" label="活动研究生" align="center" />
            </template>
            <template v-if="pkRange.includes('6')">
              <el-table-column prop="vocationalEduToday" label="今日职业教育" align="center" width="92" />
              <el-table-column prop="vocationalEduTotal" label="活动职业教育" align="center" width="92" />
            </template>
            <template v-if="pkRange.includes('7')">
              <el-table-column prop="overseasEduToday" label="今日海外教育" align="center" width="92" />
              <el-table-column prop="overseasEduTotal" label="活动海外教育" align="center" width="92" />
            </template>

            <el-table-column prop="todayOrders" label="今日合计" align="center" />
            <el-table-column prop="performanceDeduction" label="调整业绩" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('lrDialogShow', scope.row)">{{
                  scope.row.performanceDeduction }}</el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>
            <el-table-column prop="totalOrders" label="活动合计" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('resultsDialogShow', scope.row)">{{
                  scope.row.totalOrders }}</el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>

            <!-- 普通目标 普通目标可以存在多个-->
            <template v-if="targetType === 1 && targetConfigList.length">
              <el-table-column
                v-for="(item, index) in targetConfigList"
                :key="index"
                :prop="targetConfigListKey + index"
                :label="item"
                align="center"
                width="150"
              >
                <template slot-scope="scope">
                  <target-progress
                    :target="scope.row.totalScore"
                    :total="scope.row.targetConfigList &&
                      scope.row.targetConfigList[index]
                    "
                  />
                </template>
              </el-table-column>
            </template>

            <!-- 助学积分、助学人数、活动人均 -->
            <el-table-column prop="totalScore" align="center">
              <template slot="header">
                <span class="score-column-color">{{ pkType | pkTypeToText }}</span>
              </template>

              <template slot-scope="scope">
                <span class="score-column-color">{{ scope.row.totalScore }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 人力详情弹窗 -->
    <team-member-dialog :visible.sync="tmDialogShow" :query="currentLookTeam" />
    <!-- 减业绩详情弹窗 -->
    <loss-results-dialog :visible.sync="lrDialogShow" :query="currentLookTeam" />
    <!-- 活动合计详情弹窗 -->
    <results-dialog :visible.sync="resultsDialogShow" :query="currentLookTeam" />
  </div>
</template>

<script>
import TeamMemberDialog from '../unionDetailed/team-member-dialog';
import LossResultsDialog from '../unionDetailed/loss-results-dialog';
import ResultsDialog from '../unionDetailed/results-dialog';
import countdown from './countdown.vue'; // 倒计时
import { formatTimeStamp, throttle } from '@/utils';
import { StickyScroller } from '@cell-x/el-table-sticky';
import PkProgress from './components/PkProgress.vue';
import targetProgress from './components/targetProgress.vue';

export default {
  directives: {
    StickyScroller: new StickyScroller({ offsetBottom: '15px' }).init()
  },
  filters: {
    pkTypeToText(val) {
      const pkTypeEnum = {
        1: '助学积分',
        2: '助学人数',
        3: '活动人均'
      };
      return pkTypeEnum[val] || '/';
    }
  },
  components: {
    TeamMemberDialog,
    LossResultsDialog,
    ResultsDialog,
    countdown,
    PkProgress,
    targetProgress
  },
  data() {
    return {
      groupId: null,
      pkActId: null,
      pkChildId: null,
      tmDialogShow: false, // 人力详情弹窗
      lrDialogShow: false, // 减业绩详情弹窗
      resultsDialogShow: false, // 活动合计详情弹窗
      nowDate: null,
      currentLookTeam: {}, // 当前查看的团队
      endTime: 0, // 结束时间戳
      pkType: 0, // 1：助学积分 2：助学人数 3：活动人均
      pkRange: [], // pk范围，1：成教 2：国开 3：全日制 4：自考 5：研究生 6：职业教育 7：海外教育
      targetType: 0, // 1: 普通目标, 2: 奖励目标
      targetConfigList: [], // 普通目标的配置
      pkChildName: '', // 标题
      warList: [],
      tableLoading: false,
      firstHandler: null,
      secondHandler: null,
      targetConfigListKey: 'targetConfigList_' // 普通目标的key
    };
  },
  created() {
    this.init();
  },
  beforeDestroy() {
    this.removeScrollSync();
  },
  methods: {
    doLayout() {
      this.$nextTick(() => {
        this.$refs.firstTable.doLayout();
        this.$refs.secondTable.doLayout();
      });
    },
    init() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.pkActId = this.$route.query.pkActId;
      this.pkChildId = this.$route.query.pkChildId;
      this.groupId = this.$route.query.groupId;
      this.nowDate = formatTimeStamp(new Date().getTime(), 'MM月DD日  HH:mm:ss');
      this.getChildActivityInfo(this.pkChildId);
      this.getAreaGroupStream(this.pkChildId, this.groupId);
    },
    // 获取活动信息
    getChildActivityInfo(pkChildId) {
      this.$post('getChildActivityInfo', { pkChildId }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.pkType = body.pkType;
          this.endTime = body.endTime;
          this.pkRange = body.pkRange.split(',');
          this.targetType = body.targetType;
          this.pkChildName = body.pkChildName;
          this.targetConfigList = Array.isArray(body.targetConfigList) ? body.targetConfigList : [];
        }
      });
    },
    // 获取pk流水（助学榜单用）
    getAreaGroupStream(pkChildId, groupId) {
      this.tableLoading = true;
      this.$post('getAreaGroupStream', { pkChildId, groupId }).then(async res => {
        const { fail, body } = res;
        if (!fail) {
          // 为每个团队的数据添加合计行
          body.forEach(team => {
            if (team.areaStreamVO && team.areaStreamVO.length > 0) {
              const summaryRow = this.calculateSummaryRow(team.areaStreamVO);
              team.areaStreamVO.push(summaryRow);
            }
          });
          this.warList = this.handleCompareGrades(body);
        }
      }).finally(() => {
        this.tableLoading = false;
        this.setupScrollSync();
      });
    },
    // 处理比较成绩
    handleCompareGrades(data) {
      const firstPk = data[0];
      const secondPk = data[1];
      const result = this.calculatePercentages(firstPk.totalOrders, secondPk.totalOrders);
      if (firstPk.totalOrders === secondPk.totalOrders) {
        firstPk.isWin = false;
        secondPk.isWin = false;
        firstPk.width = result.A;
        secondPk.width = result.B;
      } else if (firstPk.totalOrders > secondPk.totalOrders) {
        firstPk.isWin = true;
        secondPk.isWin = false;
        firstPk.width = result.A;
        secondPk.width = result.B;
      } else {
        firstPk.isWin = false;
        secondPk.isWin = true;
        firstPk.width = result.A;
        secondPk.width = result.B;
      }
      return data;
    },
    // 处理成绩百分比
    calculatePercentages(A, B) {
      let percentageA = (A / (A + B)) * 100;
      let percentageB = (B / (A + B)) * 100;
      if (A === 0 && B === 0) {
        percentageA = 50;
        percentageB = 50;
      }
      // 保留两位小数
      percentageA = Math.round(percentageA * 100) / 100;
      percentageB = Math.round(percentageB * 100) / 100;

      return { A: percentageA, B: percentageB };
    },
    refresh() {
      this.init();
    },
    openDetailsDialog(field, item) {
      this.currentLookTeam = {
        pkActId: this.pkActId,
        pkChildId: this.pkChildId,
        pkType: this.pkType,
        pkRange: this.pkRange,
        dialogTitle: item?.supTeamName || '',
        newTeamId: item.supTeamId
      };
      this[field] = true;
    },
    // 计算合计行数据
    calculateSummaryRow(data) {
      if (!data || data.length === 0) return {};

      // 初始化合计行
      const summaryRow = {
        dpName: '合计',
        dpEmpName: '', // 留空，会被合并
        isSummaryRow: true, // 标记为合计行
        targetConfigList: []
      };

      // 定义不需要求和的字段
      const excludeFields = [
        'dpName', 'dpEmpName', 'supTeamName', 'supTeamId',
        'isSummaryRow', 'targetConfigList', 'areaStreamVO'
      ];

      // 获取第一行数据的所有key
      const firstRow = data[0];
      const allKeys = Object.keys(firstRow);

      // 动态初始化数字类型的字段
      allKeys.forEach(key => {
        if (!excludeFields.includes(key)) {
          const value = firstRow[key];
          // 检查是否为数字类型或可转换为数字的字符串
          if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
            summaryRow[key] = 0;
          }
        }
      });

      // 计算各列的合计
      data.forEach(row => {
        // 动态累加所有数字字段
        Object.keys(summaryRow).forEach(key => {
          if (!excludeFields.includes(key) && typeof summaryRow[key] === 'number') {
            summaryRow[key] += Number(row[key]) || 0;
          }
        });

        // 保留第一行的团队信息（用于弹窗）
        if (!summaryRow.supTeamName) {
          summaryRow.supTeamName = row?.supTeamName || '';
          summaryRow.supTeamId = row.supTeamId;
        }

        // 处理目标配置列表（特殊处理）
        if (row.targetConfigList && Array.isArray(row.targetConfigList)) {
          row.targetConfigList.forEach((value, index) => {
            if (!summaryRow.targetConfigList[index]) {
              summaryRow.targetConfigList[index] = 0;
            }
            summaryRow.targetConfigList[index] += Number(value) || 0;
          });
        }
      });

      return summaryRow;
    },
    // 合并单元格方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 如果是合计行
      if (row.isSummaryRow) {
        // 合并前两列（部门和分校长）
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 2
          };
        } else if (columnIndex === 1) {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    comeBack() {
      this.$router.go(-1);
    },
    headerStyle() {
      return 'tableStyle';
    },
    // 表格行样式
    tableRowClassName({ row, rowIndex }) {
      if (row.isSummaryRow) {
        return 'summary-row';
      }
      return '';
    },
    // 处理表格滚动同步
    handleTableScroll(sourceScroller, targetScroller) {
      targetScroller.scrollLeft = sourceScroller.scrollLeft;
    },
    // 设置表格滚动监听
    setupScrollSync() {
      this.$nextTick(() => {
        const firstTableBody = document.querySelector('.first-table .el-table__body-wrapper');
        const secondTableBody = document.querySelector('.second-table .el-table__body-wrapper');

        if (firstTableBody && secondTableBody) {
          // 保存事件处理函数的引用
          this.firstHandler = throttle(() => this.handleTableScroll(firstTableBody, secondTableBody), 50);
          this.secondHandler = throttle(() => this.handleTableScroll(secondTableBody, firstTableBody), 50);

          // 添加事件监听
          firstTableBody.addEventListener('scroll', this.firstHandler);
          secondTableBody.addEventListener('scroll', this.secondHandler);
        }
      });
    },
    // 移除表格滚动监听
    removeScrollSync() {
      const firstTableBody = document.querySelector('.first-table .el-table__body-wrapper');
      const secondTableBody = document.querySelector('.second-table .el-table__body-wrapper');

      if (firstTableBody && secondTableBody) {
        firstTableBody.removeEventListener('scroll', this.firstHandler);
        secondTableBody.removeEventListener('scroll', this.secondHandler);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
/* 定义表格渐变背景颜色变量 */
$first-table-default-bg-color: linear-gradient(270deg, #2D2044 0%, #753B52 100%); // 第一个表格默认背景颜色
$first-table-hover-bg-color: linear-gradient(90deg, #A5505D 0%, #462949 100%); // 第一个表格鼠标悬停背景颜色
$second-table-default-bg-color: linear-gradient(270deg, #251E49 0%, #3A4D8C 100%); // 第二个表格默认背景颜色
$second-table-hover-bg-color: linear-gradient(90deg, #486AB8 0%, #2F3569 100%); // 第二个表格鼠标悬停背景颜色
/* 定义表格滚动条高度和表头底部间距 */
$table-header-margin-bottom: 5px; // 表头底部间距
$sticky-plugin-scroll-height: 10px; // @cell-x/el-table-sticky插件滚动条高度

.yz-base-container {
  min-height: 100vh;
  background-color: #180E36;
  color: #fff;
  padding: 10px;
}

.header {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
  align-items: center;

  .l {
    padding-left: 50px;

    .el-button {
      padding: 10px 30px;
    }

    .pkDetails {
      color: #fff;
      background: radial-gradient(rgba(95, 153, 254, 0.4) 0%, rgba(73, 119, 254, 0.8) 100%);
    }
  }

  .center {
    flex: 1;
    text-align: center;

    background: linear-gradient(180deg, #FAEEE3 0%, #D9B271 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;

    font-family: PingFangSC, PingFang SC !important;
    font-size: 36px;
    font-style: normal;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .r {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;

    .refresh {
      width: 60px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
      background-image: radial-gradient(rgba(124, 203, 0, 0.3) 0%, rgba(124, 203, 0, 0.5) 100%);
      border-radius: 2px;
      border: 1px solid rgba(124, 203, 0, 0.6);
      font-size: 12px;
    }

    .text {
      margin-top: 10px;
      font-size: 14px;
    }
  }
}

.countdown-container {
  margin-bottom: 15px;
}

.connect {
  width: 100%;
  // height: 88vh;
  /* border: 1px solid #fff; */
  padding: 0 20px;
}

/* 内容块样式 */
.block {
  background: rgba(255, 255, 255, .05);
  padding-bottom: 20px;
  border-radius: 5px;
}

/* 表格容器样式 */
.table-box {
  padding: 0 20px;

  .first-table {
    margin-bottom: 1px;
  }
}

/* 链接样式 */
.link {
  color: #fff;

  &:hover {
    color: #fff;
  }

  &:hover:after {
    position: absolute;
    left: 0;
    right: 0;
    height: 0;
    bottom: 0;
    border-bottom: 1px solid #fff;
  }
}

/* 箭头图标样式 */
.icon-arrow-right {
  width: 3px;
  height: 6px;
  background: url("~@/assets/imgs/helpStudyPkRank/icon-arrow-right.png") no-repeat;
  background-size: 100% 100%;
  display: inline-block;
  margin-left: 6px;
}

.el-avatar {
  background: #fff;
}

/* 表格样式 */
/* 表格表头行样式 */
::v-deep .tableStyle {
  background-color: #4731A6FF;
  color: #fff;
  font-weight: 400;
}

/* 表格基础样式 */
::v-deep .el-table,
::v-deep .el-table__expanded-cell {
  background-color: #241a40;
}

::v-deep .el-table td {
  border: 1px solid rgba(255, 255, 255, 0.1);
}

::v-deep .el-table th.is-leaf {
  border: none;
}

::v-deep .el-table td {
  border-right: none;
}

::v-deep .el-table th,
::v-deep .el-table tr {
  background-color: transparent;
}

/* 表格表头样式 */
::v-deep .el-table__header-wrapper {
  border-top: none !important; // 处理粘性插件的边框
  border-bottom: $table-header-margin-bottom solid transparent;
  border-radius: 2px;
}

::v-deep .el-table__body-wrapper {
  border-radius: 2px 2px 0 0;
}

::v-deep .el-table__footer-wrapper {
  border-radius: 0 0 2px 2px;
  margin-top: 0px;
}

/* 表格表头单元格样式 */
::v-deep .table-cell-header {
  background-color: #4731a6 !important; // 要不然左右滚动颜色就会穿透
  color: #fff !important;
}

/* 表格行基础样式 */
::v-deep .el-table__row {
  background-color: transparent;
  background-image: linear-gradient(90deg, rgba(#5F99FEFF, .5) 20%, rgba(#4977FEFF, .2) 100%);
  color: #fff;
}

/* 合计行特殊样式 */
::v-deep .el-table__row.summary-row {
  background: linear-gradient(90deg, #364483 50%, #28285d 100%) !important;
  background-image: linear-gradient(90deg, #364483 50%, #28285d 100%) !important;
  font-weight: bold;
}

/* 第一个表格的合计行 */
.first-table ::v-deep .el-table__row.summary-row {
  background: linear-gradient(270deg, #985868 0%, #A6505D 100%) !important;
  background-image: linear-gradient(270deg, #985868 0%, #A6505D 100%) !important;
}

/* 第二个表格的合计行 */
.second-table ::v-deep .el-table__row.summary-row {
  background: linear-gradient(90deg, #486CBA 0%, #3C54AC 100%) !important;
  background-image: linear-gradient(90deg, #486CBA 0%, #3C54AC 100%) !important;
}

/* 表格通用悬停样式 */
::v-deep .el-table__body tr.hover-row>td.el-table__cell {
  background: transparent !important;
  color: #fff;
}

/* 表格固定列样式 */
::v-deep .el-table__fixed-footer-wrapper tbody td.el-table__cell {
  border-top: none;
}

/* 修复固定表格底部白边 */
::v-deep .el-table__fixed::before {
  height: 0;
}

/* 滚动条样式 - @cell-x/el-table-sticky插件 */
::v-deep .gm-scrollbar {
  background-color: rgba(255, 255, 255, 0.1);

  .thumb {
    background-color: rgba(255, 255, 255, 0.3);

    &:active,
    &:hover {
      background-color: rgba(255, 255, 255, 0.3) !important;
    }
  }
}

/* 修复表格sticky插件影响固定表格高度没有一致 */
::v-deep .el-table--scrollable-x .el-table__fixed-footer-wrapper {
  bottom: $sticky-plugin-scroll-height + 4px;
}

/* 修复表格sticky底部合计合并俩个单元格, 导致固定的单元格不一致 */
::v-deep .el-table__footer-wrapper tbody .el-table__cell:nth-child(2) .cell {
  visibility: visible;
}

/* 表格特殊列颜色 */
::v-deep .el-table__footer-wrapper tr td:nth-last-child(3) {
  color: #FFD387FF !important;
}

/* 表格助学积分、助学人数、活动人均列特殊颜色 */
.score-column-color {
  color: #FFD387FF;
}

/* 表格页脚样式 */
::v-deep .el-table__footer tr {
  background: linear-gradient(90deg, #364483 50%, #28285d 100%);
}

::v-deep .el-table__footer-wrapper tbody td,
::v-deep .el-table__header-wrapper tbody td {
  background-color: transparent;
  color: #fff;
}

/* 表格边框样式 */
::v-deep .el-table--border,
::v-deep .el-table--group {
  border: none;
}

::v-deep .el-table--border::after,
::v-deep .el-table--group::after,
::v-deep .el-table::before {
  background-color: transparent;
}

::v-deep .el-table--border th,
::v-deep .el-table__fixed-right-patch {
  border: none;
}

::v-deep .el-table--border td,
::v-deep .el-table--border th,
::v-deep .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
  border-right: none;
}

/* 第一个表格特殊样式 */
.first-table {

  /* 表格行样式 */
  ::v-deep .el-table__row {
    background: $first-table-default-bg-color;
  }

  /* 表格页脚样式 */
  ::v-deep .el-table__footer tr {
    background: $first-table-hover-bg-color;
  }

  ::v-deep .el-table__footer tr td:nth-child(1) {
    color: #fff;
    background: linear-gradient(270deg, #985868 0%, #A6505D 100%) !important;
  }

  /* 表格行悬停效果 */
  ::v-deep .el-table__body tr.hover-row,
  ::v-deep .el-table__row:hover {
    background: $first-table-hover-bg-color;
  }

  ::v-deep .el-table__row:hover td {
    background: transparent !important;
    background-color: transparent !important;
  }
}

/* 第二个表格特殊样式 */
.second-table {

  /* 隐藏表头 */
  ::v-deep .el-table__header {
    display: none;
  }

  /* 表格行样式 */
  ::v-deep .el-table__row {
    background: $second-table-default-bg-color;
  }

  /* 表格页脚样式 */
  ::v-deep .el-table__footer tr {
    background: $second-table-hover-bg-color;
  }

  ::v-deep .el-table__footer tr td:nth-child(1) {
    color: #fff;
    background: linear-gradient(90deg, #486CBA 0%, #3C54AC 100%) !important;
  }

  /* 表格行悬停效果 */
  ::v-deep .el-table__body tr.hover-row,
  ::v-deep .el-table__row:hover {
    background: $second-table-hover-bg-color;
  }

  ::v-deep .el-table__row:hover td {
    background: transparent !important;
    background-color: transparent !important;
  }

  ::v-deep .el-table__row:hover {
    background-image: linear-gradient(90deg, rgba(#5F99FEFF, .6) 40%, rgba(#4977FEFF, .2) 100%);
  }
}
</style>
