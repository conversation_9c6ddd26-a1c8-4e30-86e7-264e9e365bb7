<template>

  <div>
    <common-dialog
      class="common-dialog"
      width="1000px"
      title="筛选或导入"
      :visible.sync="show"
      :remindId='remindId'
      @open="open"
      @close="close"
    >
      <div class="dialog-main"></div>
      <!-- 表单 -->
      <el-form
        ref="searchForm"
        size="mini"
        label-width="120px"
        class="yz-search-form"
        :model="form"
        @submit.native.prevent="search"
      >
        <el-form-item label="是否选中" prop="isChecked">
          <el-select v-model="form.isChecked" clearable placeholder="请选择" filterable>
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="报考层次" prop="pfsnLevel">
          <el-select v-model="form.pfsnLevel" clearable placeholder="请选择" filterable>
            <el-option
              v-for="item in pfsnLevelList"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="年级" prop="grade">
          <el-select v-model="form.grade" clearable placeholder="请选择" filterable>
            <el-option
              v-for="item in gradeList"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="院校" prop="unvsId">
          <el-select v-model="form.unvsId" clearable placeholder="请选择" filterable>
            <el-option
              v-for="item in unvsList"
              :key="item.unvsId"
              :label="item.unvsName"
              :value="item.unvsId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="专业" prop="pfsnId">
          <el-select v-model="form.pfsnId" clearable placeholder="请选择" filterable>
            <el-option
              v-for="item in pfsnsList"
              :key="item.pfsnId"
              :label="item.pfsnName"
              :value="item.pfsnId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="考区" prop="taId">
          <el-select v-model="form.taId" clearable placeholder="请选择" filterable>
            <el-option
              v-for="item in sTaList"
              :key="item.taId"
              :label="item.taName"
              :value="item.taId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学生姓名" prop="stdName">
          <el-input v-model="form.stdName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="证件号码" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="班主任" prop="tutorName">
          <el-input v-model="form.tutorName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="招生类型" prop="recruitType">
          <el-select v-model="form.recruitType" clearable placeholder="请选择" filterable>
            <el-option
              v-for="item in recruitTypeList"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="报读时间起" prop="enrollStartTime">
          <el-date-picker
            v-model="form.enrollStartTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="选择日期时间"
          />
        </el-form-item>
        <el-form-item label="报读时间止" prop="enrollEndTime">
          <el-date-picker
            v-model="form.enrollEndTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="选择日期时间"
          />
        </el-form-item>
        <el-form-item label="学业编码" prop="learnId">
          <el-input v-model="form.learnId" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="远智编码" prop="yzCode">
          <el-input v-model="form.yzCode" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="跟进人" prop="followerName">
          <el-input v-model="form.followerName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="协作人" prop="coordinationName">
          <el-input v-model="form.coordinationName" placeholder="请输入" />
        </el-form-item>
        <div class="search-reset-box " style="margin-right: 10px; ">
          <el-button
            type="primary"
            icon="el-icon-search"
            native-type="submit"
            size="mini"
          >搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
        </div>
        <!-- 勾选区 -->
        <div class="yz-table-select">
          <div class="select-title">学员阶段</div>
          <el-checkbox-group v-model="checkStatusList">
            <el-checkbox v-for="item in stuStatusList" :key="item.value" :label="item.value" :value="item.value">{{ item.key }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form>
      <!-- 按钮区 -->
      <div class="yz-table-btnbox" style="margin-right: 10px;">
        <el-button
          type="primary"
          size="small"
          @click="addSelect()"
        >添加选中</el-button>
        <el-button
          type="warning"
          size="small"
          @click="deleteSelect()"
        >清除选中</el-button>
        <el-button
          type="primary"
          size="small"
          @click="addAll()"
        >添加全部</el-button>
        <el-button
          type="danger"
          size="small"
          @click="deleteAll()"
        >删除全部</el-button>
        <el-button
          type="primary"
          size="small"
          @click="importStudents()"
        >目标学员导入</el-button>
      </div>
      <!-- 表格 -->
      <el-table
        ref="table"
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%;"
        header-cell-class-name="table-cell-header"
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          align="center"
          type="selection"
          width="50"
        />
        <el-table-column
          label="是否目标学员"
          prop="taskId"
          align="center"
        >
          <template v-slot="scope">
            <div v-if="scope.row.remindId">是</div>
            <div v-else>否</div>
          </template>
        </el-table-column>
        <el-table-column prop="learnId" label="学业编码" align="center" />
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="stdName" label="学员姓名" align="center" />
        <el-table-column prop="grade" label="年级" align="center" />
        <el-table-column prop="recruitType" label="招生类型" align="center" />

        <el-table-column
          label="院校专业"
          width="220"
          align="center"
        >
          <template v-slot="scope">
            <div>
              {{ scope.row.unvsName }};{{ scope.row.pfsnName }}[{{ scope.row.pfsnLevel }}]({{ scope.row.grade }}级)
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="stdStage" label="学员阶段" align="center" />
        <!-- <el-table-column prop="followerName" label="跟进人" align="center" />
        <el-table-column prop="coordinationName" label="协作人" align="center" /> -->
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination" style="margin-bottom: 10px">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getRemindOrdinaryStudentList"
        />
      </div>
    </common-dialog>
    <excel-import :visible.sync="importShow" :remindId='remindId' @closeExcelImport="closeExcelImport" />
  </div>
</template>

<script>
import excelImport from './importStudentMessage.vue';

const form = {
  remindId: '', // 消息id
  isChecked: '', // 是否选中
  pfsnLevel: '', // 专业层次
  grade: '', // 年级
  unvsId: '', // 院校id
  pfsnId: '', //  专业id
  taId: '', // 考区
  stdName: '', // 学生姓名
  idCard: '', // 证件
  mobile: '', // 手机号
  tutorName: '', // 班主任
  recruitType: '', // 招生类型
  learnId: '', // 学业id
  yzCode: '', // 远智编码
  enrollStartTime: '', // 报读时间起
  enrollEndTime: '', // 报读时间止
  stdStages: '', // 学员状态集合
  followerName: '',
  coordinationName: ''
};

export default {
  components: {
    excelImport
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    remindId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      show: false,
      form: form,
      tableLoading: false,
      importShow: false,
      tableData: [],
      // taskList: [],
      gradeList: [], // 年级
      // pfsnLevel: [], // 报考层次
      unvsList: [], // 院校
      pfsnsList: [], // 专业
      sTaList: [], // 考区
      pfsnLevelList: [], // 报考层次
      recruitTypeList: [], // 招生类型
      stuStatusList: [], // 已勾选学员状态列表
      checkStatusList: [], // 学员状态可选列表
      checkStudentList: {
        remindId: '',
        taskId: '',
        learnIds: ''
      }, // 已勾选学员数据
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    'form.unvsId'(val) {
      this.form.pfsnId = '';
      this.getPfsnsList();
    }
  },
  provide() {
    return {
      parentVm: this
    };
  },
  methods: {
    open() {
      this.form.remindId = this.remindId;
      this.initData();
    },
    closeExcelImport() {
      this.importShow = false;
      this.initData();
    },
    initData() {
      // this.getTaskList();
      this.getGradeList();
      this.getPfsnLevelList();
      this.getUnvsList();
      this.getPfsnsList();
      this.getSTaList();
      this.getRecruitTypeList();

      this.getStuStatusList();
      this.getRemindOrdinaryStudentList();
    },
    getCampusList() {
      this.$post('getCampusList').then((res) => {
        if (res.code === '00') {
          this.campusList = res.body;
        }
      });
    },
    close() {
      this.stuStatusList = [];
      this.checkStatusList = [];
      this.tableData = [];
      this.$refs['searchForm'].resetFields();
      // this.$emit('update:visible', false);
      // this.$emit('close');

      this.$emit('closeFilterOrImport');
    },
    handleQUeryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    getGradeList() {
      this.$post('getDictInfoList', { name: 'grade' }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.gradeList = body;
        }
      });
    },
    getRecruitTypeList() {
      this.$post('getDictInfoList', { name: 'recruitType' }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.recruitTypeList = body;
        }
      });
    },
    getPfsnLevelList() {
      this.$post('getDictInfoList', { name: 'pfsnLevel' }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.pfsnLevelList = body;
        }
      });
    },
    getUnvsList() {
      this.$post('getUnvsList', { page: 1, rows: 999 }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.unvsList = body.data;
        }
      });
    },
    getPfsnsList() {
      this.$post('getPfsnsList', { page: 1, rows: 999, sId: this.form.unvsId }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.pfsnsList = body.data;
        }
      });
    },
    getSTaList() {
      this.$post('getSTaList', { page: 1, rows: 999 }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.sTaList = body.data;
        }
      });
    },
    // 获取学业状态列表
    getStuStatusList() {
      this.$post('getDictInfoList', { name: 'stdStage' }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.stuStatusList = body;
        }
      });
    },
    getRemindOrdinaryStudentList() {
      this.tableLoading = true;
      let stdStages = '';
      for (let i = 0; i < this.checkStatusList.length; i++) {
        if (i === 0) {
          stdStages = this.checkStatusList[i];
        } else {
          stdStages = stdStages + ',' + this.checkStatusList[i];
        }
      }
      this.form.stdStages = stdStages;
      const params = this.handleQUeryParams();
      this.$post('getRemindOrdinaryStudentList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getRemindOrdinaryStudentList();
      }
    },
    handleSelectionChange(val) {
      const rowData = val;
      this.checkStudentList.remindId = this.form.remindId;
      this.checkStudentList.taskId = this.form.taskId;
      let learnIds = '';
      for (let i = 0; i < rowData.length; i++) {
        if (i === 0) {
          learnIds = rowData[i].learnId.toString();
        } else {
          learnIds = learnIds + ',' + rowData[i].learnId.toString();
        }
      }
      this.checkStudentList.learnIds = learnIds;
    },
    addSelect() {
      if (this.checkStudentList.learnIds.length === 0) {
        this.$message({
          message: '请选择学员',
          type: 'warning'
        });
        return;
      }
      this.$post('addRemindTaskStudent', this.checkStudentList, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.$message({
            message: '添加选中成功！',
            type: 'success'
          });
          this.pagination.page = 1;
          this.getRemindOrdinaryStudentList();
        }
      });
    },
    deleteSelect() {
      if (this.checkStudentList.learnIds.length === 0) {
        this.$message({
          message: '请选择学员',
          type: 'warning'
        });
        return;
      }
      this.$post('delRemindStudent', this.checkStudentList, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.$message({
            message: '清除选中成功！',
            type: 'success'
          });
          this.pagination.page = 1;
          this.getRemindOrdinaryStudentList();
        }
      });
    },
    addAll() {
      this.$post('addAllRemindStudent', this.form, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.pagination.page = 1;
          this.$message({
            message: '添加全部成功！',
            type: 'success'
          });
          this.getRemindOrdinaryStudentList();
        }
      });
    },
    deleteAll() {
      this.$post('delAllRemindStudent', this.form, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.$message({
            message: '删除全部成功！',
            type: 'success'
          });
          this.pagination.page = 1;
          this.getRemindOrdinaryStudentList();
        }
      });
    },
    importStudents() {
      this.importShow = true;
    }
  }
};
</script>
<style lang = "scss" scoped>
.yz-table-select {
  display: flex;
  margin: 10px 0;
  // padding: 0 20px;
  .select-title {
    width: 120px;
    padding-right:  10px;
    text-align: right;
  }
}
</style>
