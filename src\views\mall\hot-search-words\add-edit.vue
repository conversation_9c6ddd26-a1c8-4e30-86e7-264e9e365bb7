<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    :title="`${ isEdit ? '修改' : '新增'}` + '热门搜索词配置'"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        size="small"
      >
        <el-form-item label="热门搜索词:" prop="content">
          <el-input v-model="form.content" placeholder="请输入热门搜索词" maxlength="8" show-word-limit />
        </el-form-item>

        <el-form-item label="展示时间:" required>
          <div style="display: flex;">
            <el-form-item prop="showTimeStatus">
              <el-radio-group v-model="form.showTimeStatus">
                <el-radio label="PERMANENT_EFFECTIVE">永久有效</el-radio>
                <el-radio label="TIME_EFFECTIVE">固定时间</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="form.showTimeStatus == 'TIME_EFFECTIVE'" class="ml10" prop="unveilTime">
              <el-date-picker
                v-model="form.unveilTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </div>
        </el-form-item>

        <el-form-item label="搜索词权重:" prop="weight">
          <div style="display: flex;">
            <el-input-number v-model="form.weight" :controls="false" :min="0" :max="9999" placeholder="请输入搜索词权重" style="width: 200px" />
            <span>（数字越大，排序越靠前）</span>
          </div>
        </el-form-item>

        <el-form-item label="状态:" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      confirmLoading: false,
      show: false,
      isEdit: false, // 是否编辑
      form: {
      },
      rules: {
        content: [
          { required: true, message: '请输入热门搜索词', trigger: 'blur' }
        ],
        wordsTimeRadio: [
          { required: true, message: '请选择展示时间', trigger: 'change' }
        ],
        wordsTime: [
          { required: true, message: '请选择展示时间', trigger: 'change' }
        ],
        weight: [
          { required: true, message: '请输入搜索词权重', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框
    open() {
      if (this.currentId) {
        this.isEdit = true;
        this.$http.get(`/mallHotSearchConfig/getById/${this.currentId}`).then(res => {
          const { fail, body } = res;
          if (!fail) {
            const {
              id,
              content,
              showTimeStatus,
              weight,
              status,
              showStartTime,
              showEndTime
            } = body;
            this.form = {
              id,
              content,
              showTimeStatus,
              weight,
              status,
              unveilTime: showTimeStatus == 'TIME_EFFECTIVE' ? [showStartTime, showEndTime] : []
            };
          }
        });
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        this.confirmLoading = true;

        const form = JSON.parse(JSON.stringify(this.form));
        if (form.showTimeStatus == 'TIME_EFFECTIVE') {
          form.showStartTime = form.unveilTime[0];
          form.showEndTime = form.unveilTime[1];
        } else {
          delete form.showStartTime;
          delete form.showEndTime;
        }
        delete form.unveilTime;

        let apiKey = 'addZMHotSearchWords';
        if (this.isEdit) {
          apiKey = 'updateZMHotSearchWords';
        }
        this.$post(apiKey, form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
