<template>
  <common-dialog width="50%" class="add_target" :title="viTitle?'编辑目标':'添加目标'" :visible.sync="visible" :show-footer="true" @open="targetInit" @close="targetClose" @confirm="targetSubmit">
    <el-form ref="targetRef" class="add_target-main" size="mini" :model="targetFrom" :rules="targetRule" label-width="180px">
      <el-form-item label="目标设定：" prop="title">
        <el-input v-model="targetFrom.title" maxlength="10" show-word-limit />
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: { type: Boolean, default: false },
    viTitle: { type: String, default: '' }
  },
  data() {
    return {
      targetFrom: { title: '' },
      targetRule: { title: [{ required: true, message: '请输入', trigger: 'blur' }] }
    };
  },
  methods: {
    // 初始化
    targetInit() {
      this.targetFrom.title = this.viTitle;
    },
    // 关闭
    targetClose() {
      this.$emit('close', '');
    },
    // 添加
    targetSubmit() {
      this.$refs['targetRef'].validate((valid) => {
        if (valid) {
          this.$emit('close', this.targetFrom?.title);
        }
      });
    }
  }
};
</script>

<style lang="scss">
.add_target {
  &-main {
    width: 80%;
    margin-top: 80px;
    min-height: 240px;
  }
  .yz-common-dialog__footer {
    text-align: center;
  }
}
</style>

