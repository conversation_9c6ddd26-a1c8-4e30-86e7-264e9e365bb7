<template>
  <div class="reviews-container">
    <div class="main">
      <div class="directory" :style="leftStyle">
        <div class="left">
          <el-collapse
            v-show="rightShow"
            v-model="activeName"
            class="site inline-block"
            accordion
          >
            <el-collapse-item title="论文大纲" name="1">
              <div class="tree">
                <el-scrollbar style="height: 100%">
                  <el-tree
                    ref="tree"
                    :data="treeData"
                    node-key="id"
                    default-expand-all
                    :expand-on-click-node="false"
                  >
                    <div
                      slot-scope="{ node, data }"
                      class="custom-tree-node"
                    >
                      <span class="label">
                        {{ data.title }}
                      </span>
                    </div>
                  </el-tree>
                </el-scrollbar>
              </div>
            </el-collapse-item>
            <el-collapse-item title="老师点评建议" name="2">
              <div class="teacher-suggest">
                <el-scrollbar style="height: 100%">
                  <div class="suggest-content" v-html="suggest"></div>
                </el-scrollbar>
              </div>
            </el-collapse-item>
          </el-collapse>
          <div class="susp" @click="openSidebar">
            <div><i :class="rightShow ? 'el-icon-caret-left': 'el-icon-caret-right'"></i></div>
          </div>
        </div>
      </div>
      <div class="right" :style="rightStyle">
        <iframe
          :src="wordUrl"
          :style="{ width: rightStyle.width}"
        ></iframe>
      </div>
    </div>
  </div>
</template>

<script>
import { ossUri } from '@/config/request';
export default {
  components: {},
  data() {
    return {
      office: 'https://view.officeapps.live.com/op/view.aspx?src=',
      activeName: '1',
      treeData: [],
      wordUrl: null,
      learnId: null,
      taskId: null,
      paperUploadType: '',
      suggest: '',
      checkStatus: '',
      leftStyle: {
        width: '425px'
      },
      rightStyle: {
        width: '768px'
      },
      rightShow: true
    };
  },
  mounted() {
    this.learnId = this.$route.query.learnId;
    this.taskId = this.$route.query.taskId;
    this.paperUploadType = this.$route.query.paperUploadType;
    this.attachmentId = this.$route.query.attachmentId;
    this.checkStatus = this.$route.query.checkStatus;
    this.getParperInfo();
  },
  methods: {
    openSidebar() {
      this.rightShow = !this.rightShow;
      if (this.rightShow) {
        // this.leftStyle.width = '768px';
        this.leftStyle.width = '425px';
        this.rightStyle.width = '768px';
      } else {
        // this.leftStyle.width = '1200px';
        this.leftStyle.width = '30px';
        this.rightStyle.width = '1162px';
      }
    },
    getParperInfo() {
      const params = {
        learnId: this.learnId,
        taskId: this.taskId,
        paperUploadType: this.paperUploadType, // 0 是老师提交 其他是学生
        attachmentId: this.attachmentId,
        checkStatus: this.checkStatus
      };
      this.$http.post('/paper/getStudentOnlinePaper', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.wordUrl = this.office + ossUri + body.attachmentUrl + '?v=' + new Date().getTime();
            this.treeData = body.list;
            this.suggest = body.commentContent;
          }
        });
    }
  }
};
</script>

<style lang='scss' scoped>
.main {
  margin: 0 auto;
  min-width: 1200px;
  max-width: 1200px;
  padding-top: 52px;
  overflow: hidden;

  .directory {
    height: 90vh;
    display: inline-block;
    vertical-align: top;
  }

  .right {
    width: 768px;
    min-height: 90vh;
    display: inline-block;
    vertical-align: top;
    border: 1px solid #c9d8db;
    background: #ffffff;
    overflow: hidden;
    margin-left: 7px;

    iframe {
      width: 768px;
      min-height: 90vh;
      display: inline-block;
      vertical-align: top;
      background: #ffffff;
      border: none;
    }
  }

  .left {
    position: fixed;
    top: 52px;
    height: 90vh;
    display: inline-block;

    .site {
      width: 408px;
      height: 90vh;
      border: 1px solid #c9d8db;
      border-right: none;
      background: #ffffff;
      vertical-align: top;
      display: inline-block;
    }

    .tree, .teacher-suggest {
      height: calc(90vh - 127px);
      background: #F8F8F9;
    }

    ::v-deep .w-e-text-container {
      background: #F8F8F9;
    }

    ::v-deep .el-collapse {
      .el-collapse-item__header {
        height: 62px;
        line-height: 62px;
        font-size: 21px;
        font-weight: 600;
        color: #303133;
        padding: 0 10px;

        .el-icon-arrow-right:before {
          content: "\e791" !important;
        }
      }

      .el-collapse-item__content {
        padding-bottom: 0;
      }

    }

  }

  .susp {
    width: 17px;
    height: 90vh;
    display: inline-block;
    position: relative;
    border-left: 1px solid #c9d8db;

    div {
      width: 17px;
      height: 102px;
      line-height: 102px;
      text-align: center;
      background: url("../../assets/imgs/susp.png") no-repeat;
      background-size: 100% 100%;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      user-select: none;

      &:hover {
        background: url("../../assets/imgs/susp_h.png") no-repeat;
        background-size: 100% 100%;
        color: #fff;
      }
    }
  }
}

.inline-block {
  display: inline-block;
}

.suggest-content {
  padding: 16px 10px;
}

::v-deep .el-tree {
  background: #f8f8f9;
}

::v-deep .el-tree-node__expand-icon {
  color: initial;
  font-size: 14px;
}

::v-deep .el-tree-node__expand-icon.is-leaf {
  color: transparent;
}

::v-deep .el-tree-node__content {
  height: 46px;
  background: #F8F8F9;
}

::v-deep .custom-tree-node {
  font-size: 16px;
  color: #3A3B3D;
  font-weight: 600;
  padding: 15px 5px 15px 5px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .label {
    width: 340px;
    display: block;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
  }

  .menus {
    padding-right: 10px;
  }

  i {
    margin-left: 16px;
  }
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

</style>
