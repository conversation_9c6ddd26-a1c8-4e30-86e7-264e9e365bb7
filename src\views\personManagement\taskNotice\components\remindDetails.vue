<template>
  <div>
    <common-dialog
      is-full
      class="common-dialog"
      width="1100px"
      :title="optionsType"
      :visible.sync="show"
      :remindId="remindId"
      @open="open"
      @close="close"
    >
      <div class="yz-base-container">
        <el-steps direction="vertical" :active="2">
          <el-step title="第一步： 基础信息填写">
            <template slot="description">
              <div>
                <el-form ref="basicInfos" size='mini' :model="basicInfos" :rules="basicRules" label-width="135px" class="basic-info" :disabled="optionsType==='查看'">
                  <el-form-item label="类型" prop="type" style="width: 250px;">
                    <el-select v-model="basicInfos.type" placeholder="请选择类型" style="height: 40px" :disabled="remindId !== 0">
                      <el-option label="公司通知" value="1" />
                      <el-option label="学服通知" value="2" />
                      <el-option label="学服任务" value="3" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="标题" prop="sysRemindName" style="width: 580px;">
                    <el-input v-model="basicInfos.sysRemindName" placeholder="请输入标题" maxlength="40" />
                  </el-form-item>
                  <el-form-item label="简述" prop="sysRemindSketch">
                    <el-input v-model="basicInfos.sysRemindSketch" type="textarea" :rows="6" resize='none' placeholder="请输入简述" maxlength="249" show-word-limit />
                  </el-form-item>
                  <el-form-item label="内容" prop="sysRemindText">
                    <wang-editor ref="sysRemindText" v-model="basicInfos.sysRemindText" class="editor-color content" :content.sync="basicInfos.sysRemindText" />
                  </el-form-item>
                  <el-form-item label="附件" prop="annex">
                    <el-upload
                      v-loading="loading"
                      element-loading-text="正在上传中"
                      element-loading-spinner="el-icon-loading"
                      element-loading-background="rgba(0, 0, 0, 00)"
                      class="upload-demo"
                      :action="action"
                      :on-success="uploadSuccess"
                      :on-change="fileBeforeUpload"
                      :before-remove="beforeRemove"
                      multiple
                      :limit="1"
                      :on-exceed="handleExceed"
                      :file-list="fileList"
                    >
                      <el-button class="options-btn" type="primary">选择文件</el-button>
                      <span class="upload-tips">（温馨提示：上传文件大小需控制在100M以内）</span>
                    </el-upload>
                  </el-form-item>
                  <el-form-item>
                    <el-button v-if="remindId === 0 " class="basic-btn" type="primary" @click="saveBasicInfos('basicInfos')">下一步</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </template>
          </el-step>
          <el-step v-if="remindId" title="第二步： 人员选择及设置完善">
            <template slot="description">
              <el-form ref="moreInfos" size='mini' :model="moreInfos" :rules="moreRules" label-width="136px" class="more-info" :disabled="!remindId || optionsType==='查看'">
                <el-form-item v-if="basicInfos.type !== '1'" label="学员名单">
                  <div>
                    <el-button class="options-btn" type="success" style="margin-right: 20px;" @click="selectTaskImport">选择任务导入</el-button>
                    <el-button class="options-btn" type="primary" @click="filterOrImport">筛选或导入</el-button>
                    <span class="stu-tips">温馨提示：学员名单的确定为二选一。从【选择任务导入】或【筛选或导入】进行。</span>
                  </div>
                  <div v-if="taskList.length > 0" style="margin-top: 12px;">
                    <el-table :data="taskList" border style="width: 1480px">
                      <el-table-column type="index" label="序号" width="55" align="center" />
                      <el-table-column prop="taskName" label="任务名称" width="300" align="center" />
                      <el-table-column prop="nums" label="选中学员总数" width="120" align="center">
                        <template v-slot="scope">
                          <div class="options-box">
                            <div class="options-button" @click="checkStudentDetails(scope.row.taskId,'0')">{{ scope.row.followerTotal + scope.row.noFollowerTotal }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="followerTotal" width="170" label="有跟进人的学员人数" align="center">
                        <template v-slot="scope">
                          <div class="options-box">
                            <div class="options-button" @click="checkStudentDetails(scope.row.taskId,'1', 1)">{{ scope.row.followerTotal }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="noFollowerTotal" width="120" label="无跟进人学员" align="center">
                        <template v-slot="scope">
                          <div class="options-box">
                            <div class="options-button" @click="checkStudentDetails(scope.row.taskId,'2', 1)">{{ scope.row.noFollowerTotal }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="noFollowerTotal" width="150" label="有辅导员的学员数" align="center">
                        <template v-slot="scope">
                          <div class="options-box">
                            <div class="options-button" @click="checkStudentDetails(scope.row.taskId,'1', 2)">{{ scope.row.tutorTotal }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="noFollowerTotal" width="150" label="无辅导员的学员数" align="center">
                        <template v-slot="scope">
                          <div class="options-box">
                            <div class="options-button" @click="checkStudentDetails(scope.row.taskId,'2', 2)">{{ scope.row.noTutorTotal }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="noFollowerTotal" width="150" label="有协作人的学员数" align="center">
                        <template v-slot="scope">
                          <div class="options-box">
                            <div class="options-button" @click="checkStudentDetails(scope.row.taskId,'1', 3)">{{ scope.row.coordinatorTotal }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="noFollowerTotal" width="160" label="无协作人的学员数" align="center">
                        <template v-slot="scope">
                          <div class="options-box">
                            <div class="options-button" @click="checkStudentDetails(scope.row.taskId,'2', 3)">{{ scope.row.noCoordinatorTotal }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="100" align="center">
                        <template v-slot="scope">
                          <div class="options-box">
                            <div class="options-button options-underline" @click="clearTaskStudents(scope.row)">清空</div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                  <div v-if="bindTotal > 0" class="add-row">
                    <div @click="checkStudentDetails()">
                      <span class="add-tips">已添加</span>
                      <span class="stu-nums">{{ bindTotal }}学员</span>
                    </div>
                    <!-- <el-button class="delete-btn" type="danger" @click="clearStudents">清空</el-button> -->
                    <div class="delete-btn" @click="clearStudents">清空</div>
                  </div>
                </el-form-item>
                <el-form-item v-if="bindTotal > 0" label="同步给学员的话术" prop="sysRemindSpeech">
                  <!-- <wang-editor ref="sysRemindSpeech" v-model="moreInfos.sysRemindSpeech" class="editor-color content" :content.sync="moreInfos.sysRemindSpeech" /> -->
                  <el-input v-model="moreInfos.sysRemindSpeech" type="textarea" :rows="6" resize='none' placeholder="请输入简述" maxlength="249" show-word-limit />
                </el-form-item>
                <el-form-item v-if="bindTotal > 0" label="同步给学员的附件" prop="annex">
                  <el-upload
                    v-loading="loading"
                    element-loading-text="正在上传中"
                    element-loading-spinner="el-icon-loading"
                    element-loading-background="rgba(0, 0, 0, 00)"
                    class="upload-demo"
                    :action="action"
                    :on-success="uploadPictureCardSuccess"
                    :on-change="pictureBeforeUpload"
                    :before-remove="handleRemovePictureCard"
                    multiple
                    :limit="9"
                    :on-exceed="handleExceedPictureCard"
                    :file-list="pictureList"
                  >
                    <el-button class="options-btn" type="primary">选择文件</el-button>
                    <span class="upload-tips">（支持图片（JPG、PNG）、视频（MP4）、普通文件，请控制单个附件在10M以内，最多上传9个）</span>
                  </el-upload>
                </el-form-item>
                <el-form-item v-if="basicInfos.type=== '3'" label="信息收集" prop="isCollect" class="radio-class">
                  <el-radio v-model="moreInfos.isCollect" label="1">是</el-radio>
                  <el-radio v-model="moreInfos.isCollect" label="0">否</el-radio>
                </el-form-item>
                <el-form-item v-if="basicInfos.type=== '3' && moreInfos.isCollect==='1'" label="关键词" prop="keyWords" style="width: 580px;">
                  <!-- <el-input v-model="moreInfos.keyWords" placeholder="请输入关键词" maxlength="40" /> -->
                  <!-- 父盒子 -->
                  <div class="father_box" @click="clickKeyWords">
                    <!-- 生成的标签 -->
                    <div v-for="(item, index) in moreInfos.keyWordsArray" :key="index" class="spanbox">
                      <span class="tagspan">{{ item }}</span>
                      <i class="span_close" @click="removeTag(index, item)"></i>
                    </div>
                    <!-- 输入框 -->
                    <input
                      ref="inputTag"
                      v-model="currentval"
                      maxlength="15"
                      placeholder="最多新增10个关键词，每个关键词不能超过15个字！"
                      :style="inputStyle"
                      class="inputTag"
                      type="text"
                      @keyup.enter="addTags"
                    />
                  </div>
                </el-form-item>
                <el-form-item label="执行者" prop="selectdTree">
                  <el-button class="options-btn" type="primary" :disabled="bindType !== null || remindId === 0 || optionsType === '查看'" icon="el-icon-plus" @click="addPerformer">添加</el-button>
                  <el-button class="options-btn" type="primary" :disabled="bindType !== null || remindId === 0 || optionsType === '查看'" icon="el-icon-upload2" @click="importPerformer">导入</el-button>
                  <div v-if="selectdTree.length > 0 && bindTotal === 0" class="add-row">
                    <span class="add-tips">已添加</span>
                    <span class="stu-nums">{{ selectdTree.length }}人</span>
                    <div v-if="bindType === null" class="delete-btn" @click="clearPerformer">清空</div>
                  </div>
                  <div v-if="bindTotal > 0">
                    <el-checkbox-group v-model="checkList">
                      <div>
                        <el-checkbox :label="1" text-color="#606266" @change="handleCheckedChange(1)">
                          <span>跟进人（学员的跟进老师）</span>
                          <span v-if="checkList.includes(1)" style="color: #409EFF">已添加{{ followerEmpCount }}人</span>
                        </el-checkbox>
                      </div>
                      <div>
                        <el-checkbox :label="2" text-color="#606266" @change="handleCheckedChange(2)">
                          <span>辅导员（学员的班主任）</span>
                          <span v-if="checkList.includes(2)" style="color: #409EFF">已添加{{ tutorEmpCount }}人</span>
                        </el-checkbox>
                      </div>
                      <div>
                        <el-checkbox :label="3" text-color="#606266" @change="handleCheckedChange(3)">
                          <span>协作人（学生服务部的老师、运营部的老师、上进学社的老师）</span>
                          <span v-if="checkList.includes(3)" style="color: #409EFF">已添加{{ coordinatorEmpCount }}人</span>
                        </el-checkbox>
                      </div>
                    </el-checkbox-group>
                  </div>
                </el-form-item>
                <el-form-item v-if="basicInfos.type === '3'" label="截止时间" prop="endTime">
                  <el-date-picker
                    v-model="moreInfos.endTime"
                    :picker-options="pickerOptions"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="datetime"
                    placeholder="选择日期时间"
                  />
                </el-form-item>
                <el-form-item label="是否启用" prop="isStart" class="radio-class">
                  <el-radio v-model="moreInfos.isStart" label="1">是</el-radio>
                  <el-radio v-model="moreInfos.isStart" label="0">否</el-radio>
                </el-form-item>
                <el-form-item label="是否同步企业微信" prop="isSendWeChat" class="radio-class">
                  <el-radio v-model="moreInfos.isSendWeChat" label="1">是</el-radio>
                  <el-radio v-model="moreInfos.isSendWeChat" label="0">否</el-radio>
                </el-form-item>
              </el-form>
            </template>
          </el-step>
        </el-steps>
        <div v-if="remindId" class="confirm-box">
          <el-button class="save-btn" style="margin-right: 50px;" @click="cancelSubmit">取消</el-button>
          <el-button class="save-btn" type="primary" :disabled="optionsType === '查看' || !remindId" @click="confirmSubmit('moreInfos')">提交</el-button>
        </div>
      </div>
    </common-dialog>
    <performer-select :visible.sync="performerShow" :selectedlist="selectedlist" @handleCheckPerformer="handleCheckPerformer" />
    <select-task-import :visible.sync="selectTaskImportShow" :remindId="remindId" @closeSelectTaskImport="closeSelectTaskImport" />
    <filter-or-import :visible.sync="filterOrImportShow" :remindId="remindId" @closeFilterOrImport="closeFilterOrImport" />
    <student-details :visible.sync="studentDetailsShow" :checkStudentList="checkStudentList" />
    <ImportStudents :showInfo="showInfo" @showInfo="showInfo = false" @successInfo="successInfo"></ImportStudents>
  </div>
</template>

<script>
import moment from 'moment';

import wangEditor from '@/components/Editor/wang-editor';
import performerSelect from '../components/performerSelect.vue';
import selectTaskImport from '../components/selectTaskImport.vue';
import filterOrImport from '../components/filterOrImport.vue';
import studentDetails from '../components/studentDetails.vue';
import ImportStudents from '../components/ImportStudents.vue';
import { ossUri } from '@/config/request';

export default {
  components: {
    performerSelect,
    selectTaskImport,
    filterOrImport,
    studentDetails,
    ImportStudents
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    optionsType: {
      type: String,
      default: ''
    },
    remindId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      components: {
        wangEditor
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        }
      },
      show: false,
      loading: false,
      dialogImageUrl: '',
      dialogVisible: false,
      performerShow: false,
      selectTaskImportShow: false,
      filterOrImportShow: false,
      studentDetailsShow: false,
      uploadDisabled: false,
      basicInfos: {
        type: '',
        sysRemindType: '',
        sysChildrenType: '',
        sysRemindName: '',
        sysRemindSketch: '',
        sysRemindText: '',
        fileName: '',
        fileUrl: ''
      },
      taskList: [],
      moreInfos: {
        isStart: '1',
        isCollect: '0',
        isSendWeChat: '0',
        sysRemindSpeech: '',
        selectdTree: [],
        endTime: '',
        keyWords: '',
        keyWordsArray: [],
        sysRemindImgs: ''
      },
      action: '/sysRemind/upload.do',
      selectdTree: [],
      fileList: [],
      pictureList: [],
      selectedlist: [],
      checkStudentList: {
        remindId: '',
        taskId: '',
        empStatus: '',
        empType: ''
      },
      bindType: null, // 学员绑定类型  null 未绑定 1 普通导入 2 学服任务导入
      bindTotal: 0, // 绑定学员的人数
      followerEmpCount: 0, // 跟进人执行者数量
      tutorEmpCount: 0, // 辅导员执行者数量
      coordinatorEmpCount: 0, // 协作人执行者数量
      checkList: [],
      showInfo:false,
      // 输入框
      currentval: '',
      // tag
      TagsAll: ['标签1', '标签2'],
      // 输入框宽度
      inputLength: '',
      // 计算删除位置
      n: 0,
      basicRules: {
        type: [
          { required: true, pattern: '[^ \x22]+', message: '这是必填字段', trigger: 'blur' }
        ],
        sysRemindName: [
          { required: true, pattern: '[^ \x22]+', message: '这是必填字段', trigger: 'blur' }
        ],
        sysRemindSketch: [
          { required: true, pattern: '[^ \x22]+', message: '这是必填字段', trigger: 'blur' },
          { min: 10, message: '最少输入10个字符', trigger: 'blur' }
        ],
        sysRemindText: [
          { required: true, pattern: '[^ \x22]+', message: '这是必填字段', trigger: 'blur' }
        ]
      },
      moreRules: {
        isStart: [
          { required: true, pattern: '[^ \x22]+', message: '这是必填字段', trigger: 'blur' }
        ],
        isCollect: [
          { required: true, pattern: '[^ \x22]+', message: '这是必填字段', trigger: 'blur' }

        ],
        isSendWeChat: [
          { required: true, pattern: '[^ \x22]+', message: '这是必填字段', trigger: 'blur' }
        ],
        sysRemindSpeech: [
          { required: true, pattern: '[^ \x22]+', message: '这是必填字段', trigger: 'blur' }
        ],
        selectdTree: [
          { required: true, pattern: '[^ \x22]+', message: '请选择执行者', trigger: 'blur' }
        ],
        endTime: [
          { required: true, pattern: '[^ \x22]+', message: '请选择截止时间 ', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    // 计算属性：计算出动态输入框宽度
    inputStyle() {
      const style = {};
      style.width = `${this.inputLength}px`;
      return style;
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    bindTotal(val) {
      if (val === 0) this.uploadDisabled = false;
    },
    'basicInfos.type'(val) {
      if (val === '1') {
        this.basicInfos.sysRemindType = '1';
        this.basicInfos.sysChildrenType = '1';
      } else if (val === '2') {
        this.basicInfos.sysRemindType = '1';
        this.basicInfos.sysChildrenType = '2';
      } else if (val === '3') {
        this.basicInfos.sysRemindType = '2';
        this.basicInfos.sysChildrenType = '2';
      }
    },
    // 监听输入的值越多，输入框越长
    currentval(val) {
      // 实时改变input输入框宽度，防止输入内容超出input默认宽度显示不全
      this.inputLength = this.$refs.inputTag.value.length * 12 + 50;
    },
    'moreInfos.keyWordsArray'(val) {
      this.moreInfos.keyWords = '';
      for (let i = 0; i < val.length; i++) {
        if (i === 0) {
          this.moreInfos.keyWords = val[i].toString();
        } else {
          this.moreInfos.keyWords = this.moreInfos.keyWords + ',' + val[i].toString();
        }
      }
    }
  },
  methods: {
    // 点击叉叉删除tag
    removeTag(index, item) {
      this.moreInfos.keyWordsArray.splice(index, 1);
    },

    // 回车增加tag
    addTags() {
      if (this.currentval.length === 0) {
        this.$message({
          message: '请输入关键词内容！',
          type: 'warning'
        });
        return;
      }
      if (this.moreInfos.keyWordsArray.length === 10) {
        this.$message({
          message: '最多只能10个标签关键词！',
          type: 'warning'
        });
        return;
      }
      this.moreInfos.keyWordsArray.push(this.currentval);
      // 清空输入框
      this.currentval = '';
    },
    // 键盘删除键删除tag
    // deleteTags() {
    //   // 逻辑：当删除到最后一个字符的时候，删除后currentval为空，所以继续执行，n++。这时候n=1.然后判断n是否等于2，不等于不执行。
    //   // 这里是保证当你删除最后一个字符的时候不会执行删除tag的方法，当我们删完了字符后再按一次删除的时候，n就等于2了。就开始删除tag。
    //   // 当有多个tag时，我们连续删除，就会出现，因为currentval为空，所以一直执行n++，导致n不等于2了，所以没法删除后面的tag。
    //   // 所以增加判断，当n大于2的时候我们看tag的长度有没有，有那就继续删除，没有就归0，从来。
    //   if (this.currentval === '') {
    //     this.n++;
    //     if (this.n === 2) {
    //       this.moreInfos.keyWordsArray.pop();
    //     }
    //     if (this.n > 2) {
    //       if (this.moreInfos.keyWordsArray.length) {
    //         this.moreInfos.keyWordsArray.pop();
    //       } else {
    //         this.n = 0;
    //       }
    //     }
    //   } else {
    //     this.n = 0;
    //   }
    // },
    // 点击父盒子输入框获取焦点
    clickKeyWords() {
      this.$nextTick(() => {
        this.$refs.inputTag.focus();
      });
    },
    open() {
      this.checkList = [];
      if (this.optionsType !== '添加') {
        this.getPaperDatas();
      } else {
        this.checkList.push(1);
      }
    },
    async getPaperDatas() {
      this.initData();
      await this.getRemindStudentTotal();
      await this.getRemindDetail();
    },
    closeSelectTaskImport() {
      this.selectTaskImportShow = false;
      this.getPaperDatas();
    },
    closeFilterOrImport() {
      this.filterOrImportShow = false;
      this.getPaperDatas();
    },
    close() {
      this.uploadDisabled = false;
      this.initData();
      this.$emit('update:visible', false);
      this.$emit('close');
      this.$emit('getTableList');
    },
    initData() {
      this.bindTotal = 0;
      this.selectdTree = [];
      this.selectedlist = [];
      this.$data.basicInfos = this.$options.data().basicInfos;
      this.$data.moreInfos = this.$options.data().moreInfos;
      this.fileList = [];
      this.pictureList = [];
      this.taskList = [];
    },
    successInfo(list){
      this.selectdTree = [];
      this.selectedlist = list;
      this.selectedlist.forEach((item, index) => {
        const selectPerson = {};
        selectPerson.empId = item.empId;
        selectPerson.empName = item.empName;
        this.selectdTree[index] = selectPerson;
      });
    },
    handleCheckPerformer(selectedlist) {
      this.selectdTree = [];
      this.selectedlist = selectedlist;
      this.selectedlist.forEach((item, index) => {
        const selectPerson = {};
        selectPerson.empId = item.id;
        selectPerson.empName = item.title;
        this.selectdTree[index] = selectPerson;
      });
    },
    handleQUeryParams() {
      for (let i = 0; i < this.pictureList.length; i++) {
        if (i === 0) {
          this.moreInfos.sysRemindImgs = this.pictureList[i].fileUrl.toString();
        } else {
          this.moreInfos.sysRemindImgs = this.moreInfos.sysRemindImgs + ',' + this.pictureList[i].fileUrl.toString();
        }
      }
      this.moreInfos.selectdTree = JSON.stringify(this.selectdTree);
      const basicInfos = JSON.parse(JSON.stringify(this.basicInfos));
      const moreInfos = JSON.parse(JSON.stringify(this.moreInfos));
      return {
        id: this.remindId,
        ...basicInfos,
        ...moreInfos,
        empTypes: this.checkList
      };
    },
    getRemindDetail() {
      this.$post('getRemindDetail', { id: this.remindId }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.handleType(body.sysRemindType, body.sysChildrenType);
          this.basicInfos.sysRemindSketch = body.sysRemindSketch;
          this.basicInfos.sysRemindName = body.sysRemindName;
          this.basicInfos.sysChildrenType = body.sysChildrenType;
          this.basicInfos.sysRemindType = body.sysRemindType;
          this.selectdTree = body.selectdTreeArray || [];
          this.selectdTree.forEach((item, index) => {
            const selectPerson = {};
            selectPerson.id = item.empId;
            selectPerson.title = item.empName;
            this.selectedlist[index] = selectPerson;
          });
          if (body.isSendWeChat !== null) {
            this.moreInfos.isSendWeChat = body.isSendWeChat.toString() || '0';
          }
          if (body.isStart !== null) {
            this.moreInfos.isStart = body.isStart.toString() || '1';
          }
          if (body.isCollect !== null) {
            this.moreInfos.isCollect = body.isCollect.toString() || '1';
          }
          if (body.endTime) {
            this.moreInfos.endTime = moment(body.endTime).format('YYYY-MM-DD HH:mm:ss') || '';
          }
          this.moreInfos.keyWords = body.keyWords;
          this.$refs['sysRemindText'].setContent(body.sysRemindText || '');
          if (this.optionsType === '查看') {
            this.$refs['sysRemindText'].editor.disable();
          }
          if (body.sysRemindSpeech) {
            this.moreInfos.sysRemindSpeech = body.sysRemindSpeech;
          }
          if (body.keyWords) {
            // 关键词回显
            this.moreInfos.keyWordsArray = body.keyWords.split(',');
          }
          // 已上传附件的回显
          if (body.fileName) {
            const file = {
              name: body.fileName,
              url: ossUri + body.fileUrl
            };
            this.fileList.push(file);
            this.basicInfos.fileUrl = body.fileUrl;
            this.basicInfos.fileName = body.fileName;
          }
          // 已上传的照片的回显
          if (body.sysRemindImgs) {
            const temp = body.sysRemindImgs.split(',');
            for (let i = 0; i < temp.length; i++) {
              const num = temp[i].lastIndexOf('/') + 1;
              const file = {
                name: temp[i].substring(num),
                url: ossUri + temp[i],
                fileUrl: temp[i]
              };
              this.pictureList.push(file);
            }
            if (this.pictureList.length >= 9) {
              this.uploadDisabled = true;
            }
          }

          if (body.empTypeList) {
            this.followerEmpCount = body.empTypeList[0].totalCount;
            this.tutorEmpCount = body.empTypeList[1].totalCount;
            this.coordinatorEmpCount = body.empTypeList[2].totalCount;
            body.empTypeList.map((item) => {
              if (item.isSelect) {
                this.checkList.push(item.empType);
              }
            });
          }
        }
      });
    },
    // 后端接口返回的类型需要处理一下才可用,后端老师说是上一个版本需求拓展性没留好导致
    handleType(sysRemindType, sysChildrenType) {
      if (sysRemindType === 1) {
        if (sysChildrenType === 1) {
          this.basicInfos.type = '1';
        } else {
          this.basicInfos.type = '2';
        }
      } else {
        this.basicInfos.type = '3';
      }
    },
    saveBasicInfos(basicInfos) {
      this.$refs[basicInfos].validate((valid) => {
        if (valid) {
          this.addRemind();
        } else {
          return false;
        }
      });
    },
    fileBeforeUpload(file, fileList) {
      this.fileList = fileList;
      const isLt100M = file.size / 1024 / 1024 > 100;
      if (isLt100M) {
        this.$message.error('上传文件大小不能超过100MB!');
        const currIdx = this.fileList.indexOf(file);
        this.fileList.splice(currIdx, 1);
        return;
      }
      this.loading = true;
    },
    pictureBeforeUpload(file, pictureList) {
      this.pictureList = pictureList;
      // const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1);
      const isLt2M = file.size / 1024 / 1024 > 10;
      // const isRt5K = file.size / 1024 < 5;
      // const whiteList = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
      // const isSuffix = whiteList.indexOf(fileSuffix.toLowerCase()) === -1;
      // if (isSuffix) {
      //  this.$message.error('上传文件只能是 jpg、jpeg、png、gif、bmp格式');
      //  const currIdx = this.pictureList.indexOf(file);
      //  this.pictureList.splice(currIdx, 1);
      //  return;
      // }
      if (isLt2M) {
        this.$message.error('上传图片请控制在10M以内!');
        const currIdx = this.pictureList.indexOf(file);
        this.pictureList.splice(currIdx, 1);
        return;
      }
      if (this.pictureList.length >= 9) {
        this.uploadDisabled = true;
      }
    },
    uploadSuccess(response, file, fileList) {
      this.$nextTick(() => {
        this.loading = false;
      });
      if (response.code === '00') {
        this.basicInfos.fileName = response.body.fileName;
        this.basicInfos.fileUrl = response.body.fileUrl;
        this.$message({
          message: '附件上传成功',
          type: 'success'
        });
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择1个文件，本次已选择${files.length} 个文件`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleRemovePictureCard(file, pictureList) {
      this.uploadDisabled = false;
      this.pictureList.forEach((value, index, array) => {
        if (value.uid === file.uid) {
          array.splice(index, 1);
        }
      });
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    uploadPictureCardSuccess(response, file, pictureList) {
      if (response.code === '00') {
        file.fileUrl = response.body.fileUrl;
        this.pictureList.push(file);
        this.$message({
          message: '图片上传成功',
          type: 'success'
        });
      }
    },
    handleExceedPictureCard(files, fileList) {
      this.$message.warning('当前限制最多可上传9个文件');
    },
    checkRemindBindStudentType() {
      this.$post('getRemindBindStudentType', { remindId: this.remindId }, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.bindType = body;
        }
      });
    },
    selectTaskImport() {
      if (this.bindType === 1) {
        this.$message({
          message: '已筛选或导入！',
          type: 'warning'
        });
      } else {
        this.selectTaskImportShow = true;
      }
    },
    filterOrImport() {
      if (this.bindType === 2) {
        this.$message({
          message: '已选学服任务导入！',
          type: 'warning'
        });
      } else {
        this.filterOrImportShow = true;
      }
    },
    clearTaskStudents(row) {
      this.$confirm('您确认清空此学服任务的学员吗？', '清空提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$post('delRemindBindStudent', { remindId: this.remindId, taskId: row.taskId }, { json: true }).then((res) => {
          const { fail, body } = res;
          if (!fail) {
            this.$message({
              message: '清除成功！',
              type: 'success'
            });
            this.getPaperDatas();
          }
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消清除'
        });
      });
    },
    clearStudents() {
      this.$confirm('您确认清空所有学员名单吗？', '清空提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$post('delRemindBindStudent', { remindId: this.remindId }, { json: true }).then((res) => {
          const { fail, body } = res;
          if (!fail) {
            this.$message({
              message: '清除成功！',
              type: 'success'
            });
            this.getPaperDatas();
          }
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消清除'
        });
      });
    },
    addPerformer() {
      this.performerShow = true;
    },
    importPerformer(){
      this.showInfo = true    },
    clearPerformer() {
      this.selectdTree = [];
      this.selectedlist = [];
    },
    cancelSubmit() {
      this.close();
    },
    confirmSubmit(moreInfos) {
      if ((this.selectdTree.length === 0 && this.basicInfos.type === '1') || (this.basicInfos.type !== '1' && this.checkList.length === 0)) {
        this.$message({
          message: '请选择执行者！',
          type: 'warning'
        });
        return;
      }
      const params = this.handleQUeryParams();
      this.$refs[moreInfos].validate((valid) => {
        if (valid) {
          this.$post('editRemind', params, { json: true }).then((res) => {
            const { fail, body } = res;
            if (!fail) {
              this.$message({
                message: '提交成功！',
                type: 'success'
              });
              this.close();
            }
          });
        } else {
          return false;
        }
      });
    },
    addRemind() {
      if (this.loading) {
        this.$message({
          message: '附件还未上传完成!',
          type: 'warning'
        });
        return;
      }
      this.$post('addRemind', this.basicInfos, { json: true }).then((res) => {
        if (res.code === '00') {
          this.$emit('changeRemindId', res.body);
          this.$message({
            message: '保存成功',
            type: 'success'
          });
          this.checkRemindBindStudentType();
        }
      });
    },
    getRemindStudentTotal() {
      this.$post('getRemindStudentTotal', { remindId: this.remindId }, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.taskList = body.taskList;
          this.bindTotal = body.total;
        }
      });
      this.checkRemindBindStudentType();
    },
    // 查看学员详情
    checkStudentDetails(taskId, empStatus, empType) {
      // type  0 任务下所有学员  1 任务下有跟进的学员 2 任务下无跟进的学员
      this.checkStudentList.remindId = this.remindId;
      if (taskId) {
        this.checkStudentList.taskId = taskId;
        this.checkStudentList.empStatus = empStatus;
        this.checkStudentList.empType = empType;
      } else {
        this.checkStudentList.empStatus = '';
        this.checkStudentList.empType = '';
      }
      if (!empStatus) {
        this.checkStudentList.taskId = '';
      }
      this.studentDetailsShow = true;
    },
    handleCheckedChange(val) {
      if ((val === 1 && this.followerEmpCount !== 0) || (val === 2 && this.tutorEmpCount !== 0) || (val === 3 && this.coordinatorEmpCount !== 0)) {
        return;
      }
      this.$message.warning('执行人为空');
      const index = this.checkList.indexOf(val);
      setTimeout(() => {
        index > -1 && this.checkList.splice(index, 1);
      }, 800);
    }
  }
};
</script>

<style lang = "scss" scoped>
.yz-base-container{
  // width: 1000px;
  margin: 0 auto;

  .basic-info  {
    margin-top: 30px;
  }
  .more-info {
    margin-top: 30px;
  }
  .options-btn {
    height: 36px;
  }
  .delete-btn {
    height: 24px;
    font-size: 14px;
    padding: 0 7px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid #C3C3C3;
    font-size: 14px;
    color: #606266;
    line-height: 22px;
  }
  .delete-btn:hover {
    height: 24px;
    font-size: 14px;
    padding: 0 7px;
    height: 24px;
    border-radius: 4px;
    font-size: 14px;
    // color: #606266;
    border: none;
    background: #F56C6C;
    color: #FFFFFF;
    line-height: 24px;
  }
  .stu-tips {
    margin-left: 15px;
    font-size: 14px;
    color: #FF6E10;
    line-height: 36px;
  }
  .options-box {
    display: flex;
    justify-content: center;
    align-items: center;

    .options-button {
      font-size: 14px;
      color: #409EFF;
      cursor: pointer;
      line-height: 20px;
    }
    .options-underline {
      text-decoration: underline;
    }
    .options-button:hover {
      color:#FB1111;
    }
  }
  .upload-tips {
    font-size: 14px;
    color: #606266;
    line-height: 20px;
  }

  .add-row {
    margin-top: 12px;
    cursor: pointer;
    display: flex;
    .add-tips {
    font-size: 14px;
    margin-right: 5px;
    color: #409eff;
    line-height: 24px;
  }
  .stu-nums {
    margin-right: 10px;
    font-size: 14px;
    color: #409eff;
    line-height: 24px;
    font-weight: 600;
  }
  }

  .basic-btn {
    width: 120px;
    height: 40px;
    margin-bottom: 42px;
  }
  .confirm-box {
    text-align: center;
    .save-btn {
      width: 164px;
      height: 50px;
    }

  }
  /* 外层div */
.father_box {
  width: 600px;
  box-sizing: border-box;
  background-color: white;
  border: 1px solid #409EFF;
  border-radius: 4px;
  font-size: 12px;
  text-align: left;
  padding-left: 5px;
  word-wrap: break-word;
  overflow: hidden;
}
/* 标签 */
.spanbox {
  display: inline-block;
  font-size: 14px;
  margin: 3px 12px 3px 0;
  padding-right: 6px;
  background: rgba(64,158,255,0.1);
  border-radius: 4px;
  border: 1px solid rgba(64,158,255,0.5);
  // background-color: #ecf5ff;
  // border: 1px solid #e8eaec;
  // border-radius: 3px;
  // box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
}
/* 标签文字 */
.tagspan {
  height: 24px;
  line-height: 22px;
  max-width: 99%;
  position: relative;
  display: inline-block;
  padding-left: 10px;
  padding-right: 4px;
  color: #409EFF;
  font-size: 14px;
  opacity: 1;
  vertical-align: middle;
  overflow: hidden;
  transition: 0.25s linear;
}
/* tag的叉叉 */
.span_close {
  padding: 0 4px 0 4px;
  opacity: 1;
  -webkit-filter: none;
  filter: none;
  color: #409EFF;
  /* font-weight: 600; */
  cursor:pointer;
}
/* 鼠标经过叉叉 */
.span_close:hover{
  background-color: #409EFF;
  border-radius: 50%;
  color: #fff;
}
.span_close:after {
  content: "\00D7";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* line-height: 27px; */
  transition: 0.3s, color 0s;
}
/* input */
.inputTag {
  font-size: 14px;
  border: none;
  box-shadow: none;
  outline: none;
  background-color: transparent;
  padding: 0;
  width: auto;
  min-width: 300px;
  vertical-align: top;
  height: 38px;
  color: #1F1F1F;
  line-height: 38px;
}
/* 输入框提示文字大小 */
input:placeholder-shown {
  font-size: 0.6rem;
}

/* Chrome */
::-webkit-input-placeholder{
  color: #C0C4CC;
}
/* IE 10+ */
:-ms-input-placeholder{
  color: #C0C4CC;
}
/* Firefox 4-18 */
/* Firefox 19+ */
:-moz-placeholder,
::-moz-placeholder{
  color: #C0C4CC;
  opacity: 1;
}
}
::v-deep .yz-search-form .el-form-item {
  margin-top: 20px;
}

::v-deep .el-step__description.is-finish{
  color: #000;
}

// ::v-deep .el-dialog{
//   width:85vw;
//   height:85vh;
// }
::v-deep .el-step__description{
  font-size: medium;
}
::v-deep .el-step.is-vertical .el-step__title{
  font-size: 18px;
  color: #000;
  font-weight: 600;
}
::v-deep  .el-step:last-of-type .el-step__line{
  display: block;
  background-color: #409EFF;
}

::v-deep .disabled .el-upload--picture-card {
        display: none !important;
    }

::v-deep .el-step {
  .el-step__description  {
    padding-right: 5%;
  }
  .el-step__title {
    font-size: 22px;
    font-weight: 600;
    color: #1A1B1D;
    line-height: 30px;
  }

  .el-input--mini .el-input__inner  {
    padding: 0 10px;
    height: 40px;
    line-height: 28px;
    font-size: 14px;
    color: #1F1F1F;
  }
  .el-input--mini .el-input__icon {
      line-height: 42px;
  }
  .el-date-editor {
    width: 200px;
    .el-input__inner  {
      padding-left: 30px;
    }

  }
  .el-textarea__inner {
    padding: 0 10px;
    line-height: 28px;
    font-size: 14px;
    color: #1F1F1F;
  }

  .el-form-item__label {
    margin-top: 10px;
    font-size: 14px;
    color: #606266;
    line-height: 20px;
  }
  .more-info .radio-class .el-form-item__label {
    margin-top: 5px;
  }
}
::v-deep .el-table {
  // 表头样式
  border-radius: 4px;
  font-size: 14px;

  th.el-table__cell {
    height: 48px;
    font-size: 16px;
    color: #303133;
    line-height: 22px;
  }
  // 内容样式
  td.el-table__cell {
    height: 48px;
    background: #f2f6fc;
  }
}

.editor-color {
  ::v-deep .w-e-text p,
  .w-e-text h1,
  .w-e-text h2,
  .w-e-text h3,
  .w-e-text h4,
  .w-e-text h5,
  .w-e-text table,
  .w-e-text pre {
    color: #606266;
  }
}

</style>
