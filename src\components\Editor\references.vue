<template>
  <div class="reference">
    <h2>参考文献</h2>
    <div class="list">
      <ul>
        <li v-for="(item,index) in list" :key="index">
          <div class="serial-num">
            [{{ index + 1 }}]
          </div>
          <div class="editor">
            <wang-editor :content.sync="item.content" />
          </div>
          <div class="operating">
            <i v-if="index !== 0 || list.length > 1" class="yz-icon-minus" @click="del(index)"></i>
            <i class="yz-icon-plus" @click="add(index)"></i>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import wangEditor from './wang-editor';
export default {
  name: 'References',
  components: {
    wangEditor
  },
  props: {
    reference: {
      type: Array,
      default: () => {
        // 默认三个框
        return [
          { content: '' },
          { content: '' },
          { content: '' }
        ];
      }
    }
  },
  data() {
    return {
      list: this.reference
    };
  },
  watch: {
    reference(newVal) {
      this.list = newVal;
    }
  },
  mounted() {},
  methods: {
    add(index) {
      if (this.list.length >= 50) {
        this.$message({
          message: '最多只能添加50个参考文献！',
          type: 'warning'
        });
        return;
      }
      this.list.splice(index + 1, 0, { value: '' });
    },
    del(index) {
      this.list.splice(index, 1);
    }
  }
};
</script>

<style lang='scss' scoped>
.reference {
  min-height: 140px;
  //padding-bottom: 100px;

  ::v-deep .w-e-text p,
  .w-e-text h1,
  .w-e-text h2,
  .w-e-text h3,
  .w-e-text h4,
  .w-e-text h5,
  .w-e-text table,
  .w-e-text pre {
    color: #606266;
  }

}
.title {
  font-size: 21px;
  font-weight: 600;
  color: #303133;
}
.list {
  ul {
    li {
      line-height: 1.5;
      position: relative;

      .editor {
        width: calc(100% - 90px);
        margin-left: 30px;
      }

      .serial-num {
        height: 44px;
        position: absolute;
        left: 0;
        top: 0;
        margin: 10px 0;
        color: #606266;
      }

      .operating {
        position: absolute;
        right: 0;
        top: 10px;
        z-index: 520;
        i {
          cursor: pointer;
          margin: 0 5px;
        }
      }
    }
  }
}
</style>
