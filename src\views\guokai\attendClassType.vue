<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref="searchForm"
      size="mini"
      label-width="150px"
      class="yz-search-form"
      :model="form"
      @submit.native.prevent="search"
    >
      <el-form-item label="后台上课方式名称" prop="adminTeachTypeName">
        <el-input v-model="form.adminTeachTypeName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="学员端上课方式名称" prop="clientTeachTypeName">
        <el-input v-model="form.clientTeachTypeName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="是否启用" prop="openStatus">
        <el-select v-model="form.openStatus" clearable placeholder="请选择" filterable>
          <el-option label="启用" :value="1" />
          <el-option label="停用" :value="0" />
        </el-select>
      </el-form-item>
      <!-- canvas 表单 socket sessionstorage loacalstroge -->
      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
      </div>
    </el-form>
    <!-- 按钮区 -->
    <div class="yz-table-btnbox">
      <el-button
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="addRemind"
      >添加上课方式</el-button>
    </div>
    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      fit
      border
      size="small"
      header-cell-class-name="table-cell-header"
      style="width: 100%"
      :data="tableData"
    >
      <el-table-column
        prop="adminTeachTypeName"
        label="后台上课方式名称"
        align="center"
      />
      <el-table-column
        prop="clientTeachTypeName"
        label="学员端上课方式名称"
        width="250"
        align="center"
      />
      <el-table-column
        prop="createUserName"
        label="创建人"
        align="center"
        width="100"
      />
      <el-table-column
        prop="updateUserName"
        label="更新人"
        align="center"
        width="100"
      />
      <el-table-column
        prop="updateTime"
        label="更新时间"
        align="center"
        width="135"
      >
        <template v-slot="scope">
          {{ scope.row.updateTime ? scope.row.updateTime : '无' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="openStatus"
        label="是否启用"
        align="center"
        width="100"
      >
        <template v-slot="scope">
          <el-tag
            v-if="!scope.row.openStatus"
            class="tag"
            type="danger"
          >停用</el-tag>
          <el-tag
            v-if="scope.row.openStatus"
            class="tag"
            type="success"
          >启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="220">
        <template v-slot="scope">
          <el-popconfirm
            popper-class="my-popper"
            style="margin-top:10px"
            :title="'确定'+ stateFilter(scope.row.openStatus) +'？'"
            @confirm="statusChange(scope.row)"
          >
            <el-button
              slot="reference"
              size="mini"
              type="text"
              style="margin-right:10px"
              :disabled="!scope.row.newTeachType"
            >
              {{ scope.row.openStatus ? '停用':'启用' }}
            </el-button>
          </el-popconfirm>
          <el-button
            size="small"
            type="text"
            :disabled="!scope.row.newTeachType"
            @click="editRemind(scope.row)"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 新增/编辑  -->
    <course-details :visible.sync="remindShow" :title="optionsType" :row="row" @getTableList="getTableList" />

  </div>
</template>

<script>
import moment from 'moment';
import { getCookie } from 'tiny-cookie';
import courseDetails from './components/courseDetails.vue';

const proxy = {
  // 国开项目
  gk: [
    '/gkTeachType/getTeachTypeConfList.do', // 获取国开上课方式配置列表
    '/gkTeachType/updateOpenStatus.do' // 修改开启状态
  ],
  // bms 项目
  bms: [
    '/teachType/getTeachTypeConfList.do', // 获取国开上课方式配置列表
    '/teachType/updateOpenStatus.do' // 修改开启状态
  ]
};
const form = {
  clientTeachTypeName: '', // 后台上课方式名称
  adminTeachTypeName: '', // 学员端上课方式名称
  openStatus: '' // 启用状态
};
export default {
  components: {
    courseDetails
  },
  data() {
    return {
      apiSource: '', // 项目类型
      isSelf: false,
      form: form,
      tableData: [],
      tableLoading: false,
      confirmShow: false,
      situationShow: false,
      remindShow: false,
      tipsTitle: '',
      optionsType: '',
      row: {},
      status: '',
      selectRemindList: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  computed: {
    stateFilter() {
      return function(openStatus) {
        return openStatus ? '停用' : '启用';
      };
    }
  },
  created() {
  },
  mounted() {
    this.apiSource = this.$route.query.apiSource || 'gk';
    console.log(this.$route.query.apiSource, 'this.$route.query.apiSource');
    this.getTableList();
  },
  methods: {
    statusChange(row) {
      const url = proxy[this.apiSource][1];
      const params = {
        teachTypeConfId: row.teachTypeConfId,
        openStatus: !row.openStatus
      };
      this.$http.post(url, params).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.getTableList();
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    handleQUeryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        pageSize: this.pagination.limit
      };
    },
    getTableList() {
      const url = proxy[this.apiSource][0];
      this.tableLoading = true;
      const params = this.handleQUeryParams();
      this.$http.post(url, params).then((res) => {
      // this.$post('getSysRemindList', params).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.tableData.forEach(item => {
            item.updateTime = moment(item.updateTime).format('YYYY-MM-DD HH:mm:ss');
          });
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    addRemind(row) {
      this.optionsType = '添加';
      this.row = null;
      this.remindShow = true;
    },
    editRemind(row) {
      this.optionsType = '编辑';
      this.row = row;
      this.remindShow = true;
    }
  }
};
</script>

<style lang="scss" >
.yz-table-btnbox{
  margin-top: 20px;
}
.my-popper{
  .el-popconfirm__action{
    margin-top: 5px;
    .el-button:nth-child(1){
      padding-right: 10px;
    }
  }
  .el-popconfirm__main {
    display: flex;
    justify-content: center;
    margin-bottom: 5px;
  }
}
</style>
