<template>
  <div class="yz-base-container">
    <el-steps direction="vertical" :active="3">
      <el-step title="第一步： 复制网报信息">
        <template slot="description">
          <div class="header">
            <div>
              <span class="step-title">步骤一</span>
              进入网报官网地址 <a href="#" class="intro-first" @click="openExam">点击跳转</a> ，按照如下信息填写保存
            </div>
            <div>
              <span class="step-title">步骤二</span>
              可以选择以下信息单个复制
              <!-- 或者选择一键全部复制<span class="handle-step" @click="showScriptImg">(操作步骤)</span> -->
            </div>

          </div>
          <!-- <div class='yz-table-btnbox'>
            <el-button v-show="!copyBtnShow" type="primary" size="small" @click="downSript">批量复制信息代码</el-button>
          </div> -->
          <h3 class="titVal">注册信息</h3>
          <el-descriptions class="margin-top" :column="2" :size="size" border>
            <el-descriptions-item :span='2'>
              <template slot="label">
                姓名
              </template>
              {{ stuInfo.xm }}
              <span @click="copy(stuInfo.xm)">复制</span>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                性别
              </template>
              {{ stuInfo.xbname }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                出生日期
              </template>
              {{ stuInfo.csrq }}
              <span @click="copy(stuInfo.csrq)">复制</span>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                证件类型
              </template>
              {{ stuInfo.zjlxname }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                证件号
              </template>
              {{ stuInfo.zjdm }}
              <span @click="copy(stuInfo.zjdm)">复制</span>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                民族
              </template>
              {{ stuInfo.mzname }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                户口所在地
              </template>
              {{ stuInfo.hkname }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                密码
              </template>
              {{ ruleForm.password || stuInfo.password }}
              <span @click="copy(ruleForm.password ||stuInfo.password)">复制</span>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                确认密码
              </template>
              {{ ruleForm.password || stuInfo.password }}
              <span @click="copy(ruleForm.password || stuInfo.password)">复制</span>
            </el-descriptions-item>
            <el-descriptions-item :span='2'>
              <template slot="label">
                移动电话
              </template>
              {{ stuInfo.lxsj }}
              <span @click="copy(stuInfo.lxsj)">复制</span>
              <span class="tips">(此手机号码用于绑定您的网上报名号)</span>
            </el-descriptions-item>
          </el-descriptions>

          <h3 class="titVal">基本资料</h3>
          <el-descriptions class="margin-top" :column="2" :size="size" border>
            <el-descriptions-item :span='2'>
              <template slot="label">
                居住证信息
              </template>
              {{ stuInfo.jzzszdmc }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                政治面貌
              </template>
              {{ stuInfo.zzmmname }}
            </el-descriptions-item>
            <el-descriptions-item :span='2'>
              <template slot="label">
                考试语种
              </template>
              {{ stuInfo.wyyzname }}
              <span class="tips">(专升本考生只能选报英语)</span>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                考试类型
              </template>
              {{ stuInfo.kslxdm === '0' ? '参加考试' : '免试入学' }}
              <span class="tips">(免试生请选择免试入学)</span>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                照顾加分
              </template>
              <!-- {{ stuInfo.kslbname }} -->
              <span class="tips">(山区县和25岁不用勾选)</span>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                考试类别
              </template>
              {{ stuInfo.kslbname }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                报考科类
              </template>
              {{ stuInfo.jhlbname }}
              <span class="tips">(报考专升本的外语类专业请选择"文史类")</span>
            </el-descriptions-item>
            <el-descriptions-item :span='2'>
              <template slot="label">
                考试科目组
              </template>
              {{ stuInfo.kmzname }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                考试县区
              </template>
              {{ stuInfo.xqname }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                报名点
              </template>
              {{ stuInfo.bmddmmc }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                考前学历
              </template>
              {{ stuInfo.kqxlname }}
            </el-descriptions-item>

            <el-descriptions-item :span='2'>
              <template slot="label">
                职业
              </template>
              {{ stuInfo.zyname }}
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                毕业学校
              </template>
              {{ stuInfo.byxx }}
              <span @click="copy(stuInfo.byxx)">复制</span>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                毕业年月
              </template>
              {{ stuInfo.byrq }}
            </el-descriptions-item>
            <el-descriptions-item :span='2'>
              <template slot="label">
                毕业专业
              </template>
              {{ stuInfo.byzy }}
              <span @click="copy(stuInfo.byzy)">复制</span>
            </el-descriptions-item>
            <el-descriptions-item :span='2'>
              <template slot="label">
                毕业证书号
              </template>
              {{ stuInfo.byzshm }}
              <span @click="copy(stuInfo.byzshm)">复制</span>
              <span class="tips">(未取得专科毕业证的专科生请输入"待定")</span>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                邮政编码
              </template>
              {{ stuInfo.yzbm }}
              <span @click="copy(stuInfo.yzbm)">复制</span>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                固定电话
              </template>
              {{ stuInfo.lxdh }}
              <span @click="copy(stuInfo.lxdh)">复制</span>
            </el-descriptions-item>

            <el-descriptions-item :span='2'>
              <template slot="label">
                通讯地址
              </template>
              <p>
                <span class="address"> 省： {{ stuInfo.exam_province_name || '' }}</span>
                <span class="address">市： {{ stuInfo.exam_city_name || '' }}</span>
                <span class="address">区（县）：{{ stuInfo.exam_district_name || '' }}</span>
              </p>
              {{ stuInfo.txdz }}
              <span @click="copy(stuInfo.txdz)">复制</span>
            </el-descriptions-item>
          </el-descriptions>

          <!-- copy 专用 -->
          <div ref="copyElem" class="hidden">{{ copyText }}</div>
          <h3 class="titVal">报考志愿</h3>
          <div class="table-b">
            <table v-if="stuInfo.kslbdm === '1'">
              <thead>
                <tr>
                  <th>批次</th>
                  <th>报考院校</th>
                  <th>报考专业</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td :rowspan="2">专科升本科</td>
                  <td class="taleft" style="color:red">
                    院校1：{{ stuInfo.zsbpc1bkyx1 || '' }}
                    <span class="copyTxt" @click="copy(stuInfo.zsbpc1bkyx1 || '')">复制</span>
                  </td>
                  <td class="td-flex">
                    <span class="red">
                      专业1：{{ stuInfo.zsbpc1bkyx1zy1 || '' }}
                      <span class="copyTxt" @click="copy(stuInfo.zsbpc1bkyx1zy1 || '')">复制</span>
                    </span>
                    <span class="red">
                      专业2：{{ stuInfo.zsbpc1bkyx1zy2 || '' }}
                      <span class="copyTxt" @click="copy(stuInfo.zsbpc1bkyx1zy2 || '')">复制</span>
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="taleft">
                    院校2：{{ stuInfo.zsbpc1bkyx2 || '不必填写' }}
                    <span v-if="stuInfo.zsbpc1bkyx2" class="copyTxt" @click="copy(stuInfo.zsbpc1bkyx2)">复制</span>
                  </td>
                  <td class="td-flex">
                    <span class="red">
                      专业1：{{ stuInfo.zsbpc1bkyx2zy1 || '不必填写' }}
                      <span v-if="stuInfo.zsbpc1bkyx2zy1" class="copyTxt" @click="copy(stuInfo.zsbpc1bkyx2zy1)">复制</span>
                    </span>
                    <span class="red">
                      专业2：{{ stuInfo.zsbpc1bkyx2zy2 || '不必填写' }}
                      <span v-if="stuInfo.zsbpc1bkyx2zy2" class="copyTxt" @click="copy(stuInfo.zsbpc1bkyx2zy2)">复制</span>
                    </span>
                  </td>
                  <!-- <td class="taleft">院校2：不必填写</td>
                  <td class="td-flex">
                    <span>专业1：不必填写</span>
                    <span>专业2：不必填写</span>
                  </td> -->
                </tr>
              </tbody>
            </table>

            <table v-else-if="stuInfo.kslbdm === '5'">
              <thead>
                <tr>
                  <th colspan="2">批次</th>
                  <th>报考院校</th>
                  <th>报考专业</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td :rowspan="999">高起专</td>
                  <td rowspan="2"> 脱产班</td>
                  <td class="taleft">院校1：不必填写</td>

                  <td class="td-flex">
                    <span>专业1：不必填写</span>
                    <span>专业2：不必填写</span>
                  </td>
                </tr>
                <tr>
                  <td class="taleft">院校2：不必填写</td>

                  <td class="td-flex">
                    <span>专业1：不必填写</span>
                    <span>专业2：不必填写</span>
                  </td>
                </tr>
                <tr>
                  <td rowspan="3">非脱产班</td>
                  <td class="taleft" style="color:red">
                    院校1：{{ stuInfo.gqgpc4bkyx1 }}
                    <span class="copyTxt" @click="copy(stuInfo.gqgpc4bkyx1 || '')">复制</span>
                  </td>
                  <td class="td-flex">
                    <span class="red">
                      专业1：{{ stuInfo.gqgpc4bkyx1zy1 }}
                      <span class="copyTxt" @click="copy(stuInfo.gqgpc4bkyx1zy1 || '')">复制</span>
                    </span>
                    <span>
                      专业2：{{ stuInfo.gqgpc4bkyx1zy2 || '不必填写' }}
                      <span v-if="stuInfo.gqgpc4bkyx1zy2" class="copyTxt" @click="copy(stuInfo.gqgpc4bkyx1zy2)">复制</span>
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="taleft" style="color:red">
                    院校2：{{ stuInfo.zsbpc1bkyx2 }}
                    <span class="copyTxt" @click="copy(stuInfo.zsbpc1bkyx2)">复制</span>
                  </td>
                  <td class="td-flex">
                    <span class="red">
                      专业1：{{ stuInfo.zsbpc1bkyx2zy1 }}
                      <span class="copyTxt" @click="copy(stuInfo.zsbpc1bkyx2zy1)">复制</span>
                    </span>
                    <span>
                      专业2：{{ stuInfo.zsbpc1bkyx2zy2 || '不必填写' }}
                      <span v-if="stuInfo.zsbpc1bkyx2zy2" class="copyTxt" @click="copy(stuInfo.zsbpc1bkyx2zy2)">复制</span>
                    </span>
                  </td>
                </tr>

              </tbody>
            </table>
          </div>
          <div class="upload-box">
            <p>注：您的备用资料下载（如无用上请忽略）
              <span class="down-click" @click="downAllFileFn">一键下载</span>
            </p><br />
            <template v-for="(item,index) in annexInfo">
              <p v-if="item.isRequire === '1'" :key="index">
                {{ item.annexTypeName }}:
                <span class="down-click" @click="downFileFn(item)">点击下载</span>
              </p>
            </template>

            <template v-for="item in annexlist">
              <p v-if="item.annexUrl" :key="item.annexId">
                {{ item.annexTypeName }}:
                <span class="down-click" @click="downLiveFileFn(item)">点击下载</span>
              </p>
            </template>
            <div class="pdf-box">
              <el-button v-if="liveList.length > 1" type="primary" size="small" @click="downLivePdf('jzz')">下载居住证pdf</el-button>
              <el-button v-if="houseHoldList.length > 1" type="primary" size="small" @click="downLivePdf('hkb')">下载户口本pdf</el-button>
              <el-button v-if="idCardList.length > 1" type="primary" size="small" @click="downLivePdf('sfz')">下载身份证pdf</el-button>
            </div>
            <!-- 隐藏元素 -->
            <div class="fg-box">
              <img v-for="(item) in annexlist" :id="'fg' + item.annexId" :key="item.annexType" :src="item.annexUrl | imgFilters" alt="" />
              <img
                v-for="item in idCardList"
                :id="'fg' + item.annexType"
                :key="item.annexType"
                class="jzz"
                :src="item.annexUrl | splitOssUrl"
                alt="身份证"
              />
            </div>

          </div>
        </template>
      </el-step>
      <!-- <div class='yz-table-btnbox'> -->
      <!-- <el-button v-if="!copyBtnShow" type="success" size="small" plain :disabled='copyBtnShow' @click="downSript">复制脚本</el-button> -->
      <!-- <el-button v-show="!copyBtnShow" type="success" size="small" @click="downSript">复制脚本</el-button> -->
      <!-- <el-button type="success" size="small" icon="el-icon-plus" plain @click="openSignUp">新增预报名</el-button> -->
      <!-- </div> -->
      <el-step title="第二步： 网报信息回填">
        <template slot="description">
          <div class="intro-second">
            <div class="form-box">
              <div class="form-head">
                预报名号、密码回填
                <span :class="{'status':true,'isfinish':signFormState === ''}" class="status">{{ signFormState ? '（已完成）':'（未完成）' }}</span>
              </div>
              <el-form ref="ruleForm" size='mini' :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
                <el-form-item label="预报名号" prop="username">
                  <el-input v-model="ruleForm.username" placeholder="请填写并确认你的预报名号" @keyup.enter.native="handleMethod" />
                </el-form-item>
                <el-form-item label="预报名密码" prop="password">
                  <el-input v-model="ruleForm.password" placeholder="请填写并确认你的密码" :maxlength="8" @keyup.enter.native="handleMethod" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submit()">确认保存</el-button>
                </el-form-item>
              </el-form>

            </div>

            <div class="form-box">
              <div class="form-head">
                回填考生号
                <span :class="{'status':true,'isfinish':stuExamNoState === ''}" class="status">{{ stuExamNoState ? '（已完成）':'（未完成）' }}</span>
              </div>
              <el-form ref="stuNoForm" size='mini' :model="stuNoForm" :rules="rules" label-width="100px" class="demo-ruleForm">
                <el-form-item label="考生号" prop="examNo">
                  <el-input v-model="stuNoForm.examNo" placeholder="请填写考生号" @keyup.enter.native="handleMethod" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitStuExamNo">提交</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <div class="form-box">
            <div class="form-head">
              备注
            </div>
            <el-form ref="remarkForm" size='mini' :model="remarkForm" :rules="rules" label-width="100px" class="demo-ruleForm">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="remarkForm.remark" type="textarea" :rows="2" placeholder="请填写备注" :maxlength="100" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="submitRemark">提交</el-button>
              </el-form-item>
            </el-form>
          </div>
        </template>
      </el-step>
    </el-steps>

    <el-form class="update-form">
      <el-form-item label="变更记录" prop="password">
        <el-table
          class="tableRecord"
          size='small'
          :data="tableRecord"
          style="width: 100%"
        >
          <el-table-column prop="name" width="50">
            <template slot-scope="scope">
              <div> {{ scope.$index + 1 }}、</div>
            </template>
          </el-table-column>
          <el-table-column prop="create_user" label="姓名" width="180" />
          <el-table-column prop="create_time" label="时间" width="180" />
          <el-table-column prop="ext_1" label="信息" />
        </el-table>
      </el-form-item>
    </el-form>

    <!-- 保存成功提示-->
    <common-dialog
      width="1000px"
      :visible.sync='dialogShow'
      @close='dialogClose'
    >
      <div class="dialog-main">
        <h3>保存成功，下一步请学历验证或上传附件，然后绑定手机号</h3>
        <p>请回到网报页面底部，完成相应操作，如下图：</p>
        <img v-if="stuInfo.kslbdm === '5'" src="../../assets/imgs/annex/school1.png" alt="" />
        <img v-if="stuInfo.kslbdm === '1'" src="../../assets/imgs/annex/school2.png" alt="" />
      </div>
    </common-dialog>

    <!-- 一键复制网报信息-->
    <common-dialog
      width="1100px"
      :visible.sync='copyScriptShow'
      @close='copyScriptClose'
    >
      <div class="dialog-main tips-box">
        <div class="pop-head">
          <span>一键网报操作指引</span>
          <!-- <el-button v-show="!copyBtnShow" type="primary" size="small" @click="downSript">批量复制信息代码</el-button> -->
        </div>
        <div class="split"></div>
        <img src="../../assets/imgs/annex/script-tips.png" alt="" />
      </div>
    </common-dialog>
  </div>
</template>

<script>

import downFile from '@/utils/downFile';
import { ossUri } from '@/config/request';
import { CopyElemText } from '@/utils';
import buildZipDownload from '@/utils/zip';
import downScript from '@/utils/down-script';
import Driver from 'driver.js';
import { jsPDF } from 'jspdf';
import 'driver.js/dist/driver.min.css';
// const proxy = {
//   // bst 项目
//   bst: [
//     '/sceneConfirm/insertRegisterNo.do' // pc远智学堂新增预报名号接口
//   ],
//   // bms 项目
//   bms: [
//     '/newWork/get.do', // 报读资料
//     '/recruit/getAnnexList.do' // 学生资料下载
//   ]
// };
export default {
  filters: {
    imgFilters(val) {
      return ossUri + val;
    }
  },
  data() {
    return {
      selFiles: [],
      pdfWidth: 19,
      size: 'medium',
      type: 'undergraduate1',
      learnId: '',
      stdId: '',
      successState: false,
      copyBtnShow: false,
      annexInfo: [],
      tableRecord: [],
      annexlist: [],
      liveList: [], // 居住证
      houseHoldList: [], // 户口本
      idCardList: [], // 身份证
      idCardIdList: [], // 身份证idList
      tempPwd: '',
      // 弹窗
      dialogShow: false,
      copyScriptShow: false,
      stuInfo: {},
      copyText: '',
      signFormState: false,
      stuExamNoState: false,
      remarkForm: {
        remark: ''
      },
      stuNoForm: {
        examNo: ''
      },
      ruleForm: {
        username: '',
        password: ''
      },
      driver: '',
      steps: [
        {
          element: '.intro-first',
          popover: {
            title: ' ',
            description: `<img
            class="step-img"
              alt=""
            />`,
            position: 'bottom'
          }
        },
        {
          element: '.intro-second',
          popover: {
            title: ' ',
            description: `<div>
            <span class="tip-title">第二步</span>
            <span class="tip-content">回填预报名号、密码、考生号及其他状态</span>
            </div>`,
            position: 'top'
          }
        }
      ],
      rules: {
        username: [
          { required: true, message: '必须填写！', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '必须填写！', trigger: 'blur' }
        ]
      }
    };
  },
  mounted() {
    this.learnId = this.$route.query.learnId;
    this.stdId = this.$route.query.stdId;
    // this.ruleForm.password = 'yz888888';
    this.getStuInfoUrl();
    this.getDataInfo();
    this.getRegisterList();
    this.getannexInfo();
    // 系统步骤引导
    this.driver = new Driver({
      animate: true,
      doneBtnText: '完成',
      closeBtnText: '关闭',
      nextBtnText: '下一步',
      prevBtnText: '上一步',
      keyboardControl: true
    });
    if (!localStorage.getItem('isExitSteps')) {
      this.handleShowGuide();
      localStorage.setItem('isExitSteps', 'true');
    }
  },
  methods: {
    downLivePdf(type) {
      if (type === 'jzz') {
        this.selFiles = this.liveList;
      } else if (type === 'hkb') {
        this.selFiles = this.houseHoldList;
      } else if (type === 'sfz') {
        this.selFiles = this.idCardIdList;
      }
      // console.log(this.selFiles, ' this.selFiles1111111111111');
      this.downPdf(type);
    },
    handleShowGuide() {
      this.driver.defineSteps(this.steps);
      this.driver.start();
    },
    openExam() {
      window.open('https://www.eeagd.edu.cn/cr');
    },
    dialogClose() {
      this.dialogShow = false;
    },
    copyScriptClose() {
      this.copyScriptShow = false;
    },
    // 基础资料信息
    getDataInfo() {
      this.$http.post('/newWork/get.do', { learnId: this.learnId }).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          this.stuInfo = body;
          this.tempPwd = body.password;
        }
      });
    },
    // 考生号input回车
    handleMethod() {
      // if (this.ruleForm.username && this.ruleForm.password) {
      //   this.submit();
      // }
    },
    // 获取预报名列表信息
    getRegisterList() {
      console.log(this.learnId, 'this.learnId');
      this.$post('yzGetSceneRegisterList', { learnId: this.learnId }).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          this.sceneRegisterList = body;
          if (body.length > 0) {
            this.signFormState = true;
          }
          this.sceneRegisterList.forEach(item => {
            if (item.register_status === '1') {
              this.checkedAct = item.register_id;
              this.ruleForm.username = item.username || '';
              this.ruleForm.password = item.password || this.tempPwd;
            }
          });
        }
      });
      this.getExamNoModifyRecord();
      // 获取考试号
      this.$post('yzGetExamNoByLearnId', { learnId: this.learnId }).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          this.stuNoForm.examNo = body;
          this.stuExamNoState = !!body;
        }
      });
      // 获取备注
      this.$post('getRemark', { learnId: this.learnId }).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          this.remarkForm.remark = body;
        }
      });
    },
    getExamNoModifyRecord() {
      // 获取变更记录信息
      this.$post('yzGetExamNoModifyRecord', { learnId: this.learnId }).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          this.tableRecord = body;
        }
      });
    },

    // 修改预报名信息
    submit() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.ruleForm));
          const data = {
            ...formData,
            learnId: this.learnId,
            stdId: this.stdId
          };
          this.$http.post('/sceneConfirm/insertRegisterNo.do', data).then(res => {
            const { fail, body } = res;
            if (!fail) {
              this.signFormState = true;
              // this.dialogShow = true;
              this.getExamNoModifyRecord();
              this.getRegisterList();
            }
          });
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getDataInfo();
        }
      });
    },
    // 修改考生号
    submitStuExamNo() {
      if (!this.stuNoForm.examNo) {
        this.$message.error('考生号不可为空！');
        return;
      }
      const data = {
        examNo: this.stuNoForm.examNo,
        learnId: this.learnId,
        stdId: this.stdId
      };
      this.$post('yzUpdateExamNo', data).then(res => {
        const { fail, body } = res;

        if (!fail) {
          this.stuExamNoState = true;
          this.getExamNoModifyRecord();
          if (body) {
            this.stuInfo = body;
          }
        }
      });
      this.$message({
        message: '操作成功',
        type: 'success'
      });
    },
    // 修改备注
    submitRemark() {
      if (!this.remarkForm.remark) {
        this.$message.error('备注不可为空！');
        return;
      }
      const data = {
        learnId: this.learnId,
        studentRemark: this.remarkForm.remark
      };
      this.$post('yzUpdateStudentRemark', data).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          this.stuInfo = body;
          this.getRegisterList();
        }
      });
      this.$message({
        message: '操作成功',
        type: 'success'
      });
    },
    close() {},
    // 获取附件资料
    getStuInfoUrl() {
      const data = {
        learnId: this.learnId,
        recruitType: 1
      };
      this.$http.post('/annex/getAnnexList.do', data)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.annexInfo = body;
            this.annexInfo.forEach(item => {
              if (item.annexUrl) {
                if (item.annexType === '1' || item.annexType === '2') {
                  this.idCardList.push(item);
                  this.idCardIdList.push({ 'id': 'fg' + item.annexType });
                }
              }
            });
          }
        });
    },
    downFileFn(item) {
      this.$message({
        message: '正在下载中，请耐心等待...',
        type: 'success'
      });
      const fileSuffixIndex = item.annexUrl.lastIndexOf('.');
      let url;
      // 如果路径没有文件后缀，那么默认给个jpg，不然下载的文件没有格式
      if (fileSuffixIndex === -1) {
        url = '.jpg';
      } else {
        url = '';
      }
      downFile(ossUri + item.annexUrl + '?time=' + new Date().getTime() + '&x-oss-process=image/interlace,1/format,jpg/quality,q_30', this.stuInfo.xm + item.annexTypeName + url);
    },
    downLiveFileFn(item) {
      this.$message({
        message: '正在下载中，请耐心等待...',
        type: 'success'
      });
      const fileSuffixIndex = item.annexUrl.lastIndexOf('.');
      let url;
      // 如果路径没有文件后缀，那么默认给个jpg，不然下载的文件没有格式
      if (fileSuffixIndex === -1) {
        url = '.jpg';
      } else {
        url = '';
      }
      downFile(ossUri + item.annexUrl + '?time=' + new Date().getTime() + '&x-oss-process=image/interlace,1/format,jpg/quality,q_30', this.stuInfo.xm + item.annexTypeName + url);
    },
    downAllFileFn() {
      const fileList = [];
      this.annexInfo.forEach(item => {
        if (item.isRequire === '1' && item.annexUrl) {
          const fileSuffixIndex = item.annexUrl.lastIndexOf('.');
          let urlFormat;
          // 如果路径没有文件后缀，那么默认给个jpg，不然下载的文件没有格式
          if (fileSuffixIndex === -1) {
            urlFormat = '.jpg';
          } else {
            urlFormat = item.annexUrl.substring(item.annexUrl.lastIndexOf('.'));
          }
          fileList.push({
            fileUrl: ossUri + item.annexUrl + '?time=' + new Date().getTime() + '&x-oss-process=image/interlace,1/format,jpg/quality,q_30',
            fileName: this.stuInfo.xm + item.annexTypeName + urlFormat
          });
        }
      });
      this.annexlist.forEach(item => {
        if (item.annexUrl) {
          const urlFormat = item.annexUrl.substring(item.annexUrl.lastIndexOf('.'));
          fileList.push({
            fileUrl: ossUri + item.annexUrl + '?time=' + new Date().getTime() + '&x-oss-process=image/interlace,1/format,jpg/quality,q_30',
            fileName: this.stuInfo.xm + item.annexTypeName + urlFormat
          });
        }
      });
      // console.log(this.annexInfo, 'this.annexInfo');
      // console.log(this.annexlist, 'this.annexlist');
      this.selFiles = [...this.liveList, ...this.houseHoldList, ...this.idCardIdList];
      if (this.liveList.length > 1) {
        this.downLivePdf('jzz');
      }
      if (this.houseHoldList.length > 1) {
        this.downLivePdf('hkb');
      }
      if (this.idCardIdList.length > 1) {
        this.downLivePdf('sfz');
      }
      buildZipDownload(fileList, `${this.stuInfo.xm}+${this.stuInfo.zjdm}`);
    },
    // 复制链接
    copy(val) {
      this.copyText = val;

      // 等待元素更新
      this.$nextTick(() => {
        CopyElemText(this.$refs.copyElem);
        this.$message({
          message: '复制成功',
          type: 'success'
        });
      });
    },
    // 下载脚本
    downSript() {
      downScript(this.stuInfo);
      this.copy(downScript(this.stuInfo));
    },
    showScriptImg() {
      this.copyScriptShow = true;
    },
    getannexInfo() {
      const data = {
        learnId: this.learnId,
        start: '0',
        length: '10'
      };
      this.$post('getannexlist', data).then(res => {
        console.log(res, 'getannexlist');
        const { fail, body } = res;
        if (!fail && body) {
          this.annexlist = body;
          if (this.annexlist.length === 0) return;
          this.annexlist.map(item => {
            if (item.annexUrl) {
              if (item.annexType === 8 || item.annexType === 9) {
                this.liveList.push({ 'id': 'fg' + item.annexId });
              }
              if (item.annexType === 10 || item.annexType === 11) {
                this.houseHoldList.push({ 'id': 'fg' + item.annexId });
              }
            }
          });
          // this.selFiles = this.liveList;
        }
      });
    },
    // 得到pdf内容的高度
    getPdfHeight() {
      // const img = document.getElementById('fg80');
      // console.log(img.height, 'img.height111');
      // console.log(this.selFiles);
      let allHeight = 0;
      // const psflist =
      for (var i = 0; i < this.selFiles.length; i++) {
        const one = this.selFiles[i];
        // 得到高度
        const img = document.getElementById(one.id);
        console.log(img.height, 'img.height');
        // allHeight = allHeight + img.height;
        allHeight += (img.height * 19) / img.width;
      }
      // const pdfHeight = (allHeight * this.pdfWidth) / 400;
      return allHeight;
    },
    // 下载pdf
    downPdf(type) {
      const pdfHeight = this.getPdfHeight();
      // eslint-disable-next-line new-cap
      const recordPdf = new jsPDF({ unit: 'cm', format: [21, pdfHeight + 3] });
      // 遍历图片
      let top = 1;
      let imgStr = '';
      for (var i = 0; i < this.selFiles.length; i++) {
        const one = this.selFiles[i];
        // 得到图片所占内容的高度
        const img = document.getElementById(one.id);
        const destHeight = (img.height * 19) / img.width;
        // pdf内容添加图片
        imgStr = img.src + '?time=' + new Date().getTime() + '&x-oss-process=image/interlace,1/format,jpg/quality,q_30';
        recordPdf.addImage(imgStr, 'jpeg', 1, top, this.pdfWidth, destHeight);
        top = top + destHeight + 1;
      }
      // 保存pdf
      let strName;
      if (type === 'jzz') {
        this.selFiles = this.liveList;
        strName = '居住证.pdf';
      } else if (type === 'hkb') {
        strName = '户口本.pdf';
      } else if (type === 'sfz') {
        strName = '身份证.pdf';
      }
      strName = this.stuInfo.xm + strName;
      recordPdf.save(strName);
    }
  }
};
</script>

<style lang = "scss" scoped>
.yz-base-container{
  width: 1200px;
  margin: 0 auto;
}
.margin-top{
  margin-top: 22px;
  margin-bottom: 30px;
  span{
    /* float: right; */
    color: #3693f7;
    cursor: pointer;
    margin-left: 20px;
    font-weight: 600;
  }
  .tips{
    color: red;
    cursor: auto;
  }
  .address{
    color: #000;
    font-weight: 400;
    margin-right: 30px;
    margin-left: 0;
  }
}
.table-b {
  table {
    width: 100%;
    border-top: 1px solid #EBEEF5;
    border-left: 1px solid #EBEEF5;
    border-right: 1px solid #EBEEF5;

    th, td {
      font-size: 16px;
      font-weight: 400;
      vertical-align: middle;
      border-bottom: 1px solid #EBEEF5;
      border-right: 1px solid #EBEEF5;
      background-color: #fff;
      color: #606266;
      /*border-right: 1px solid #72dfff;*/
      padding: 4px;
      text-align: center;
      &:last-of-type {
        border-right: none;
      }
      &.text-l {
        text-align: left;
      }
      span{
        /* display: block;
        line-height: .28rem; */
        margin-right: 50px;
      }
    }
    .td-flex{
      display: flex;
      span{
        flex:1;
      }
      &>span{
        width: 50%;
        text-align: left;
        padding-left: 20px;
      }
    }

  }
}
.demo-ruleForm{
  width: 900px;
  margin-top: 20px;
}
.titVal{
  text-align: center;
  color: #000;
  opacity: .8;
}
.upload-box{
  margin: 20px 0;
  p{
    display: inline-block;
  }
  span{
    color: #3693f7;
    cursor: pointer;
    margin-right: 20px;
    line-height: 30px;
    font-weight: 600;
  }

}
.hidden {
  width: 0;
  height: 0;
  position: absolute;
  top:0;
  opacity: 0 !important;
}
.copy{
  color: #3693f7;
  cursor: pointer;
  margin-right: 20px;
  font-weight: 600;
}
.table{
  margin-bottom: 20px;
  .checked{
    margin-right: 10px;
  }
}
.tableRecord ::v-deep .el-table__header-wrapper{
  display: none;
}
.red{
  color: red;
}

.copyTxt{
  color: #3693f7;
  margin-left: 30px;
  cursor: pointer;
}
::v-deep .el-descriptions-item__content{
  color: #000;
}
.table-b table td{
  color: #000;
}

.down-click{
  color: #3693f7;
  font-weight: 600;
}
.taleft{
  text-align:left!important;
  padding-left: 20px!important;
}
.form-box{
  margin-bottom: 15px;
  background-color: #f7f7f7;
  padding: 10px;
  .form-head{
    padding-top: 16px;
    padding-left: 20px;
    font-size: 15px;
    color: #666;
    .isfinish{
      color: red;
    }
  }
}
.tips-box{
  img{
    /* width: 1100px;
    height: 2000px; */
    width: 100%;
    height: 100%;
  }
}
.header{
  padding-top: 20px;
  width: 1000px;
  /* height: 100px; */
  line-height: 25px;
  font-weight: 600;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  div{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    flex-shrink: 0;
    p{
      margin-top: 5px;
    }
  }
  .step-title{

    position: relative;
    display: inline-block;
    margin-right: 30px;
    width: 80px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    background-color: #ebf4fe;
    color: #5ea8f8;
    font-weight: 400;
  }
    .step-title::before{
      position:absolute;
      content: "";
      width: 20px;
      height: 30px;
      right: -20px;
      top: 0;
      /* background-color: pink; */
      border-style: solid;
      border-color: transparent;
      border-width: 15px 0 15px 20px;
      border-left-color: #ebf4fe;
    }
}
.handle-step{
  color:#1890ff;
  cursor: pointer;
  font-weight: 400;

}
.pop-head{
  display: flex;
  align-items: center;
  margin-left: 57px;
  span{
    font-size: 22px;
    font-weight: 600;
    margin-right: 10px;
    color: #1a1b1d;
  }
  ::v-deep .el-button--small{
    font-size: 16px;
  }
}
.split{
  width: 100%;
  height: 1px;
  margin-top: 20px;
  background-color: #eee;
}
.fg-box{
  display: none;
}
.intro-second{
  padding-top: 20px;
}
::v-deep .el-step__description.is-finish{
  color: #000;
}
::v-deep .el-step__description{
  font-size: medium;
}
::v-deep .el-step.is-vertical .el-step__title{
  font-size: 18px;
  color: #000;
  font-weight: 600;
}
::v-deep  .el-step:last-of-type .el-step__line{
  display: block;
  background-color: #409EFF;
}
.update-form{
  margin-left: 30px;
}
.pdf-box{
  margin: 10px 0;
}
</style>

