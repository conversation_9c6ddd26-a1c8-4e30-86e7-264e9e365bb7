<template>
  <div class="yz-open-packup">
    <div class='yz-open-packup__children' :class='{active: isOpen}'>
      <slot></slot>
    </div>

    <label :for='labelName' class='yz-open-packup__arrow'>
      <i class='el-icon-arrow-down toggle-icon' :class='{rotate: isOpen}'></i>
      <span class='toggle-text'>{{ isOpen ? '收起' : '展开' }}</span>
      <input :id='labelName' v-model='isOpen' type='checkbox' name='' />
    </label>
  </div>
</template>

<script>
export default {
  name: 'OpenPackup',
  data() {
    return {
      isOpen: false
    };
  },
  computed: {
    labelName() {
      return `toggle-radio-${new Date().getTime()}`;
    }
  },
  methods: {
  }
};
</script>

<style lang="scss" scoped>
  .yz-open-packup{
  }

  .yz-open-packup__children{
    overflow: hidden;
    transition: max-height 0.3s;
    max-height: 70px;
    &.active{
      max-height: 600px;
    }
  }

  .yz-open-packup__arrow{
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #1890ff;
    padding: 10px;
    user-select: none;
    &:active{
      opacity: 0.8;
    }
    .toggle-icon{
      transition: transform 0.2s;
      &.rotate{
        transform: rotate(180deg);
      }
    }
    .toggle-text{
      padding-left: 5px;
      font-size: 14px;
    }
    & > input{
      position: absolute;
      top: -9999px;
      left: -9999px;
    }
  }

</style>
