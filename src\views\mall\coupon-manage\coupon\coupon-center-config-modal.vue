<template>
  <common-dialog
    class="common-dialog"
    width="70%"
    :title="`${ isEdit ? '修改' : '新增'}领券中心配置`"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        size="small"
        label-suffix=":"
      >
        <el-form-item label="领券中心标题" prop="couponCenterName">
          <el-input v-model="form.couponCenterName" placeholder="请输入领券中心标题" maxlength="8" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="微信分享标题" prop="couponWxShareTitle">
          <el-input v-model="form.couponWxShareTitle" placeholder="请输入微信分享标题" maxlength="20" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="微信分享副标题" prop="couponWxShareSubTitle">
          <el-input v-model="form.couponWxShareSubTitle" placeholder="微信分享副标题" maxlength="20" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="领券中心头图" prop="couponCenterHeadImg">
          <upload-file
            :max-limit="1"
            exts="jpg|png"
            :file-list="couponCenterHeadImg"
            @remove="headImgRemove"
            @success="headImgSuccess"
          />
        </el-form-item>
        <el-form-item label="微信分享图" prop="couponWxShareImg">
          <upload-file
            :max-limit="1"
            exts="jpg|png"
            :file-list="couponWxShareImg"
            @remove="shareImgRemove"
            @success="shareImgSuccess"
          />
        </el-form-item>
        <el-form-item label="优惠券展示" prop="productCouponVOList">
          <el-button type="primary" size="mini" @click="selectCouponVisible = true">选择</el-button>
          <span class="ml-mr">已选择{{ form.productCouponVOList.length }}张优惠券</span>
        </el-form-item>
        <!-- 优惠券表格 -->
        <el-form-item>
          <p style="color: red;">提示: 可拖拽优惠券进行排序</p>
          <el-table ref="tableRef" row-key="couponId" class="coupon-table" size="small" :data="form.productCouponVOList" border>
            <el-table-column type="index" label="序号" align="center" />
            <el-table-column prop="couponId" label="优惠券id" align="center" />
            <el-table-column prop="couponName" label="优惠券名称" align="center" width="135" />
            <el-table-column prop="couponPrice" label="优惠券额度" align="center" width="105" />
            <el-table-column label="适用用户" align="center">
              <template slot-scope="scope">
                {{ scope.row.applyUser | couponUserTypeEnum }}
              </template>
            </el-table-column>
            <el-table-column prop="limitGetNum" label="每人限领" align="center" />
            <el-table-column prop="availableScope" label="适用商品" align="center" width="105" />
            <el-table-column prop="couponTime" label="可领券时间" align="center" width="135">
              <template slot-scope="scope">
                <p> {{ scope.row.couponStartTime }}</p>
                <p> {{ scope.row.couponEndTime }}</p>
              </template>
            </el-table-column>
            <el-table-column prop="validTimeRange" label="优惠券有效时间" align="center" width="157" />
            <el-table-column label="上架状态" align="center">
              <template slot-scope="scope">
                {{ scope.row.status | couponShelfStatusEnum }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="160"
              fixed="right"
            >
              <template slot-scope="scope">
                <div style="display: flex;">
                  <el-button v-if="scope.$index !== 0" style="margin-right: auto;" type="primary" size="mini" circle icon="el-icon-top" @click="handleUpAndDown(scope.$index, scope.$index - 1)" />
                  <el-button v-if="scope.$index !== form.productCouponVOList.length - 1" style="margin-left: auto;" type="primary" size="mini" circle icon="el-icon-bottom" @click="handleUpAndDown(scope.$index, scope.$index + 1)" />
                  <el-button type="primary" size="mini" @click="handleTableDelete(scope.$index)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </div>
    <selectCouponModal :visible.sync="selectCouponVisible" :tablePropData="form.productCouponVOList" @confirm="updateTableData" />
  </common-dialog>
</template>

<script>
import Sortable from 'sortablejs';
import { ossUri } from '@/config/request';
import { arrToEnum } from '@/utils';
import { couponShelfStatus, couponUserType } from './../../type';
const couponUserTypeEnum = arrToEnum(couponUserType);
const couponShelfStatusEnum = arrToEnum(couponShelfStatus);

import selectCouponModal from './select-coupon-modal.vue';
export default {
  components: {
    selectCouponModal
  },
  filters: {
    couponUserTypeEnum(val) {
      return couponUserTypeEnum[val] || '/';
    },
    couponShelfStatusEnum(val) {
      return couponShelfStatusEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    centerConfigId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      couponShelfStatus: couponShelfStatus, // 优惠券上架状态
      couponUserType: couponUserType, // 优惠券适用用户

      isEdit: false,
      selectCouponVisible: false, // 选择优惠券弹框
      confirmLoading: false,
      show: false,
      form: {
        couponCenterHeadImg: '',
        couponWxShareImg: '',
        couponIdList: [],
        productCouponVOList: []
      },
      couponCenterHeadImg: [],
      couponWxShareImg: [],
      rules: {
        couponCenterName: [{ required: true, message: '请输入优惠券名称', trigger: 'blur' }],
        couponWxShareTitle: [{ required: true, message: '请输入微信分享标题', trigger: 'blur' }],
        couponWxShareSubTitle: [{ required: true, message: '请输入微信分享副标题', trigger: 'blur' }],
        couponCenterHeadImg: [{ required: true, message: '请上传领券中心头图', trigger: 'change' }],
        couponWxShareImg: [{ required: true, message: '请上传微信分享图', trigger: 'change' }],
        productCouponVOList: [{ required: true, message: '请选择', trigger: 'change' }]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 表格拖拽排序
    initDragSort() {
      this.$nextTick(() => {
        const tbody = document.querySelector('.coupon-table tbody');
        Sortable.create(tbody, {
          onEnd: ({ oldIndex, newIndex }) => {
            // 交换位置
            const arr = this.form.productCouponVOList;
            const page = arr[oldIndex];
            arr.splice(oldIndex, 1);
            arr.splice(newIndex, 0, page);
          }
        });
      });
    },
    // 表格删除
    handleTableDelete(index) {
      this.form.productCouponVOList.splice(index, 1);
    },
    // 更新表格数据
    updateTableData(data) {
      this.form.productCouponVOList = data;
    },
    // 上移和下移
    handleUpAndDown(oldIndex, newIndex) {
      // 表格
      const productCouponVOList = this.form.productCouponVOList;
      const page = productCouponVOList[oldIndex];
      productCouponVOList.splice(oldIndex, 1);
      productCouponVOList.splice(newIndex, 0, page);
    },
    // 领券中心头图删除
    headImgRemove({ file, fileList }) {
      this.form.couponCenterHeadImg = '';
    },
    // 领券中心头图上传成功
    headImgSuccess({ response, file, fileList }) {
      this.form.couponCenterHeadImg = response;
    },
    // 微信分享图删除
    shareImgRemove({ file, fileList }) {
      this.form.couponWxShareImg = '';
    },
    // 微信分享图上传成功
    shareImgSuccess({ response, file, fileList }) {
      this.form.couponWxShareImg = response;
    },
    // 打开弹框
    open() {
      this.initDragSort();
      if (this.centerConfigId) {
        this.isEdit = true;
        this.$http.get(`/couponCenterConfig/getById/${this.centerConfigId}`).then(res => {
          const { fail, body } = res;
          if (!fail) {
            body.couponIdList = [];
            this.form = body;
            this.couponCenterHeadImg.push({ url: ossUri + body.couponCenterHeadImg });
            this.couponWxShareImg.push({ url: ossUri + body.couponWxShareImg });
          }
        });
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;
        this.confirmLoading = true;

        const form = JSON.parse(JSON.stringify(this.form));
        form.couponIdList = form.productCouponVOList.map(item => item.couponId);
        delete form.productCouponVOList;

        const apiKey = this.isEdit ? 'updateZMCouponCenterConfig' : 'addZMCouponCenterConfig';

        this.$post(apiKey, form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            if (this.isEdit) this.$parent.getCouponCenterConfig();
          }
        }).finally(() => {
          this.show = false;
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ml-mr {
  margin: 0 10px;
}
</style>
