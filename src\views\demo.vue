<template>
  <div>
    <el-steps direction="vertical" :active="2">
      <el-step title="步骤 1">
        <template slot="description">
          <div>123</div>
        </template>
      </el-step>
      <el-step title="步骤 2" />
    </el-steps>
  </div>

</template>
<script>
import { jsPDF } from 'jspdf';
export default {
  data() {
    return {
      selFiles: [],
      pdfWidth: 19
    };
  },

  methods: {
    // 得到pdf内容的高度
    getPdfHeight() {
      let allHeight = 0;
      for (var i = 0; i < this.selFiles.length; i++) {
        const one = this.selFiles[i];
        // 得到高度
        const img = document.getElementById('fg' + one.id);
        allHeight = allHeight + img.height;
        console.log(img.height, 'img.height');
      }
      const pdfHeight = (allHeight * this.pdfWidth) / 400;
      return pdfHeight;
    },
    // 下载pdf
    down() {
      const pdfHeight = this.getPdfHeight();
      // eslint-disable-next-line new-cap
      const recordPdf = new jsPDF({ unit: 'cm', format: [21, pdfHeight + 2] });
      // 遍历图片
      let top = 1;
      for (var i = 0; i < this.selFiles.length; i++) {
        const one = this.selFiles[i];
        // 得到图片所占内容的高度
        const img = document.getElementById('fg' + one.id);
        const destHeight = (img.height * 19) / img.width;
        // pdf内容添加图片
        recordPdf.addImage(img.src, 'jpeg', 1, top, this.pdfWidth, destHeight);
        recordPdf.text(20, 20, ' ');
        top = top + destHeight;
      }
      // 保存pdf
      recordPdf.save('PDF存档.pdf');
    },
    // 选中图片时，把图片添加到数组
    handleFile(e) {
      const filePaths = e.target.files;
      // 清空原有缩略图
      if (filePaths.length === 0) {
        // 未选择，则返回
        return;
      } else {
        // 清空数组中原有图片
        this.selFiles.length = 0;
      }
      // 把新选中的图片加入数组
      for (var i = 0; i < filePaths.length; i++) {
        const file = filePaths[i];
        const one = {
          id: i,
          fileimg: URL.createObjectURL(file), // 预览用
          file: file
        };
        this.selFiles.push(one);
        console.log(this.selFiles, 'selFiles');
      }
    }
  }

};
</script>
