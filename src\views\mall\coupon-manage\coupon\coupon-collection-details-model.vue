<template>
  <common-dialog
    class="common-dialog"
    width="100%"
    :isFull="true"
    title="领取详情"
    :visible.sync="show"
    :show-footer="false"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <!-- 顶部筛选 -->
      <el-form
        ref="searchForm"
        :model="form"
        label-width="120px"
        class="yz-search-form"
        size="mini"
        label-suffix=":"
        @submit.native.prevent="search"
      >
        <el-form-item label="用户姓名" prop="realName">
          <el-input
            v-model="form.realName"
            placeholder="请输入用户姓名"
            clearable
          />
        </el-form-item>
        <el-form-item label="远智编号" prop="yzCode">
          <el-input
            v-model="form.yzCode"
            placeholder="请输入下单人远智编号"
            clearable
          />
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input
            v-model="form.mobile"
            placeholder="请输入手机号码"
            clearable
          />
        </el-form-item>
        <el-form-item label="领券方式" prop="couponGetType">
          <el-select
            v-model="form.couponGetType"
            placeholder="请选择领券方式"
            clearable
          >
            <el-option
              v-for="item in couponNeckType"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="优惠券状态" prop="couponStatus">
          <el-select
            v-model="form.couponStatus"
            placeholder="请选择优惠券状态"
            clearable
          >
            <el-option
              v-for="item in couponStatus"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <div class="search-reset-box">
          <el-button
            type="primary"
            native-type="submit"
            size="mini"
          >查询</el-button>
          <el-button size="mini" @click="search(0)">重置</el-button>
        </div>
      </el-form>

      <!-- 新增栏 -->
      <div style="float: right; margin: 15px 25px 15px 0">
        <el-button
          type="primary"
          size="small"
          @click="exportData"
        >导出</el-button>
      </div>

      <el-table
        ref="table"
        v-loading="tableLoading"
        size="small"
        :data="tableData"
        style="width: 100%; margin: 25px 0"
        border
      >
        <el-table-column label="赠送对象" align="center" width="150">
          <template slot-scope="scope">
            <div style="text-align: left">
              <p>远智编号: {{ scope.row.yzCode || '无' }}</p>
              <p>真实姓名: {{ scope.row.realName || '无' }}</p>
              <p>昵称: {{ scope.row.nickname || '无' }}</p>
              <p>手机号: {{ scope.row.mobile || '无' }}</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="领券时间" align="center" />
        <el-table-column prop="couponStatus" label="优惠券状态" align="center">
          <template slot-scope="scope">
            {{ scope.row.couponStatus | couponStatusEnum }}
          </template>
        </el-table-column>
        <el-table-column prop="couponGetType" label="领券方式" align="center">
          <template slot-scope="scope">
            {{ scope.row.couponGetType | couponNeckTypeEnum }}
          </template>
        </el-table-column>
        <el-table-column prop="couponGiver" label="派券人" align="center" />
        <el-table-column prop="orderId" label="优惠券对应订单" align="center" />
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
import { httpPostDownFile } from '@/utils/downExcelFile';
import { arrToEnum } from '@/utils';
import { couponStatus, couponNeckType } from './../../type';
const couponStatusEnum = arrToEnum(couponStatus);
const couponNeckTypeEnum = arrToEnum(couponNeckType);
export default {
  filters: {
    couponStatusEnum(val) {
      return couponStatusEnum[val] || '/';
    },
    couponNeckTypeEnum(val) {
      return couponNeckTypeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentRow: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      couponNeckType: couponNeckType,
      couponStatus: couponStatus,
      show: false,
      currentId: '',
      form: {
        realName: '',
        yzCode: '',
        mobile: '',
        couponGetType: '',
        couponStatus: ''
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableLoading: false,
      tableData: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 导出数据
    exportData() {
      const params = this.handleQueryParams();
      httpPostDownFile({
        url: '/usProductCoupon/exportRecord',
        params
      });
    },
    open() {
      if (this.currentRow.couponId) this.getTableList();
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 查询和重置
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 处理筛选参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        couponId: this.currentRow.couponId,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQueryParams();
      this.$post('getZMUsProductCouponList', params, { json: true }).then(
        (res) => {
          const { fail, body } = res;
          if (!fail) {
            this.tableLoading = false;
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
          }
        }
      );
    }
  }
};
</script>

<style lang="scss" scoped>
.option-box {
  margin: 15px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    color: red;
  }
}
</style>
