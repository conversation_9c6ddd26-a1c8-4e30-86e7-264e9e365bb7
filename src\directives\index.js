
// 指令 v-width 设置高度
export function width(el, binding, vnode) {
  el.style['width'] = binding.value + 'px';
}

// element 下拉组件加载
export const loadmore = {
  bind(el, binding) {
    // 获取element-ui定义好的scroll盒子
    const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
    SELECTWRAP_DOM.addEventListener('scroll', function() {
      /**
       * scrollHeight 元素内容高度
       * scrollTop 元素滚动的偏移量
       * clientHeight 元素的可见高度
       */
      // 如果元素滚动到底, 下面等式返回true, 没有则返回false, 这里的3是给予一定的误差，以防止检测不到滚动到底部
      const CONDITION = this.scrollHeight - this.scrollTop - 3 <= this.clientHeight; // 说明滚动到底
      if (CONDITION) {
        // 执行 v-loadmore='loadMore' 传入的函数
        binding.value();
      }
    });
  }
};

export const elFocus = {
  // 当被绑定的元素插入到 DOM 中时……
  inserted: function(el) {
    const input = el.querySelector('.el-input__inner');
    input.focus();
  }
};

export const preventReClick = {
  inserted: function(el, binding) {
    el.addEventListener('click', () => {
      if (!el.disabled) {
        el.disabled = true;
        setTimeout(() => {
          el.disabled = false;
        }, binding.value || 3000);
      }
    });
  }
};
