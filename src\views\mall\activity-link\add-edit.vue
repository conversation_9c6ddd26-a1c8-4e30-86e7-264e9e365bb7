<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    :title="`${ isEdit ? '修改' : '新增'}` + '商城活动链接配置'"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        size="small"
      >
        <el-form-item label="活动标题:" prop="activityName">
          <el-input v-model="form.activityName" placeholder="请输入活动标题" />
        </el-form-item>

        <el-form-item label="活动页头图:" prop="urlList">
          <p>建议图片尺寸为686*270, 图片大小限制1M内</p>
          <upload-file
            :size="1"
            :max-limit="1"
            exts="jpg|png"
            :file-list="urlList"
            @remove="goodsImgRemove"
            @success="goodsImgSuccess"
          />
        </el-form-item>

        <el-form-item label="活动状态:" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="选择商品:" prop="productIdList">
          <el-button type="primary" @click="selectGoodsVisible = true">添加</el-button>
          <span class="ml10">已选取{{ tableData.length }}个商品</span>
          <el-table ref="tableRef" class="mt10" size="small" :data="tableData" border>
            <el-table-column prop="id" label="商品id" align="center" />
            <el-table-column prop="productName" label="商品名称" align="center" />
            <el-table-column prop="productType" label="商品类型" align="center">
              <template slot-scope="scope">
                {{ scope.row.productType | goodsTypeEnum }}
              </template>
            </el-table-column>
            <el-table-column prop="weight" label="商品权重" align="center" />
            <el-table-column label="兑换起止时间" align="center">
              <template slot-scope="scope">
                <p>起：{{ scope.row.sellingStartTime }}</p>
                <p>止：{{ scope.row.sellingEndTime || '永久有效' }}</p>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleTableDelete(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </div>
    <select-goods-modal :visible.sync="selectGoodsVisible" :tablePropData="tableData" @confirm="updateTableData" />
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
import { arrToEnum } from '@/utils';
import { goodsType } from './../type';
const goodsTypeEnum = arrToEnum(goodsType);
import selectGoodsModal from './../components/select-goods-modal.vue';
export default {
  components: {
    selectGoodsModal
  },
  filters: {
    goodsTypeEnum(val) {
      return goodsTypeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      confirmLoading: false,
      selectGoodsVisible: false, // 选择商品弹框
      show: false,
      isEdit: false, // 是否编辑
      rules: {
        activityName: [
          { required: true, message: '请输入活动标题', trigger: 'blur' }
        ],
        urlList: [
          { required: true, message: '请上传活动页头图', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择活动状态', trigger: 'change' }
        ],
        productIdList: [
          { required: true, message: '请选择商品', trigger: 'change' }
        ]
      },
      form: {
        urlList: [],
        productIdList: []
      },
      urlList: [],
      tableData: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 表格删除
    handleTableDelete(index) {
      this.tableData.splice(index, 1);
      this.form.productIdList = this.tableData.map(item => item.id);
    },
    // 更新表格数据
    updateTableData(data) {
      this.tableData = data;
      this.form.productIdList = data.map(item => item.id);
    },
    // 图片删除
    goodsImgRemove({ file, fileList }) {
      let index = 0;
      for (let i = 0; i < this.form.urlList.length; i++) {
        const item = this.form.urlList[i];
        if (item.fileUrl === file.response) {
          index = i;
          break;
        }
      }

      this.form.urlList.splice(index, 1);
    },
    // 图片上传成功
    goodsImgSuccess({ response, file, fileList }) {
      this.form.urlList.push({ fileUrl: file.response });
    },
    // 打开弹框
    open() {
      if (this.currentId) {
        this.isEdit = true;
        this.$http.get(`/productActivityConfig/getById/${this.currentId}`).then(res => {
          const { fail, body } = res;
          if (!fail) {
            const { id, activityName, imgUrlList, productList, status } = body;
            this.tableData = productList;
            this.form = {
              id,
              activityName,
              status,
              productIdList: this.tableData.map(item => item.id),
              urlList: []
            };
            imgUrlList.forEach(item => {
              this.urlList.push({ url: ossUri + item });
              this.form.urlList.push({ fileUrl: item });
            });
          }
        });
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        this.confirmLoading = true;

        const form = JSON.parse(JSON.stringify(this.form));
        form.urlList = form.urlList.map(item => item.fileUrl);

        let apiKey = 'addZMActivityLink';
        if (this.isEdit) {
          apiKey = 'updateZMActivityLink';
        }
        this.$post(apiKey, form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>

<style>

</style>
