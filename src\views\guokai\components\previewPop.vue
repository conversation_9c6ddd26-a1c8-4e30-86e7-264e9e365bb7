<template>
  <el-dialog
    class="preview-box"
    :visible.sync="show"
    append-to-body
    width="408px"
    :before-close="handleClose"
  >
    <div class="preview-main">
      <!-- :before-close="handleClose" -->
      <div class="preview-header">
        <img src="../../../assets/imgs/guokai/moblie-head.png" alt="" />
      </div>
      <div class="preview-content">
        <h3>{{ form.customGuidanceTitle }}</h3>
        <div v-html="form.customGuidanceContent"></div>
      </div>
      <div class="preview-bot">
        <div v-if="form.gotoThirdTeachPlatform" class="preview-btn">进入网站学习</div>
        <img src="../../../assets/imgs/guokai/moblie-bot.png" alt="" />
      </div>
      <i class="el-icon-circle-close" @click="show = false"></i>
    </div>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {},
    submit() {},
    handleClose() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.show = false;
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

.preview-box{
  // font-size: 100px!important;
  ::v-deep .el-dialog__header{
    padding: 0;
  }
  ::v-deep .el-dialog__body{
    padding: 0;
    border-radius: 20px;
    color: #000;
  }
  .preview-main{
    position: relative;
    border-radius: 8px;
    height: 600px;
    .preview-header{
      width: 100%;
      height: 85px;
      img{
        width: 100%;
        height: 85px;
      }
    }
    .preview-bot{
      position: absolute;
      bottom: 0px;
      // position: fixed;
      // width: 100%;
      width: 100%;
      .preview-btn{
        border-top: 5px solid #FFFFFF;
        // cursor: pointer;
        height: 50px;
        line-height: 45px;
        background: linear-gradient(135deg, #F09190 0%, #F07877 66%, #F06E6C 100%);
        font-size: 15px;
        text-align: center;
        font-weight: 600;
        color: #FFFFFF;
      }
      img{
        width: 100%;
        height: 35px;
      }
    }
    .preview-content{
      padding:0px 16px 22px;
      overflow-y: scroll;
      height: 435px;
      h3{
        margin: 16px 0 22px;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        color: #333333;
      }
      ::v-deep img{
        width: 100%!important;
        height: auto!important;
      }
      ::v-deep div{
        width: auto!important;
      }
    }
    i{
      cursor: pointer;
      font-size: 30px;
      color: #ddd;
      position:absolute;
      bottom: -45px;
      left: 50%;
      transform: translate(-50%);
    }
  }
}
</style>
