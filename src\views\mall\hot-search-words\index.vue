<template>
  <div class="yz-base-container">
    <!-- 顶部筛选 -->
    <el-form
      ref="searchForm"
      :model="form"
      label-width="110px"
      class="yz-search-form"
      size="mini"
      @submit.native.prevent='search'
    >
      <el-form-item label="热门搜索词:" prop="content">
        <el-input v-model="form.content" placeholder="请输入热门搜索词" clearable />
      </el-form-item>
      <el-form-item label="状态:" prop="status">
        <el-select v-model="form.status" placeholder="请选择类型:" clearable>
          <el-option label="已启用" :value="1" />
          <el-option label="已禁用" :value="0" />
        </el-select>
      </el-form-item>
      <div class="search-reset-box">
        <el-button
          type="primary"
          native-type="submit"
          size="mini"
        >查询</el-button>
        <el-button size="mini" @click="search(0)">重置</el-button>
      </div>
    </el-form>
    <!-- 新增栏 -->
    <div class="option-box">
      <p class="left">备注：将根据搜索词权重及展示时间，展示前8个启用状态的热门搜索词</p>
      <el-button type="primary" size="mini" @click="openAddModal">新增热门搜索词</el-button>
    </div>
    <el-table ref="table" v-loading="tableLoading" size="small" :data="tableData" style="width: 100%; margin: 25px 0" border>
      <el-table-column prop="content" label="热门搜索词" align="center" />
      <el-table-column prop="weight" label="搜索词权重" align="center" />
      <el-table-column label="展示时间" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.showTimeStatus == 'TIME_EFFECTIVE'">
            <p>起：{{ scope.row.showStartTime }}</p>
            <p>止：{{ scope.row.showEndTime }}</p>
          </template>
          <template v-else>
            <span>永久有效</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.status | yesOrNoEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="updateBy" label="最后操作人" align="center" />
      <el-table-column prop="updateTime" label="最后操作时间" align="center" />
      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button :type="scope.row.status == 1 ? 'info': 'primary'" size="small" @click="handleActivityStatus(scope.row)">{{ scope.row.status == 1 ? '禁用': '启用' }}</el-button>
          <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <add-edit :visible.sync="aeVisible" :currentId="currentId" />
  </div>
</template>

<script>
import { arrToEnum } from '@/utils';
import addEdit from './add-edit';
import { jumpPageType } from './../type';
const jumpPageTypeEnum = arrToEnum(jumpPageType);
export default {
  components: {
    addEdit
  },
  filters: {
    yesOrNoEnum(val) {
      const JudgeEnum = { 0: '禁用', 1: '启用' };
      return JudgeEnum[val] || '/';
    },
    jumpPageTypeEnum(val) {
      return jumpPageTypeEnum[val] || '/';
    }
  },
  data() {
    return {
      jumpPageType: jumpPageType,
      aeVisible: false, // 热门搜索词弹框
      currentId: '',
      form: {
        content: '',
        status: ''
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableLoading: false,
      tableData: []
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    // 改变状态
    handleActivityStatus(row) {
      const status = row.status == 1 ? 0 : 1;
      this.$http.post(`/mallHotSearchConfig/updateStatus/${row.id}/${status}`, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message.success('操作成功');
          this.getTableList();
        }
      });
    },
    openAddModal() {
      this.currentId = '';
      this.aeVisible = true;
    },
    // 编辑
    handleEdit(row) {
      this.currentId = row.id;
      this.aeVisible = true;
    },
    // 查询和重置
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 处理筛选参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQueryParams();
      this.$post('getZMHotSearchWordsList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>

<style lang = "scss" scoped>
.option-box {
  margin: 15px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    color:red;
  }
}
</style>
