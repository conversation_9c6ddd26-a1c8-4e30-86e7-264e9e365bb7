<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    title="新增优惠券赠送"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        size="small"
        label-suffix=":"
      >
        <el-form-item
          label='赠送优惠券'
          prop='couponId'
        >
          <infinite-selects
            v-model="form.couponId"
            placeholder="请选择赠送优惠券"
            :isJson="true"
            :props="{
              apiName: 'getZMCanSendCouponList',
              value: 'couponId',
              label: 'couponName',
              query: 'couponName'
            }"
            @changeVal="couponSelectChange"
          />
        </el-form-item>
        <template v-if="form.couponId">
          <el-form-item label="优惠券id">
            <span>{{ couponObj.couponId }}</span>
          </el-form-item>
          <el-form-item label="优惠券名称">
            <span>{{ couponObj.couponName }}</span>
          </el-form-item>
          <el-form-item label="优惠券类型">
            <span>{{ couponObj.couponType | couponTypeEnum }}</span>
          </el-form-item>
          <el-form-item label="优惠额度">
            <span>{{ couponObj.couponPrice }}</span>
          </el-form-item>
          <el-form-item label="每人限领">
            <span>{{ couponObj.limitGetNum }}张</span>
          </el-form-item>
        </template>
        <el-form-item label="赠送用户" prop="sendType">
          <el-radio-group v-model="form.sendType">
            <el-radio :label="1">筛选用户</el-radio>
            <el-radio :label="2">导入用户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.sendType == 1" label="筛选用户" prop="userSelectList">
          <el-button type="primary" size="mini" @click="selectUserVisible = true">选择用户</el-button>
          <span class="ml10">已选择{{ form.userSelectList.length }}个用户</span>
        </el-form-item>
        <template v-if="form.sendType == 2">
          <el-form-item label="导入模板">
            <a :href="templateUrl" download>
              <el-button type="primary" size="mini">下载模板</el-button>
            </a>
          </el-form-item>
          <el-form-item
            label="选择文件"
            prop="fileList"
          >
            <el-upload
              ref="upload"
              class="upload-demo"
              drag
              :action="action"
              :data="extraData"
              :on-change="handleChange"
              :on-exceed="handleExceed"
              :on-success="uploadSuccess"
              :on-remove="handleRemove"
              :name="field"
              :file-list="form.fileList"
              multiple
              :limit="1"
              :auto-upload="false"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击选择文件</em></div>
            </el-upload>
          </el-form-item>
        </template>
        <el-form-item label="每人赠送" prop="couponGiveNum">
          <el-input-number
            v-model="form.couponGiveNum"
            placeholder="请输入每人赠送"
            :controls="false"
            :min="1"
            :precision="0"
          />
          <span class="ml10">张</span>
        </el-form-item>
        <el-form-item label="赠送优惠券备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="4" placeholder="请输入赠送优惠券备注" maxlength="100" show-word-limit clearable />
        </el-form-item>
      </el-form>
      <selectUserModal :visible.sync="selectUserVisible" :tablePropData="form.userSelectList" @confirm="updateTableData" />
    </div>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
import { arrToEnum } from '@/utils';
import { couponType } from './../../type';
import selectUserModal from '../../components/select-user-modal.vue';
const couponTypeEnum = arrToEnum(couponType);
export default {
  components: {
    selectUserModal
  },
  filters: {
    couponTypeEnum(val) {
      return couponTypeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      templateUrl: ossUri + '/product/%E4%BC%98%E6%83%A0%E5%88%B8%E8%B5%A0%E9%80%81%E7%94%A8%E6%88%B7.xlsx',
      field: 'file',
      confirmLoading: false,
      selectUserVisible: false,
      action: '/couponGiveRecord/add',
      fileList: [], // 选择的文件
      couponObj: {}, // 选择的优惠券
      show: false,
      extraData: {
        couponId: undefined,
        couponGiveNum: undefined,
        remark: undefined,
        sendType: undefined
      },
      form: {
        fileList: [],
        userSelectList: []
      },
      rules: {
        couponId: [{ required: true, message: '请选择赠送优惠券', trigger: 'change' }],
        sendType: [{ required: true, message: '请选择赠送用户', trigger: 'change' }],
        userSelectList: [{ required: true, message: '请选择用户', trigger: 'change' }],
        couponGiveNum: [{ required: true, message: '请输入每人赠送', trigger: 'blur' }],
        fileList: [{ required: true, message: '请选择文件', trigger: 'change' }],
        remark: [{ required: true, message: '请输入赠送优惠券备注', trigger: 'blur' }]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 更新表格数据
    updateTableData(data) {
      this.form.userSelectList = data;
    },
    // 优惠券改变
    couponSelectChange({ value, label, source }) {
      this.couponObj = source;
    },
    // 上传文件
    handleChange(file, fileList) {
      this.form.fileList = fileList;
    },
    // 移除文件
    handleRemove(file, fileList) {
      this.form.fileList = fileList;
    },
    // 上传成功
    uploadSuccess(response, file, fileList) {
      this.$refs.upload.clearFiles();
      if (response.code === '00') {
        this.confirmLoading = false;
        this.show = false;
        this.$message({
          message: '导入成功',
          type: 'success'
        });
        this.$emit('refresh');
      } else {
        this.confirmLoading = false;
        const h = this.$createElement;
        this.$alert(h('div', { style: 'max-height: 60vh;overflow-y: auto;' }, [
          h('p', { domProps: { innerHTML: response.body }})
        ]), response.msg, {
          dangerouslyUseHTMLString: true, // 允许渲染 HTML
          showClose: false
        });
      }
    },
    // 上传文件超出限制
    handleExceed() {
      this.$message.error('每次只能上传一个文件');
    },
    // 打开弹框
    open() {},
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        // 每人赠送数 > 限领数
        if (this.form.couponGiveNum > this.couponObj.limitGetNum) {
          return this.$message.error('每人赠送数>限领数！请重新填写~');
        }

        this.$confirm('确定导入？导入后不可取消！请谨慎操作。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.confirmLoading = true;

          if (this.form.sendType == 1) {
            const form = new FormData();
            form.append('couponId', this.form.couponId);
            form.append('couponGiveNum', this.form.couponGiveNum);
            form.append('remark', this.form.remark);
            form.append('sendType', this.form.sendType);
            form.append('userIds', this.form.userSelectList.map(item => item.userId));
            this.$post('addZMCouponGiveRecord', form, { uploadFile: true }).then(res => {
              const { fail } = res;
              if (!fail) {
                this.show = false;
                this.$message({
                  message: '导入成功',
                  type: 'success'
                });
                this.$parent.getTableList();
              }
            });
          } else {
            this.extraData.couponId = this.form.couponId;
            this.extraData.couponGiveNum = this.form.couponGiveNum;
            this.extraData.remark = this.form.remark;
            this.extraData.sendType = this.form.sendType;
            this.$refs.upload.submit();
          }
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
