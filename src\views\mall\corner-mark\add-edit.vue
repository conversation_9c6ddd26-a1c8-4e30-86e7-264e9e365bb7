<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    :title="`${ isEdit ? '修改' : '新增'}` + '角标配置'"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        size="small"
      >
        <el-form-item label="角标名称:" prop="markName">
          <el-input v-model="form.markName" placeholder="请输入角标名称" maxlength="6" show-word-limit />
        </el-form-item>

        <el-form-item label="展示时间:" required>
          <div style="display: flex;">
            <el-form-item prop="showTimeStatus">
              <el-radio-group v-model="form.showTimeStatus">
                <el-radio label="PERMANENT_EFFECTIVE">永久有效</el-radio>
                <el-radio label="TIME_EFFECTIVE">固定时间</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="form.showTimeStatus == 'TIME_EFFECTIVE'" class="ml10" prop="unveilTime">
              <el-date-picker
                v-model="form.unveilTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </div>
        </el-form-item>

        <el-form-item label="角标权重:" prop="weight">
          <div style="display: flex;">
            <el-input-number v-model="form.weight" :controls="false" :min="0" :max="9999" placeholder="请输入角标权重" style="width: 200px" />
            <span>（相同商品，优先展示权重越高的角标）</span>
          </div>
        </el-form-item>

        <el-form-item label="选择商品:" prop="productIdList">
          <el-button type="primary" @click="selectGoodsVisible = true">添加</el-button>
          <span class="ml10">已选取{{ tableData.length }}个商品</span>
          <el-table ref="tableRef" class="mt10" size="small" :data="tableData" border>
            <el-table-column prop="id" label="商品id" align="center" />
            <el-table-column prop="productName" label="商品名称" align="center" />
            <el-table-column prop="productType" label="商品类型" align="center">
              <template slot-scope="scope">
                {{ scope.row.productType | goodsTypeEnum }}
              </template>
            </el-table-column>
            <el-table-column prop="weight" label="商品权重" align="center" />
            <el-table-column label="兑换起止时间" align="center">
              <template slot-scope="scope">
                <p>起：{{ scope.row.sellingStartTime }}</p>
                <p>止：{{ scope.row.sellingEndTime || '永久有效' }}</p>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleTableDelete(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <el-form-item label="状态:" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <select-goods-modal :visible.sync="selectGoodsVisible" :tablePropData="tableData" @confirm="updateTableData" />
  </common-dialog>
</template>

<script>
import { arrToEnum } from '@/utils';
import { goodsType } from './../type';
const goodsTypeEnum = arrToEnum(goodsType);
import selectGoodsModal from './../components/select-goods-modal.vue';
export default {
  components: {
    selectGoodsModal
  },
  filters: {
    goodsTypeEnum(val) {
      return goodsTypeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      confirmLoading: false,
      selectGoodsVisible: false, // 选择商品弹框
      show: false,
      isEdit: false, // 是否编辑
      rules: {
        markName: [
          { required: true, message: '请输入角标名称', trigger: 'blur' }
        ],
        cornerTimeRadio: [
          { required: true, message: '请选择展示时间', trigger: 'change' }
        ],
        cornerTime: [
          { required: true, message: '请选择展示时间', trigger: 'change' }
        ],
        weight: [
          { required: true, message: '请输入角标权重', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择展示状态', trigger: 'change' }
        ],
        productIdList: [
          { required: true, message: '请选择商品', trigger: 'change' }
        ]
      },
      form: {
        productIdList: []
      },
      tableData: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 表格删除
    handleTableDelete(index) {
      this.tableData.splice(index, 1);
      this.form.productIdList = this.tableData.map(item => item.id);
    },
    // 更新表格数据
    updateTableData(data) {
      this.tableData = data;
      this.form.productIdList = data.map(item => item.id);
    },
    // 打开弹框
    open() {
      if (this.currentId) {
        this.isEdit = true;
        this.$http.get(`/productMarkConfig/getById/${this.currentId}`).then(res => {
          const { fail, body } = res;
          if (!fail) {
            const {
              id,
              markName,
              showTimeStatus,
              showStartTime,
              showEndTime,
              weight,
              productList,
              status
            } = body;
            this.tableData = productList;
            this.form = {
              id,
              markName,
              showTimeStatus,
              unveilTime: showTimeStatus == 'TIME_EFFECTIVE' ? [showStartTime, showEndTime] : [],
              weight,
              productIdList: this.tableData.map(item => item.id),
              status
            };
          }
        });
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        this.confirmLoading = true;

        const form = JSON.parse(JSON.stringify(this.form));
        if (form.showTimeStatus == 'TIME_EFFECTIVE') {
          form.showStartTime = form.unveilTime[0];
          form.showEndTime = form.unveilTime[1];
        } else {
          delete form.showStartTime;
          delete form.showEndTime;
        }
        delete form.unveilTime;

        let apiKey = 'addZMCornerMark';
        if (this.isEdit) {
          apiKey = 'updateZMCornerMark';
        }
        this.$post(apiKey, form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>

<style>

</style>
