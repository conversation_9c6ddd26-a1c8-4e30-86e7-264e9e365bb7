<template>
  <div class="news-head">
    <div class="head-logo">{{ title }}&nbsp;&nbsp;&nbsp;&nbsp;PK</div>
    <div
      v-for="(vosItem, vosInx) in list"
      :key="vosInx"
      class="head-li"
      :style="{ 'width': vosItem.width + '%' }"
    >
      <div class="li-top">
        <div class="top-avatar">
          <el-avatar
            class="top-heads"
            :src="vosItem.teamAvatar | formatOssImgUrl"
            :style="{
              background: `url(${require(`@/assets/imgs/helpStudyPkRank/img_vsbg${vosInx}.png`)}) no-repeat`, backgroundSize: '100% 100%'
            }"
          >
            <img v-if="vosInx == 0" src="@/assets/imgs/helpStudyPkRank/defaultPK1.png" />
            <img v-else src="@/assets/imgs/helpStudyPkRank/defaultPK2.png" />
          </el-avatar>
          <template v-if="vosItem.isWin">
            <img class="top-headbg" src="@/assets/imgs/helpStudyPkRank/avatar-bg.png" alt="" />
            <img class="top-icon" src="@/assets/imgs/helpStudyPkRank/myRankIcon.png" alt="" />
          </template>
        </div>
        <div class="top-infor" :style="{ marginLeft: vosItem.isWin ? '50px' : '20px' }">
          <div class="top-name">总指挥：{{ vosItem.empName || vosItem.topEmpName }}</div>
          <p>{{ vosItem.teamName || vosItem.supTeamName }}</p>
        </div>

      </div>
      <div class="li-bom">
        <p v-if="vosItem.isWin" class="bom-win">领先</p>
        <div class="top-score">
          <span class="score-title">{{ title }}：</span>
          <span class="score">{{ vosItem.totalOrders }}</span>
        </div>
      </div>
      <img v-if="!vosInx" class="li-pks" src="@/assets/imgs/helpStudyPkRank/img_pk2.png" alt="" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'PkProgress',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    }
  }
};
</script>

<style lang="scss" scoped>
.news-head {
  padding-top: 10px;
  padding-bottom: 30px;
  overflow: hidden;
  width: 100%;
  position: relative;
  display: flex;
  justify-content: space-between;

  .head-logo {
    width: 288px;
    height: 42px;
    line-height: 46px;
    text-align: center;
    font-weight: 550;
    font-size: 19px;
    color: #ffffff;
    background-image: url("~@/assets/imgs/helpStudyPkRank/img_vstitle.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 0;
    left: calc(50% - 144px);
  }

  .head-li {
    margin: 20px 20px 0;
    position: relative;
    min-width: 170px;
    width: 50%;
    animation: li-bomact 1s ease-in-out;
    will-change: width;

    .li-top {
      position: absolute;
      left: 0;
      top: 0;
      display: flex;
      align-items: center;

      .top-avatar {
        min-width: 110px;
        width: 110px;
        height: 125px;
        position: relative;

        .top-heads {
          position: absolute;
          left: 0;
          top: 0;
          z-index: 5;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          ::v-deep>img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background-color: #fff;
            margin-top: -15px;
          }
        }

        .top-headbg {
          position: absolute;
          left: -21px;
          top: -20px;
          z-index: 1;
          width: 155px;
          height: 155px;
          background-color: transparent;
          animation: li-rotFir 10s linear infinite;
        }

        .top-icon {
          position: absolute;
          left: -28px;
          top: -30px;
          z-index: 99;
          width: 70px;
          height: 70px;
          background-color: transparent;
        }
      }

      .top-infor {
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;

        .top-name {
          white-space: nowrap;
          margin-bottom: 10px;
          font-size: 20px;
        }
      }
    }

    .li-bom {
      margin-top: 175px;
      height: 50px;
      background: linear-gradient(90deg, rgb(243, 93, 91) 20%, rgb(248, 137, 135) 100%);
      box-shadow:
        inset 0px 4px 20px 0px rgba(255, 255, 255, 0.4),
        inset 0px -4px 20px 0px rgba(255, 255, 255, 0.4);
      width: 100%;
      border-radius: 28px 0px 0px 28px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: relative;

      .top-score {
        position: absolute;
        right: 0;
        top: -50px;
        margin: auto 77px 11px auto;

        .score-title {
          font-size: 15px;
          line-height: 21px;
        }

        .score {
          font-weight: 600;
          font-size: 30px;
          color: #ffd387;
          line-height: 42px;
        }
      }

      .bom-win {
        margin: 0 76px;
        width: 78px;
        height: 32px;
        font-weight: 600;
        font-size: 16px;
        color: #f06e6c;
        line-height: 32px;
        text-align: center;
        background: #ffffff;
        border-radius: 24px;
      }
    }

    .li-pks {
      position: absolute;
      bottom: -40px;
      right: -77px;
      z-index: 2;
      width: 153px;
      height: 153px;
      object-fit: contain;
      animation: li-bompks 1s linear;
    }
  }

  .head-li:nth-last-child(2) {
    margin-right: 0;
  }

  .head-li:nth-last-child(1) {
    margin-left: 0;
    transform: scaleX(-1);

    .li-top {
      text-align: right;

      .top-heads,
      .top-infor {
        transform: scaleX(-1);
      }

      .top-headbg {
        animation-name: li-rotSed;
      }
    }

    .li-bom {
      background: linear-gradient(90deg, #4977feff 20%, #5f99feff 100%);

      .top-score {
        transform: scaleX(-1);
      }

      .bom-score {
        padding-left: 0;
        padding-right: 20px;
        transform: scaleX(-1);
      }

      .bom-win {
        color: #4977fe;
        transform: scaleX(-1);
      }
    }
  }
}

@keyframes li-bomact {
  from {
    width: 50%;
  }
}

@keyframes li-bompks {
  from {
    transform: scale(0);
  }

  to {
    transform: scale(1.2);
  }
}

@keyframes li-rotFir {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

@keyframes li-rotSed {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(-359deg);
  }
}
</style>
