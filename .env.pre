NODE_ENV = production

VUE_APP_RUN_ENV = 'pre'

# just a flag 这样写没用的 要用VUE_APP开头 才有效
ENV = 'staging'

# 192 环境
VUE_APP_DOWN_URL_API = 'http://pre-bms.yzwill.cn'
# VUE_APP_OSS_URL = //img2.yzwill.cn/
VUE_APP_BST_URL = 'http://pre-bst.yzwill.cn'
VUE_APP_DOWN_OSS_URL = //senspre.yzwill.cn
VUE_APP_OSS_URL = //new-yzpres.oss-cn-shenzhen.aliyuncs.com/

# 171 150 环境
# VUE_APP_OSS_URL = //yzimstest.oss-cn-shenzhen.aliyuncs.com/

# bms 51 环境
# VUE_APP_DOWN_URL_API = 'http://pre3-bsm.yzwill.cn'
# VUE_APP_OSS_URL = //yzpres.oss-cn-guangzhou.aliyuncs.com/
# VUE_APP_OSS_URL = //new-yzpres.oss-cn-shenzhen.aliyuncs.com/
# VUE_APP_BST_URL = 'http://pre3-bst.yzwill.cn'

# 网报api请求接口地址
VUE_APP_NET_NEWSPAPER_API = https://bms.yzwill.cn

# 成人教育教育考试院代理地址
VUE_APP_EEA_PROXY_URL = https://35-ijiaolian-mp.yzwill.cn

# BMS 域名
# VUE_APP_BMS_URL = //pre3-bms.yzwill.cn
# VUE_APP_BMS_URL = //test0-bms.yzwill.cn
# VUE_APP_BMS_URL = //test1-bms.yzwill.cn
VUE_APP_BMS_URL = //pre-bms.yzwill.cn
