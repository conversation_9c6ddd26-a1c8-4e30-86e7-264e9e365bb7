<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    title="查看订单信息"
    :visible.sync="show"
    :show-footer="false"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <el-table size="small" :data="tableData" border>
        <el-table-column prop="createTime" label="变更时间" align="center" />
        <el-table-column prop="createBy" label="操作人" align="center" />
        <el-table-column prop="operate" label="变更内容" align="center" />
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      tableData: [],
      tableLoading: false,
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框
    open() {
      if (this.currentId) {
        this.getTableList();
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = {
        orderId: this.currentId,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
      this.$post('getZMOrderRecord', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>

<style>

</style>
