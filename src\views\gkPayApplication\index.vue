<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form ref="searchForm" size="mini" label-width="130px" class="yz-search-form" :model="form"
      @submit.native.prevent="search">
      <el-form-item label="学员姓名" prop="applyName">
        <el-input v-model="form.applyName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="证件号码" prop="idCard">
        <el-input v-model="form.idCard" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="form.mobile" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="远智编码" prop="yzCode">
        <el-input v-model="form.yzCode" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="学业编码" prop="learnId">
        <el-input v-model="form.learnId" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="层次" prop="pfsnLevel">
        <el-select v-model="form.pfsnLevel" placeholder="请选择" clearable>
          <el-option v-for="(item, index) in schoolLevel" :key="index" :label="item.dictName" :value="item.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select v-model="form.grade" filterable clearable placeholder="请选择">
          <el-option v-for="(item, index) in grade" :key="index" :label="item.dictName" :value="item.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item label="院校" prop="unvsId">
        <el-select v-model="form.unvsId" filterable clearable placeholder="请选择">
          <el-option v-for="item in unvsList" :key="item.unvsId" :label="item.unvsName" :value="item.unvsId" />
        </el-select>
      </el-form-item>
      <el-form-item label="专业" prop="pfsnName">
        <el-input v-model="form.pfsnName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="申请时间" prop="createTime">
        <el-date-picker v-model="createTime" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" @change="createTimeChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="第一年学费" prop="status">
        <el-select v-model="form.status" clearable placeholder="请选择">
          <el-option label="已缴费" value="1"></el-option>
          <el-option label="待缴费" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="缴费时间" prop="payTime">
        <el-date-picker v-model="payTime" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" @change="payTimeChange">
        </el-date-picker>
        <!-- <el-date-picker
          v-model="form.payTime"
          type="datetime"
          placeholder="请选择"
        >
        </el-date-picker> -->
      </el-form-item>
      <div class="search-reset-box">
        <el-button type="primary" icon="el-icon-search" native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
      </div>
    </el-form>
    <!-- 表格 -->
    <el-table ref="table" v-loading="tableLoading" border size="small" style="width: 100%; margin-top: 15px"
      header-cell-class-name="table-cell-header" :data="tableData" @selection-change="handleSelectionChange">
      <el-table-column prop="createTime" label="申请时间" align="center" />
      <el-table-column prop="applyName" label="学员姓名" align="center" />
      <el-table-column prop="yzCode" label="远智编码" align="center" />
      <el-table-column prop="firstYearOriginalTuition" label="原第一年学费" align="center" />
      <el-table-column prop="firstYearOriginalBookMoney" label="原第一年书费" align="center" />
      <el-table-column prop="firstYearApplyTuition" label="申请第一年学费" align="center">
      </el-table-column>
      <el-table-column prop="firstYearApplyBookMoney" label="申请第一年书费" align="center" />
      <el-table-column prop="status" label="第一年是否缴费" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.status == 1">已缴费</div>
          <div v-else>待缴费</div>
        </template>
      </el-table-column>
      <el-table-column prop="payTime" label="缴费时间" align="center">
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.limit"
        @pagination="getTableList" />
    </div>
  </div>
</template>

<script>
import moment from 'moment';

export default {
  data() {
    return {
      form: {
        applyName: '',
        idCard: '',
        mobile: '',
        yzCode: '',
        learnId: '',
        pfsnLevel: '',
        grade: '',
        unvsId: '',
        pfsnName: '',
        status: '',
        // createTime: [],
        startTime: '',
        endTime: '',
        payStartTime: '',
        payEndTime: ''
      },
      payTime: [],
      createTime: [],
      tableData: [],
      tableLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      grade: [],
      subjectQuery: '',
      subject: [],
      schoolLevel: [],
      unvsList: []
    };
  },
  mounted() {
    this.createTime = this.calculateLastOneMonth();
    this.form.startTime = this.createTime[0] ? moment(this.createTime[0]).format('YYYY-MM-DD HH:mm:ss') : '';
    this.form.endTime = this.createTime[1] ? moment(this.createTime[1]).format('YYYY-MM-DD HH:mm:ss') : '';
    this.grade = this.$dictJson.grade;
    this.schoolLevel = this.$dictJson.pfsnLevel;
    this.getTableList();
    this.getUnvsList();

    // this.getSubject();
  },

  methods: {
    getUnvsList() {
      this.$post('getUnvsList', { page: 1, rows: 999 }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.unvsList = body.data;
        }
      });
    },

    calculateLastOneMonth() {
      const endDate = new Date();
      const startDate = new Date(
        endDate.getFullYear(),
        endDate.getMonth(),
        endDate.getDate() - 30
      );
      return [startDate, endDate];
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
        this.createTime = []
        this.payTime = []
        this.form.startTime = '',
        this.form.endTime = '',
        this.form.payStartTime = '',
        this.form.payEndTime = ''
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    payTimeChange(e) {
      if (e) {
        this.form.payStartTime = moment(this.payTime[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        );
        this.form.payEndTime = moment(this.payTime[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        );
      }
    },
    createTimeChange(e) {
      if (e) {
        this.form.startTime = moment(this.createTime[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        );
        this.form.endTime = moment(this.createTime[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        );
      }
    },
    querySubject() { },
    clearSubject() { },
    handleSelectionChange() { },
    getTableList() {
      this.form.payTime = this.form.payTime
        ? moment(this.form.payTime).format('YYYY-MM-DD HH:mm:ss')
        : '';
      this.tableLoading = true;
      let data = {
        ...this.form,
        start: this.pagination.page,
        length: this.pagination.limit
      };
      this.$http
        .post('/semesterPayApply/list', data)
        .then((res) => {
          if (res.ok) {
            this.tableData = res.body.data;
            this.pagination.total = res.body.recordsTotal;
            this.tableLoading = false;
          }
        });
    }
  }
};
</script>

<style lang="scss" scoped></style>