<template>
  <section>
    <img v-if="isbms!=1" style="width: 100%;height:170px;" src="@/assets/imgs/annex/paper-classdata-top-bg.png" alt="" />
    <div v-if="showDom" class="sign">
      <div ref="testCard" class="cardBox">
        <div class="testCard">
          <p style="font-size: .18rem;">国家开放大学期末考试</p>
          <p style="margin: .3rem;">考试通知单</p>
          <div class="info">
            <div class="img">
              <img v-if="headUrl && !defaultFlag" :src="headUrl" alt="" />
              <img v-else ref="shareImgdefault" src="@/assets/imgs/default.jpg" alt="" />
            </div>
            <div class="info_detail">
              <span>姓名：{{ info.stdName }}</span>
              <span>学号：{{ info.schoolRoll }}</span>
              <span>性别：{{ info.sex }}</span>
              <span>证件号：{{ info.idCard }}</span>
              <p>考场：{{ info.examPlace }}</p>
              <p>考场地址：{{ info.examAddress }}</p>
            </div>
          </div>
          <div class="testInfo">
            <el-table
              :data="info.courseInfoList"
              :border="true"
              style="width: 100%; color:'#666'"
              :header-row-style="{ height: '0' }"
              :header-cell-style="{ background:'#f8f8f8', color:'#666', fontSize: '12px', fontWeight: 400, padding: '3px 0' }"
              :row-style="{ height: '0' }"
              :cell-style="{ padding: '3px 0', color:'#666', fontSize: '12px' }"
            >
              <el-table-column prop="courseName" label="考试科目" width="120" align="center" label-class-name="table_label" />
              <el-table-column prop="examDate" label="考试日期" width="100" align="center">
                <template slot-scope="scope">
                  {{ scope.row.examDate.substr(0,10) }}
                </template>
              </el-table-column>
              <el-table-column prop="examStartTime" label="考试时间" width="100" align="center">
                <template slot-scope="scope">
                  {{ scope.row.examStartTime + '-' + scope.row.examEndTime }}
                </template>
              </el-table-column>
              <el-table-column prop="examWay" label="考试方式" width="100" align="center" />
              <el-table-column prop="className" label="课室及座位" align="center">
                <template slot-scope="scope">
                  {{ scope.row.className + '座位' + scope.row.seatNum }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="std_detail">
            <div class="h3">考生须知</div>
            <div v-html="info.examNotices"></div>
          </div>
          <!-- <div v-else class="std_detail">
            <h3 style="padding-bottom: 10px">考生须知</h3>
            <p>1、考生必带：①身份证原件；②准考证原件。</p>
            <p>2、考试严禁携带手机，电子记事本等具有存储记忆录放功能的电子设备进入考场。</p>
            <p>3、考生应在每科开考前20分钟进入考场，对号入座，并将上述证件放在桌面靠走道上角，以备核验。</p>
            <p>4、考试时间和考试地址是统一固定，无法更改，请提前熟悉路线做好安排。另考场附近车位有限，请您尽量乘坐公共交通工具前往，谢谢配合。</p>
          </div>            -->
        </div>
      </div>
    </div>
    <div v-if="!showDom" class="yz-base-container">
      <div class="con-grid">
        <div v-if="isbms!=1" class="title">请打印您的考试通知单{{ info.gkAdmissionTicketUrl ? '和准考证' : '' }}，按时参加考试，预祝您考试顺利！</div>
        <div class="change-grid">
          <el-carousel
            :autoplay="false"
            :indicator-position="info.gkAdmissionTicketUrl ? 'outside' : 'none'"
            :arrow="info.gkAdmissionTicketUrl ? 'always' : 'never'"
            :height="imgHeight"
            @change="change"
          >
            <el-carousel-item v-for="item in srcList" :key="item">
              <el-image
                ref="imgHeight"
                :style="{width: '100%', height: currentIndex ? imgHeight : 'auto'}"
                :src="item"
                fit="contain"
                :preview-src-list="srcList"
              />
            </el-carousel-item>
          </el-carousel>
          <!-- <div style="height: 100%;">
            <div class="grid-down">
              <div v-if="!mFolderList.length && !mFileList.length" class="no-data-b">
                <img src="@/assets/imgs/annex/paper-no-data.png" alt="" />
                <p>暂无学习资料</p>
              </div>
            </div>
          </div> -->
        </div>
        <div class="footer_downLoad" style="text-align: center;">
          <div class="downLoad-box">
            <el-button type="danger" :style="{'margin-left': info.gkAdmissionTicketUrl ? '295px': 0, width: '230px'}" @click="downLoadClick">一键下载</el-button>
            <div v-if="info.gkAdmissionTicketUrl" class="pop">点击“一键下载”，同时下载考试通知单和准考证</div>
          </div>
          <p v-if="isbms!=1">如果无法下载，请使用谷歌浏览器下载；如果无考试通知单{{ info.gkAdmissionTicketUrl ? '和准考证' : '' }}信息，请联系助学老师</p>
        </div>
      </div>
    </div>
    <!-- <div v-else>
      <el-empty description="请前往远智学堂登录" />
    </div> -->
  </section>
</template>

<script>
import { ossUri } from '@/config/request';
import JSZip from 'jszip';
import FileSaver from 'file-saver';
import axios from 'axios';
import moment from 'moment';
import html2canvas from 'html2canvas';
export default {
  filters: {
    imgFilters(val) {
      return ossUri + val;
    }
  },
  data() {
    return {
      info: {},
      mFolderList: [],
      mFileList: [],
      showDom: true,
      defaultFlag: false,
      srcList: [],
      imgHeight: '500px',
      headUrl: '',
      lock: false,
      isbms: 0,
      currentIndex: 0
    };
  },
  computed: {
    mCrumbSta() {
      if (this.mCrumbArr.length > 1) {
        return true;
      } else {
        return false;
      }
    }
  },
  created() {
    const { isbms } = this.$route.query;
    this.isbms = isbms;
    this.getAdmissionInfo();
  },
  async mounted() {
  },
  methods: {
    imgLoad() {
      this.imgHeight = this.$refs.imgHeight[0].imageHeight / 2.5 + 'px';
    },
    change(e) {
      this.currentIndex = e;
    },
    async getAdmissionInfo() {
      const loading = this.$loading({
        lock: true,
        text: '正在加载中，请稍等...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      const { learnId, examWay, examinationId } = this.$route.query;
      const params = {
        learnId,
        examWay,
        examinationId
      };
      const { code, body } = await this.$http.get('/admission/getAdmissionInfo', { params });
      if (code === '00') {
        this.info = body;
        if (body.headImg) {
          this.main(ossUri + body.headImg, (base64) => {
            this.headUrl = base64;
            setTimeout(() => {
              this.share(loading);
            }, 500);
          });
        } else {
          this.$nextTick(() => {
            this.share(loading);
          });
        }
      }
    },
    share(loading) {
      var that = this;
      var canvas2 = document.createElement('canvas');
      const _canvas = this.$refs.testCard;
      var w = parseInt(window.getComputedStyle(_canvas).width);
      var h = parseInt(window.getComputedStyle(_canvas).height);
      const scale = 3;
      canvas2.width = w * scale;
      canvas2.height = h * scale;
      canvas2.style.width = w + 'px';
      canvas2.style.height = h + 'px';
      // var context = canvas2.getContext('2d');
      // context.scale(scale, scale);
      html2canvas(this.$refs.testCard, {
        scale: scale,
        canvas: canvas2,
        useCORS: true,
        allowTaint: true, // 允许跨域图片
        width: _canvas.offsetWidth,
        height: h
      }).then(function(canvas) {
        that.info.gkAdmissionTicketUrl ? that.srcList = [canvas.toDataURL('image/jpeg'), ossUri + that.info.gkAdmissionTicketUrl] : that.srcList.push(canvas.toDataURL('image/jpeg'));
        that.showDom = false;
        that.lock = false;
        setTimeout(() => {
          that.imgLoad();
        }, 500);
        loading.close();
      }).catch(err => {
        console.error(err);
        loading.close();
      });
    },
    getBase64Image(img) {
      var canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      var ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0, img.width, img.height);
      try {
        var i = 1 / 0;
        var dataURL = canvas.toDataURL('image/jpeg'); // 可选其他值 image/jpeg
      } catch (e) {
        this.showB = true;
        this.defaultFlag = true;
      }
      return dataURL;
    },
    main(src, cb) {
      var image = new Image();
      image.crossOrigin = 'Anonymous'; // 支持跨域图片
      image.src = src + '?temp_v=' + Math.random(); // 处理缓存
      var that = this;
      image.onload = function() {
        var base64 = that.getBase64Image(image);
        cb && cb(base64);
      };
      image.onerror = function() {
        if (!that.lock) {
          that.defaultFlag = true;
          that.$nextTick(() => {
            console.log(that.$refs.shareImgdefault);
            that.$refs.shareImgdefault.onload = function() {
              that.share();
            };
          });
        }
      };
    },
    base64Img(url) {
      const base64 = url.toString(); // imgSrc 就是base64哈
      const byteCharacters = atob(
        base64.replace(/^data:image\/(png|jpeg|jpg);base64,/, '')
      );
      const byteNumbers = new Array(byteCharacters.length);
      for (var i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], {
        type: undefined
      });
      const aLink = document.createElement('a');
      aLink.download = '考试通知单.jpg'; // 这里写保存时的图片名称
      aLink.href = URL.createObjectURL(blob);
      aLink.click();
    },
    imgBase64(url) {
      fetch(url + '?s=' + Math.random().toString()).then(res => res.blob()).then((blob) => {
        const aLink = document.createElement('a');
        aLink.href = URL.createObjectURL(blob);
        aLink.download = '准考证.jpg';
        aLink.click();
      });
      // const aLink = document.createElement('a');
      // aLink.download = '准考证.jpg'; // 这里写保存时的图片名称
      // aLink.href = url + '?response-content-type=application/octet-stream';
      // aLink.click();
    },
    updateDownloadStatus(eigId) {
      const params = {
        eigId
      };
      this.$http.post('/admission/updateDownloadStatus', params, { json: true });
    },
    downLoadClick() {
      // this.srcList.forEach(element => {
      //   if (element.includes('data:image/png')) {
      //     this.base64Img(element);
      //   } else {
      //     this.imgBase64(element);
      //   }
      // });
      this.downloadImages();
      this.updateDownloadStatus(this.info.eigId);
    },

    downloadImages() {
      const zip = new JSZip();
      const imagePromises = this.srcList.map((item, index) => {
        if (item.includes('data:image/jpeg')) {
          return new Promise((resolve) => {
            const fileName = '考试通知单.jpg';
            const content = item.replace(/data:image\/.*;base64,/, '');
            zip.file(fileName, content, { base64: true });
            resolve();
          });
        } else {
          return fetch(item + '?s=' + Math.random().toString())
            .then(response => response.blob())
            .then(blob => {
              const fileName = '准考证.jpg';
              zip.file(fileName, blob);
            });
        }
      });
      Promise.all(imagePromises).then(() => {
        return zip.generateAsync({ type: 'blob' });
      }).then(blob => {
        FileSaver.saveAs(blob, `${this.info.schoolRoll}.zip`);
      }).catch(error => {
        console.error('下载图片时出错：', error);
      });
    }
  }
};
</script>

<style scoped lang="scss">

.sign {
  position: fixed;
  width: 670px;
  z-index: 3000;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  .cardBox {
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    padding: 100px 20px;
  }
  .testCard{
    font-size: 12px;
    padding: 20px 10px;
    border: 1px solid #eee;
    /*min-height: 1000px;*/
    /*height: auto;overflow: hidden;*/
    p{
      text-align: center;
      /*font-size: 30px;*/
      font-size: 12px;
    }
    h3{
      text-align: center;
      /*font-size: 30px;*/
      font-size: 15px;
    }
    .info{
      width: 100%;
      height: auto;
      overflow: hidden;
      font-size: 12px;
      .img{
        float: left;
        /*width: 210px;*/
        /*height: 280px;*/
        width: 85px;
        height: 105px;
        padding: 5px;
        background-color: #f8f8f8;
        img{
          width: 100%;
        }
      }
      .info_detail{
        float: left;
        font-size: 13px;
        width: 400px;
        margin-left: 10px;
        margin-top: 5px;
        span{
          float: left;
          /*width: 400px;*/
          width: 120px;
          /*margin-bottom: 20px;*/
          margin-bottom: 11px;
          &:nth-of-type(2),&:nth-of-type(4){
            /*width: 700px;*/
            /*margin-left: 50px;*/
            width: 200px;
          }
        }
        p{
          font-size: 12px;
          clear: both;
          text-align: left;
          margin-top: 12px;
        }
      }
    }
    .testInfo{
      /*height: auto;overflow: hidden;*/
      clear: both;
      margin: 20px auto;
    }
    .std_detail{
      .h3{
        border-bottom: solid 1px #000;
        text-align: center;
        margin-bottom: 10px;
        padding-bottom: 10px;
      }
      p{
        text-align: left;
      }
    }
  }
}

.yz-base-container{
  background: #ffffff;
  // height: calc(100vh - 220px);
  padding: 20px;
  box-sizing: border-box;
  user-select: none;
  // overflow: hidden;
  // overflow-y: scroll;
  .con-grid{
    width: 1020px;
    height: 100%;
    margin: 0 auto;
    // overflow: hidden;
    .title{
      color: #333333;
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      padding: 20px 0;
    }

    ::v-deep .el-carousel__item{
      padding: 10px 90px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    ::v-deep .el-carousel__indicators--horizontal {
      // position: absolute;
      // bottom: 5px;
      // text-align: right;
      .el-carousel__indicator--horizontal button {
        width: 10px;
        height: 10px;
        border: 1px solid #A19C9C;
        background: #ffffff;
        border-radius: 50%;
        opacity: 0.5;
      }

      .el-carousel__indicator--horizontal.is-active button {
        width: 10px;
        height: 10px;
        background: #F06E6C;
        opacity: 1;
        border-radius: 50%;
        border: 1px solid transparent;
      }
    }
    ::v-deep .el-carousel__arrow {
      background-color: #666;
    }
    .change-grid{
      // height: calc(100% - 54px);
    }
    .footer_downLoad {
      .downLoad-box {
        text-align: center;
        margin-top: 20px;
        padding-bottom: 40px;
        display: flex;
        align-items: center;
        justify-content: center;

        .pop {
          width: 280px;
          background: #333333;
          border-radius: 20px;
          height: 34px;
          line-height: 34px;
          color: #fff;
          font-size: 12px;
          text-align: center;
          margin-left: 15px;
          position: relative;
          &::before{
            content: ' ';
            position: absolute;
            left: -15px;
            bottom: 0;
            width: 0;
            height: 0;
            border-right: 30px solid #333333;
            border-bottom: 0px solid transparent;
            border-top: 25px solid transparent;
          }
        }
      }
      p {
        margin-top: 12px;
        font-size: 14px;
        color: #CC2725;
      }
    }
  }
  .grid-down{
    border:1px solid #F6F6F6;
    border-radius: 4px;
    height: 100%;
  }
}
.no-data-b {
  width: 100%;
  height: 600px;
  text-align: center;
  img{
    width: 200px;
    height: 200px;
    margin-top: 180px;
  }
  p{
    margin-top: 10px;
  }
}
</style>
