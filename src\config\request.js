
const url = '';
export const uri = url;

export const downUri = process.env.VUE_APP_DOWN_URL_API; // 导出域名拼接地址
export const ossUri = window.location.protocol + process.env.VUE_APP_OSS_URL; // oss文件服务器地址

export const bstUrl = process.env.VUE_APP_BST_URL; // bst项目服务域名、

export const netNewspaperURL = process.env.VUE_APP_NET_NEWSPAPER_API;

export const eeaProxyURL = process.env.VUE_APP_EEA_PROXY_URL;

export const downOssUri = window.location.protocol + process.env.VUE_APP_DOWN_OSS_URL; // 课程资料文件地址

export const timeout = 120000;
export const contentType = {
  form: 'application/x-www-form-urlencoded',
  json: 'application/json',
  file: 'multipart/form-data'
};

// bms域名
export const bmsURL = process.env['VUE_APP_BMS_URL'];
