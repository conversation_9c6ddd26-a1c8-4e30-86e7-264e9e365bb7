<template>
  <!-- 同桌奖学金邀约-助学老师后台 -->
  <div class="undetermines">
    <el-form
      class="undetermines-forms"
      :model="querys"
      size="mini"
      label-width="120px"
      @submit.native.prevent="searchBtn(1)"
    >
      <el-form-item label="邀约人姓名：">
        <el-input
          v-model="querys.invitationUserName"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="邀约人远智编号：" label-width="140px">
        <el-input
          v-model="querys.invitationYzCode"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="邀约人手机号：">
        <el-input
          v-model="querys.invitationMobile"
          placeholder="请输入"
          clearable
          inputmode="numeric"
          :maxlength="11"
        />
      </el-form-item>
      <el-form-item label="被邀约人姓名：">
        <el-input
          v-model="querys.customerUserName"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="被邀约人远智编号：" label-width="150px">
        <el-input
          v-model="querys.customerYzCode"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="优惠类型：" label-width="150px">
        <el-select
          v-model="querys.activityId"
          placeholder="请选择"
          filterable
          clearable
          :remote="true"
          :loading="discLoading"
          :remote-method="getDiscountList"
        >
          <el-option
            v-for="item in discounData"
            :key="item.activityId"
            :label="item.activityName"
            :value="item.activityId"
          />
        </el-select>
      </el-form-item>
      <div class="forms-btn">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
          v-text="'搜索'"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="searchBtn(0)" />
      </div>
    </el-form>
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      :data="tableData"
      header-cell-class-name="table-cell-header"
    >
      <el-table-column prop="activityName" label="优惠类型" align="center" />
      <el-table-column
        prop="invitationUserName"
        label="邀约人"
        align="center"
      />
      <el-table-column
        prop="invitationYzCode"
        label="邀约人远智编号"
        align="center"
      />
      <el-table-column
        prop="invitationFollowUserName"
        label="邀约跟进人"
        align="center"
      />
      <el-table-column
        prop="customerUserName"
        label="被邀约人"
        align="center"
      />
      <el-table-column
        prop="customerYzCode"
        label="被邀约人远智编号"
        align="center"
      />
      <el-table-column
        prop="customerFollowUserName"
        label="被邀约跟进人"
        align="center"
      />
      <el-table-column prop="invitationDate" label="邀约时间" align="center" />
      <el-table-column
        prop="invitationStatus"
        label="邀约状态"
        align="center"
      />
    </el-table>
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.start"
        :limit.sync="pagination.length"
        @pagination="getTableList"
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      querys: {},
      pagination: {
        total: 0,
        start: 1,
        length: 10
      },
      tableLoading: false,
      tableData: [],
      discounData: [],
      discLoading: false
    };
  },
  created() {
    this.getTableList();
    this.getDiscountList();
  },
  methods: {
    // 查询  重置
    searchBtn(type) {
      if (type === 1) {
        this.pagination.start = 1;
        this.getTableList();
      } else {
        this.querys = {};
      }
    },
    // 表格数据
    getTableList() {
      this.tableLoading = true;
      const params = { ...this.pagination, ...this.querys };
      for (const key in params) {
        if (!params[key]) delete params[key];
      }
      this.$http
        .post('/invitationActivity/getTeachInvitationList.do', params)
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            this.tableData = body?.data || [];
            this.pagination.total = body?.recordsTotal || 0;
          }
          this.tableLoading = false;
        })
        .catch((err) => {
          this.tableLoading = false;
          console.log('表格数据-err', err);
        });
    },
    // 优惠类型数据
    getDiscountList(activityName = '') {
      this.discLoading = true;
      console.log('优惠类型数据------', activityName);
      this.$http.post('/invitationActivity/getScholarshipList',
        { activityName, start: 0, length: 100 }).then(res => {
        const { code, body } = res;
        console.log('优惠类型数据', res);
        if (code === '00') {
          this.discounData = body?.data || [];
        }
        this.discLoading = false;
      }).catch(err => {
        this.discLoading = false;
        console.log('消息标题数据-err', err);
      });
    }
  }
};
</script>

<style lang="scss">
.undetermines {
  padding: 40px 30px;

  &-forms {
    margin-bottom: 30px;

    .el-form-item {
      width: 32%;
      display: inline-block;
    }
    .forms-btn {
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
