<template>
  <!-- 同桌奖学金邀约后台-管理员后台 -->
  <div class="undetermined">
    <el-form
      class="undetermined-forms"
      :model="querys"
      size="mini"
      label-width="120px"
      @submit.native.prevent="searchBtn(1)"
    >
      <el-form-item label="邀约人姓名：">
        <el-input
          v-model="querys.invitationUserName"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="邀约人远智编号：" label-width="140px">
        <el-input
          v-model="querys.invitationYzCode"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="邀约跟进人：">
        <el-select
          v-model="querys.invitationFollowEmpId"
          placeholder="请选择"
          filterable
          clearable
          :remote="true"
          :loading="empLoading"
          :remote-method="getEmpList"
        >
          <el-option
            v-for="item in empData"
            :key="item.empId"
            :label="item.empName"
            :value="item.empId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="邀约跟进部门：">
        <el-select
          v-model="querys.invitationFolloweDepartmentId"
          placeholder="请选择"
          filterable
          clearable
          :remote="true"
          :loading="dpLoading"
          :remote-method="getDpList"
        >
          <el-option
            v-for="item in dpData"
            :key="item.dpId"
            :label="item.dpName"
            :value="item.dpId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="被邀约人姓名：">
        <el-input
          v-model="querys.customerUserName"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="被邀约人远智编号：" label-width="150px">
        <el-input
          v-model="querys.customerYzCode"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="被邀约跟进人：">
        <el-select
          v-model="querys.customerFollowEmpId"
          placeholder="请选择"
          filterable
          clearable
          :remote="true"
          :loading="empLoading"
          :remote-method="getEmpList"
        >
          <el-option
            v-for="item in empData"
            :key="item.empId"
            :label="item.empName"
            :value="item.empId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="被邀约跟进部门：" label-width="140px">
        <el-select
          v-model="querys.customerFollowDepartmentId"
          placeholder="请选择"
          filterable
          clearable
          :remote="true"
          :loading="dpLoading"
          :remote-method="getDpList"
        >
          <el-option
            v-for="item in dpData"
            :key="item.dpId"
            :label="item.dpName"
            :value="item.dpId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优惠类型：" label-width="150px">
        <el-select
          v-model="querys.activityId"
          placeholder="请选择"
          filterable
          clearable
          :remote="true"
          :loading="discLoading"
          :remote-method="getDiscountList"
        >
          <el-option
            v-for="item in discounData"
            :key="item.activityId"
            :label="item.activityName"
            :value="item.activityId"
          />
        </el-select>
      </el-form-item>
      <div class="forms-btn">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
          v-text="'搜索'"
        />
        <el-button icon="el-icon-refresh" size="mini" @click="searchBtn(0)" />
      </div>
    </el-form>
    <div style="float: right; margin: 15px 25px 15px 0">
      <el-popconfirm
        title="确定导出数据吗？"
        @confirm="exportList(true)"
        @cancel="exportList(false)"
      >
        <el-button slot="reference" type="primary" size="small">
          导出
        </el-button>
      </el-popconfirm>
    </div>
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      :data="tableData"
      header-cell-class-name="table-cell-header"
    >
      <el-table-column prop="activityName" label="优惠类型" align="center" />
      <el-table-column
        prop="invitationUserName"
        label="邀约人"
        align="center"
      />
      <el-table-column
        prop="invitationYzCode"
        label="邀约人远智编号"
        align="center"
      />
      <el-table-column
        prop="invitationFollowUserName"
        label="邀约跟进人"
        align="center"
      />
      <el-table-column
        prop="invitationFollowDepartmentName"
        label="邀约跟进部门"
        align="center"
      />
      <el-table-column
        prop="customerUserName"
        label="被邀约人"
        align="center"
      />
      <el-table-column
        prop="customerYzCode"
        label="被邀约人远智编号"
        align="center"
      />
      <el-table-column
        prop="customerFollowUserName"
        label="被邀约跟进人"
        align="center"
      />
      <el-table-column
        prop="customerFollowDepartmentName"
        label="被邀约跟进部门"
        align="center"
      />
      <el-table-column prop="invitationDate" label="邀约时间" align="center" />
      <el-table-column
        prop="invitationStatus"
        label="邀约状态"
        align="center"
      />
      <el-table-column label="操作" align="center" width="200px">
        <template slot-scope="scope">
          <el-button :size="'mini'" type="primary" @click="openBind(scope.row)">
            换绑
          </el-button>
          <el-button
            :size="'mini'"
            type="success"
            @click="openRecord(scope.row)"
          >
            变更记录
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.start"
        :limit.sync="pagination.length"
        @pagination="getTableList"
      />
    </div>
    <!-- 换绑弹窗 -->
    <el-dialog :visible.sync="bindVisible" append-to-body title="更换绑定">
      <ul class="undetermined-bind">
        <li class="bind-li">
          <div class="bind-div">
            原邀约人远智编码：{{ bindObj.invitationYzCode || '' }}
          </div>
          <div>原邀约人姓名：{{ bindObj.invitationUserName || '' }}</div>
        </li>
        <li class="bind-li" style="padding-top: 12px">
          <img class="bind-img" src="@/assets/imgs/618rights.png" alt="" />
          <div class="bind-text">更换为</div>
        </li>
        <li class="bind-li">
          <div class="bind-div bind-input">
            <p><span>*</span>现邀约人远智编码：</p>
            <el-input
              v-model="newYZCode"
              placeholder="请输入内容"
              @blur="getUserName"
            />
          </div>
          <div>现邀约人姓名：{{ realName || '' }}</div>
        </li>
      </ul>
      <div class="undetermined-bind" style="justify-content: center">
        <span>备注：</span>
        <el-input
          v-model="operRemark"
          type="textarea"
          placeholder="请输入内容"
          :rows="4"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelBind">取 消</el-button>
        <el-button type="primary" @click="submitBind">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 变更记录弹窗 -->
    <el-dialog
      :visible.sync="recordVisible"
      append-to-body
      title="变更历史记录"
    >
      <el-table
        v-loading="recordLoading"
        border
        size="small"
        :data="recordData"
        header-cell-class-name="table-cell-header"
      >
        <el-table-column
          prop="invitationUserNames"
          label="邀约人"
          align="center"
        />
        <el-table-column label="类型" align="center">
          <template>邀约变更人</template>
        </el-table-column>
        <el-table-column prop="operRemark" label="备注" align="center" />
        <el-table-column prop="operUserName" label="操作人" align="center" />
        <el-table-column prop="createTime" label="操作时间" align="center" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="recordVisible = false">取 消</el-button>
        <el-button type="primary" @click="recordVisible = false">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { downUri } from '@/config/request';

export default {
  data() {
    return {
      querys: {
        activityId: '',
        invitationUserName: '',
        invitationYzCode: '',
        invitationFollowEmpId: '',
        invitationFolloweDepartmentId: '',
        customerUserName: '',
        customerYzCode: '',
        customerFollowEmpId: '',
        customerFollowDepartmentId: ''
      },
      newYZCode: '',
      realName: '',
      operRemark: '',
      bindObj: {},
      pagination: {
        total: 0,
        start: 1,
        length: 10
      },
      tableLoading: false,
      tableData: [],
      recordLoading: false,
      recordData: [],
      bindVisible: false,
      recordVisible: false,
      empData: [],
      empLoading: false,
      dpData: [],
      dpLoading: false,
      discounData: [],
      discLoading: false
    };
  },
  created() {
    this.getTableList();
    this.getDiscountList();
  },
  methods: {
    searchBtn(type) {
      if (type === 1) {
        this.pagination.start = 1;
        console.log('searchBtn', { ...this.querys });
        this.getTableList();
      } else {
        // 重置
        this.querys = {
          activityId: '',
          invitationUserName: '',
          invitationYzCode: '',
          invitationFollowEmpId: '',
          invitationFolloweDepartmentId: '',
          customerUserName: '',
          customerYzCode: '',
          customerFollowEmpId: '',
          customerFollowDepartmentId: ''
        };
      }
    },
    exportList(type) {
      if (type) {
        console.log('导出文---querys', this.querys);
        let urs = '';
        for (const key in this.querys) {
          urs += `${key}=${this.querys[key] || ''}&`;
        }
        if (urs) urs = urs.slice(0, -1);
        const exportUrl = `${downUri}/invitationActivity/exportAdminInvitationList.do?${urs}`;
        console.log('导出文---exportUrl', exportUrl);
        // 导出文件
        window.location.href = exportUrl;
      }
    },
    openBind(row) {
      console.log('表格数据-row', row);
      this.bindObj = row;
      this.bindVisible = true;
    },
    cancelBind() {
      this.bindObj = {};
      this.newYZCode = '';
      this.realName = '';
      this.operRemark = '';
      this.bindVisible = false;
    },
    // 提交换绑参数
    submitBind() {
      if (!this.bindObj.newUserId) {
        this.$message({ message: '请正确填写现邀约人远智编码', type: 'error' });
        return;
      }
      this.$http
        .post('/invitationActivity/changeInvitationRelation.do', {
          newInvitationUserId: this.bindObj.newUserId,
          operRemark: this.operRemark,
          relationId: this.bindObj.relationId
        })
        .then((res) => {
          const { code, body } = res;
          console.log('submitBind-body', body);
          if (code === '00') {
            this.cancelBind();
            this.getTableList();
            this.$message({ message: '换绑成功', type: 'success' });
          }
        })
        .catch(() => {
          // this.bindVisible = false;
          this.$message({ message: '换绑失败', type: 'error' });
        });
    },
    openRecord(row) {
      this.recordLoading = true;
      this.$http
        .post('/invitationActivity/getInvitationChangeLog.do', {
          relationId: row.relationId
        })
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            body?.map((item) => {
              const { oldInvitationUserName, newInvitationUserName } = item;
              item.invitationUserNames =
                oldInvitationUserName + ' ===> ' + newInvitationUserName;
              return item;
            });
            this.recordData = body;
          }
          this.recordLoading = false;
          this.recordVisible = true;
        })
        .catch(() => {
          this.recordLoading = false;
        });
    },
    // 表格数据
    getTableList() {
      this.tableLoading = true;
      const params = { ...this.pagination, ...this.querys };
      for (const key in params) {
        if (!params[key]) delete params[key];
      }
      this.$http
        .post('/invitationActivity/getAdminInvitationList.do', params)
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            this.tableData = body?.data || [];
            this.pagination.total = body?.recordsTotal || 0;
          }
          this.tableLoading = false;
        })
        .catch((err) => {
          this.tableLoading = false;
          console.log('表格数据-err', err);
        });
    },
    // 获取用户名称
    getUserName() {
      console.log('获取用户名称', this.newYZCode);
      if (!this.newYZCode.trim()) {
        this.newYZCode = '';
        return;
      }
      this.$http
        .post('/invitationActivity/getUserIdByYzCode.do', {
          yzCode: this.newYZCode
        })
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            console.log('获取用户名称-body', body);
            this.realName = body?.realName || '';
            this.bindObj.newUserId = body?.userId || '';
          }
          this.$forceUpdate();
          console.log('获取用户名称-bindObj', this.bindObj);
        })
        .catch(() => {
          this.newYZCode = '';
        });
    },
    // 查询邀约跟进人/被邀约跟进人
    async getEmpList(sName) {
      console.log('查询邀约跟进人', sName);
      if (!sName) return;
      this.empLoading = true;
      const { code, body } = await this.$http.get(
        `/employ/getEmpList.do?sName=${sName}&page=1&rows=30`
      );
      console.log('查询邀约跟进人-body', body);
      if (code === '00') {
        this.empData = body?.data || [];
      }
      this.empLoading = false;
    },
    // 查询部门
    async getDpList(sName) {
      console.log('查询部门', sName);
      if (!sName) return;
      this.dpLoading = true;
      const { code, body } = await this.$http.get(
        `/dep/getDpList.do?sName=${sName}&page=1&rows=30`
      );
      console.log('查询部门-body', body);
      if (code === '00') {
        this.dpData = body?.data || [];
      }
      this.dpLoading = false;
    },
    // 优惠类型数据
    getDiscountList(activityName = '') {
      this.discLoading = true;
      console.log('优惠类型数据------', activityName);
      this.$http
        .post('/invitationActivity/getScholarshipList', {
          activityName,
          start: 0,
          length: 100
        })
        .then((res) => {
          const { code, body } = res;
          console.log('优惠类型数据', res);
          if (code === '00') {
            this.discounData = body?.data || [];
          }
          this.discLoading = false;
        })
        .catch((err) => {
          this.discLoading = false;
          console.log('消息标题数据-err', err);
        });
    }
  }
};
</script>

<style lang="scss">
.undetermined {
  padding: 40px 30px;

  &-forms {
    margin-bottom: 30px;

    .el-form-item {
      width: 32%;
      display: inline-block;
    }
    .forms-btn {
      display: flex;
      justify-content: flex-end;
    }
  }
}

.undetermined-bind {
  margin: 30px 20px;
  display: flex;
  justify-content: space-around;
  font-size: 15px;
  line-height: 24px;

  .bind-li {
    height: 80px;
    display: block;
    text-align: center;
    .bind-div {
      margin-bottom: 30px;
    }
    .bind-img {
      width: 100px;
      height: 30px;
    }
    .bind-text {
      color: #409eff;
    }
    .bind-input {
      display: flex;
      align-items: center;
      .el-input {
        width: auto;
      }
      .el-input__inner {
        height: 30px;
        line-height: 30px;
      }
      p span {
        color: red;
        margin-right: 4px;
        line-height: 30px;
      }
    }
  }

  .el-textarea {
    width: calc(100% - 124px);
  }
}
</style>
