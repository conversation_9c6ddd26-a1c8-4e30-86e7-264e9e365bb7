<template>
  <!-- 第二步：添加编辑表格 && 第三步：查看表格 -->
  <div v-show="activeInx==2&&addType==3">
    <!-- 按钮区（第二步） -->
    <div class='yz-table-btnbox'>
      <span>战区PK人员名单：</span>
      <el-button type="primary" size="small" @click="exportWarBtn">导出分组PK人员名单</el-button>
      <el-button type="primary" size="small" @click="importWarShow = true">导入战队头像</el-button>
      <el-button type="success" size="small" @click="importData">导入PK人员名单</el-button>
    </div>

    <!-- 表格 -->
    <el-table border header-cell-class-name='table-cell-header' :data="tableData" style="width: 100%">
      <el-table-column label="pk分组" prop="groupId" width="80" align="center" />
      <el-table-column label="战队名称" width="180" align="center">
        <template slot-scope="scope">
          <el-avatar :size="100" :src="scope.row.pkTeamDetailsVOS[0].teamAvatar | formatOssImgUrl">
            <img src="@/assets/imgs/helpStudyPkRank/defaultPK1.png" />
          </el-avatar>
          <div>{{ scope.row.pkTeamDetailsVOS[0].teamName }}</div>
          <p class="avatar-p" @click="openAvatar(scope.row.pkTeamDetailsVOS[0].teamName)">上传头像</p>
        </template>
      </el-table-column>
      <el-table-column label="总指挥" prop="pkTeamDetailsVOS[0].empName" width="65" align="center" />
      <el-table-column label="总人数" prop="pkTeamDetailsVOS[0].totalNumber" width="80" align="center">
        <template slot-scope="scope">
          <div class="paper-nums">{{ scope.row.pkTeamDetailsVOS[0].totalNumber }}</div>
          <div class="avatar-p" @click="memberDetails({zdys:1,groupId:scope.row.groupId,...scope.row.pkTeamDetailsVOS[0]})">添加队员</div>
        </template>
      </el-table-column>
      <el-table-column label="部门" prop="teamName" class-name="text-top">
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(item,index) in scope.row.pkTeamDetailsVOS[0].pkTeamDetailsVOS"
              :key="index"
              class="paper-li"
            >
              {{ item.teamName }}
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column label="分校长" prop="empName" width="65" class-name="text-top">
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(item,index) in scope.row.pkTeamDetailsVOS[0].pkTeamDetailsVOS"
              :key="index"
              class="paper-li"
            >
              {{ item.empName }}
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column label="人力" prop="manPower" width="65" class-name="text-top">
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(item,index) in scope.row.pkTeamDetailsVOS[0].pkTeamDetailsVOS"
              :key="index"
              class="paper-li"
            >
              <el-link type="success" @click="memberDetails({zdys:0,...item})"> {{ item.manPower }} </el-link>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="name" width="80" class-name="text-top">
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(item,index) in scope.row.pkTeamDetailsVOS[0].pkTeamDetailsVOS"
              :key="index"
              class="paper-li"
              @click="delectTeacher(scope.row.pkTeamDetailsVOS[0].pkTeamDetailsVOS, index)"
            >
              <i class="pointer el-icon-delete"></i>
            </li>
          </ul>
        </template>
      </el-table-column>
      <!-----------  分割线 ----------------->
      <el-table-column label="VS" prop="name" width="75" align="center">
        <template>
          <span class="vs">VS</span>
        </template>
      </el-table-column>
      <!-----------  分割线 ----------------->
      <el-table-column label="pk分组" prop="groupId" width="80" align="center" />
      <el-table-column prop="pkTeamDetailsVOS[1].teamName" label="战队名称" width="180" align="center">
        <template slot-scope="scope">
          <el-avatar :size="100" :src="scope.row.pkTeamDetailsVOS[1].teamAvatar | formatOssImgUrl">
            <img src="@/assets/imgs/helpStudyPkRank/defaultPK2.png" />
          </el-avatar>
          <div>{{ scope.row.pkTeamDetailsVOS[1].teamName }}</div>
          <p class="avatar-p" @click="openAvatar(scope.row.pkTeamDetailsVOS[1].teamName)">上传头像</p>
        </template>
      </el-table-column>
      <el-table-column label="总指挥" prop="pkTeamDetailsVOS[1].empName" width="65" align="center" />
      <el-table-column label="总人数" prop="pkTeamDetailsVOS[1].totalNumber" width="80" align="center">
        <template slot-scope="scope">
          <div class="paper-nums">{{ scope.row.pkTeamDetailsVOS[1].totalNumber }}</div>
          <div class="avatar-p" @click="memberDetails({zdys:1,groupId:scope.row.groupId,...scope.row.pkTeamDetailsVOS[1]})">添加队员</div>
        </template>
      </el-table-column>
      <el-table-column label="部门" prop="teamName" class-name="text-top">
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(item,index) in scope.row.pkTeamDetailsVOS[1].pkTeamDetailsVOS"
              :key="index"
              class="paper-li"
            >
              {{ item.teamName }}
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column label="分校长" prop="empName" width="65" class-name="text-top">
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(item,index) in scope.row.pkTeamDetailsVOS[1].pkTeamDetailsVOS"
              :key="index"
              class="paper-li"
            >
              {{ item.empName }}
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column label="人力" prop="manPower" width="65" class-name="text-top">
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(item,index) in scope.row.pkTeamDetailsVOS[1].pkTeamDetailsVOS"
              :key="index"
              class="paper-li"
            >
              <el-link type="success" @click="memberDetails({zdys:0,...item})"> {{ item.manPower }} </el-link>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="name" width="80" class-name="text-top">
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(item,index) in scope.row.pkTeamDetailsVOS[1].pkTeamDetailsVOS"
              :key="index"
              class="paper-li"
              @click="delectTeacher(scope.row.pkTeamDetailsVOS[1].pkTeamDetailsVOS, index)"
            >
              <i class="pointer el-icon-delete"></i>
            </li>
          </ul>
        </template>
      </el-table-column>
    </el-table>

    <!-- 弹窗 - 导入PK人员名单 -->
    <import-achieve-dialog type="3" :title="reduceDialogTitle" :visible.sync="raVisible" :params="addParams" @success="getClanList" />
    <!-- 弹窗 - 战队人员选择 -->
    <select-member :pkChildId="pkChildId" :row="currentRow" :title="selectMemberTitle" :visible.sync="smVisible" @close="getClanList" @refresh-list="getClanList" />
    <!-- 导入战队头像-->
    <common-dialog show-footer title="导入战队头像" width="600px" :visible.sync='importWarShow' @confirm="uploadTeamBadge" @close='importWarShow = false'>
      <div class="dialog-main">
        <el-upload
          ref="upload"
          class="upload-demo"
          drag
          action="/pkTeam/teamAvatarImport"
          name="files"
          multiple
          :file-list="fileList"
          :auto-upload="false"
          :on-change="onChange"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip" style="color:red">头像上传文件名称要与战队名称一致！</div>
          <div slot="tip" class="el-upload__tip">只能上传png文件</div>
        </el-upload>
      </div>
    </common-dialog>
    <!-- 更换当前战队头像-->
    <common-dialog show-footer title="更换当前战队头像" width="600px" :visible.sync='showSingerAvatar' @confirm="uploadSingerBadge" @close='showSingerAvatar = false'>
      <div class="dialog-main">
        <el-upload
          ref="upload"
          action="#"
          name="fileName"
          :limit="1"
          list-type="picture-card"
          :file-list="singerList"
          :auto-upload="false"
          :on-change="onChange2"
          :success="handleAvatarSuccess"
        >
          <i class="el-icon-upload"></i>
          <div slot="tip" class="el-upload__tip" style="color:red">头像上传文件名称要与当前战队名称一致！</div>
          <div slot="tip" class="el-upload__tip">只能上传png文件</div>
        </el-upload>
      </div>
    </common-dialog>
  </div>
</template>

<script>
import importAchieveDialog from '../../import-achieve-dialog';
import selectMember from '../select-member';
import { httpPostDownFile } from '@/utils/downExcelFile';

export default {
  components: { importAchieveDialog, selectMember },
  props: {
    activeInx: { type: Number, default: 0 },
    visible: { type: Boolean, default: false },
    addType: { type: String, default: '1' }, // 1:个人, 2:部门, 3:战队
    addParams: { type: Object, default: () => {} }
  },
  data() {
    return {
      raVisible: false,
      smVisible: false,
      tableLoading: true,
      importWarShow: false,
      showSingerAvatar: false,
      reduceDialogTitle: '',
      selectMemberTitle: '',
      currentRow: null,
      fileList: [],
      singerName: '',
      singerList: [],
      fileData: '',
      tableData: []
    };
  },
  inject: ['newRow'],
  computed: {
    row() {
      return this.newRow();
    },
    pkChildId() {
      const isEdit = this.row?.isEdit;
      return isEdit ? this.row?.pkChildId : this.addParams?.childActivityId;
    }
  },
  methods: {
    // 初始化
    secondInit() {
      this.getClanList();
    },
    handleAvatas(src) {
      const mis = require('@/assets/imgs/helpStudyPkRank/defaultPK2.png');
      this.fileSrcList = [{ name: '', url: '' }];
    },
    // 针对单个上传战队头像
    openAvatar(names) {
      this.singerName = names;
      this.showSingerAvatar = true;
    },
    onChange2(file) {
      if (file.raw.type === 'image/png') {
        this.singerList.push(file);
      } else {
        this.singerList = [];
        this.$message.error('请选择png格式的图片');
      }
    },
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
    },
    uploadSingerBadge() {
      const fileData = new FormData();
      this.singerList.forEach(file => {
        const zio = file?.name?.split('.')[1];
        fileData.append('teamImage', file.raw);
        fileData.append('fileName', `${this.singerName}.${zio}`);
      });
      this.$http.post('/pkTeam/uploadTeamImage', fileData, { uploadFile: true })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.singerList = [];
            this.singerName = '';
            this.showSingerAvatar = false;
            this.getClanList();
            this.$message({ type: 'success', message: '上传成功!' });
          }
        });
    },
    onChange(file, fileList) {
      if (file.raw.type === 'image/png') {
        this.fileList.push(file);
      } else {
        this.fileList = this.fileList.slice(0, 1);
        this.$message.error('请选择png格式的图片');
      }
    },
    // 整体导入战队头像
    uploadTeamBadge() {
      const fileData = new FormData();
      this.fileList.forEach(file => {
        fileData.append('files', file.raw);
      });
      this.$post('uploadTeamAvater', fileData, { uploadFile: true })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.importWarShow = false;
            this.getClanList();
            this.$message({ type: 'success', message: '上传成功!' });
          }
        });
    },
    // 获取战区列表数据
    getClanList() {
      this.tableLoading = true;
      const params = {
        pkChildId: this.pkChildId, // 子活动id
        start: 1,
        length: 100000
      };
      this.$post('getClanList', params)
        .then(res => {
          this.tableData = [];
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body;
          }
          this.tableLoading = false;
        });
    },
    // 导出战队分组人员名单
    exportWarBtn() {
      httpPostDownFile({
        url: '/pkTeam/exportPkUserList',
        params: { pkChildId: this.pkChildId },
        name: '导出战队分组人员名单',
        config: { json: false }
      });
    },
    importData() {
      this.raVisible = true;
      this.reduceDialogTitle = '导入PK人员名单';
    },
    delectTeacher(list, index) {
      if (list.length === 1) {
        this.$message.error('删除失败，至少有一个团队参与pk');
        return;
      }
      this.$confirm('您确定从该战队中移除该团队吗？移除后，该团队助学数将不统计进战队PK中', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          teamId: list[index].teamId,
          teamType: '3',
          pkChildId: this.pkChildId
        };
        this.$post('deleteTeam', params)
          .then(res => {
            const { fail, body } = res;
            if (!fail) {
              this.$message({ type: 'success', message: '删除成功!' });
              this.getClanList();
            }
          });
      });
    },
    // 详情
    memberDetails(item) {
      if (item.zdys == 1) {
        delete item.dpName;
        item.topTeamId = item.teamId || '';
      }
      this.currentRow = item;
      this.currentRow['groupId'] = item?.groupId || '';
      this.selectMemberTitle = '战队人员选择';
      this.smVisible = true;
    },
    dbclick(row, event, column) {
      row.isOK = !row.isOK;
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
      this.$emit('showParent', false);
      this.$emit('refreshParent', true);
    }
  }
};
</script>

<style lang="scss" scoped>
.header {
  text-align: center;
  margin-bottom: 20px;
  h1 {
    margin-bottom: 20px;
  }
  p {
    font-size: 16px;
  }
}
.paper-ul {
  .paper-li {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ebeef5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .left {
      float: left;
      width: 230px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .right {
      float: right;
    }
  }
}

.paper-nums {
  color: #67C23A;
}

.pointer {
  cursor: pointer;
  color: red;
}

.vs {
  font-size: 38px;
  color: red;
  line-height: 38px;
}

::v-deep .text-top {
  padding: 0;
  vertical-align: top;
  text-align: center;

  .cell {
    padding: 0;
  }
}

.yz-table-btnbox {
  span {
    float: left;
  }
}

.el-avatar--circle {
  border-radius: 0;
}

::v-deep .el-avatar {
  background: rgba(60, 71, 231,0);

  img {
    width: 100%;
    height: 100%;
  }
}

.avatar-p {
  cursor: pointer;
  color: rgb(60, 71, 231);
}

.avatar-dis {
  width: 100px;
  height: 100px;
  margin: auto;
  text-align: center;
  overflow: hidden;
}
</style>

<style>
.avatar-dis .el-upload--picture-card{
  height: 100px;
  line-height: 100px;
  background: rgba(60, 71, 231,0);
}
.avatar-dis .el-upload-list__item{
  width: 100px;
  height: 100px;
}
</style>
