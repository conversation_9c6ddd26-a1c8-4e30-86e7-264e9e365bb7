<template>
  <common-dialog
    is-full
    :title="title"
    width="100%"
    confirmText='提交'
    :visible.sync='show'
    @open="init"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='selectForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        :rules="rules"
        @submit.native.prevent='search'
      >
        <el-form-item label='助学老师姓名' prop='empName'>
          <el-input v-model="form.empName" placeholder="请输入" maxlength="50" />
        </el-form-item>
        <el-form-item label='助学老师手机号' prop='mobile'>
          <el-input v-model="form.mobile" placeholder="请输入" maxlength="50" />
        </el-form-item>
        <el-form-item label='身份证号' prop='idCard'>
          <el-input v-model="form.idCard" placeholder="请输入" maxlength="50" />
        </el-form-item>
        <el-form-item label='是否在职' prop='empStatus'>
          <el-select
            v-model="form.empStatus"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label='远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder="请输入" maxlength="50" />
        </el-form-item>

        <el-form-item label='参加活动' prop='isJoin'>
          <el-select
            v-model="form.isJoin"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="参加" value="1" />
            <el-option label="不参加" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label='是否计算人力' prop='manPower'>
          <el-select
            v-model="form.manPower"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>
      </el-form>

      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button size="small" type="primary" @click="dpPersonJoinActivity(1)">参加PK</el-button>
        <el-button size="small" type="warning" @click="dpPersonJoinActivity(2)">不参加PK</el-button>
        <el-button size="small" type="success" @click="dpPersonManPower(3)">计入人力</el-button>
        <el-button size="small" type="info" @click="dpPersonManPower(4)">不计入人力</el-button>
      </div>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="empName" label="助学老师姓名" align="center" />
        <el-table-column prop="dpName" label="所属部门" align="center" />
        <el-table-column prop="jobTitle" label="岗位" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center" />
        <el-table-column prop="idCard" label="身份证号" align="center" />
        <el-table-column prop="empStatus" label="是否在职" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.empStatus==1">在职</div>
            <div v-else>离职</div>
          </template>
        </el-table-column>
        <el-table-column prop="isJoin" label="参加PK" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.isJoin==1">参加</div>
            <div v-else>不参加</div>
          </template>
        </el-table-column>
        <el-table-column prop="manPower" label="是否计入人力" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.manPower==1">是</div>
            <div v-else>否</div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

      <!-- 弹窗 - 业绩调整 -->
      <import-achieve-dialog
        :title="reduceDialogTitle"
        :visible.sync="raVisible"
        @refresh-list="getTableList"
      />
    </div>
  </common-dialog>
</template>

<script>
import importAchieveDialog from '../import-achieve-dialog';
import { exportExcel, handleDateControl } from '@/utils';
export default {
  components: {
    importAchieveDialog
  },
  props: {
    title: {
      type: String,
      default: '新增'
    },
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    },
    pkChildId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      raVisible: false,
      reduceDialogTitle: '',
      empIdList: [],
      checkedJoinList: [],
      form: {
        dpName: '',
        empName: '',
        empId: '',
        mobile: '',
        idCard: '',
        empStatus: '',
        yzCode: '',
        isJoin: '1',
        manPower: '',
        pkChildId: '',
        Range: []
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      tableData: [],
      rules: {
        // rankName: [
        //   { required: true, message: '请输入', trigger: 'blur' }
        // ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 是否参加活动
    dpPersonJoinActivity(jointype) {
      if (this.empIdList.length < 1) {
        this.$message({
          showClose: true,
          message: '至少要选择一个哦！',
          type: 'warning'
        });
        return;
      }
      const formData = {
        type: jointype,
        groupId: this.row.groupId,
        pkChildId: this.pkChildId,
        supTeamId: this.row.teamId,
        empIdList: this.empIdList,
        empName: this.row.dpEmpName,
        topTeamId: this.row.topTeamId,
        teamId: this.row.teamId
      };
      let url = 'dpPersonJoinActivity';
      if (this.row?.zdys == 1) {
        delete formData.supTeamId;
        delete formData.teamId;
        url = 'dpAllPersonJoinActivity';
      }
      this.$post(url, formData, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          // 刷新列表
          this.getTableList();
        }
      });
    },
    // 是否计算人力
    dpPersonManPower(manType) {
      if (this.empIdList.length < 1) {
        this.$message({
          showClose: true,
          message: '至少要选择一个哦！',
          type: 'warning'
        });
        return;
      }
      if (this.checkedJoinList.includes('0')) {
        this.$message({
          showClose: true,
          message: '请勾选参加了PK活动的老师进行操作',
          type: 'warning'
        });
        return;
      }
      const formData = {
        type: manType,
        pkChildId: this.pkChildId,
        supTeamId: this.row.teamId,
        empIdList: this.empIdList,
        topTeamId: this.row.topTeamId,
        teamId: this.row.teamId
      };
      if (this.row?.zdys == 1) {
        // delete formData.supTeamId;
        delete formData.teamId;
      }
      this.$post('dpPersonManPower', formData, { json: true }).then(res => {
        if (!res.fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          // 刷新列表
          this.getTableList();
        }
      });
    },
    getTableList() {
      const formData = JSON.parse(JSON.stringify(this.form));
      if (Number(formData.isJoin)) {
        formData.topTeamId = this.row.topTeamId || '';
      } else {
        formData.topTeamId = '';
      }
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...formData
      };
      this.$post('departPersonList', data).then(res => {
        const { fail, body } = res;
        this.tableData = body.data;
        this.pagination.total = body.recordsTotal;
      });
    },
    importData() {
      this.raVisible = true;
      this.reduceDialogTitle = '导入PK人员名单';
    },
    handleSelectionChange(val) {
      this.empIdList = val.map(item => {
        return item.empId;
      });
      this.checkedJoinList = val.map(item => {
        return item.isJoin;
      });
    },
    dbclick(row, event, column) {
      row.isOK = !row.isOK;
    },
    init() {
      this.form.topTeamId = this.row.topTeamId || '';
      this.form.pkChildId = this.pkChildId;
      if (this.row.zdys != 1) {
        this.form.teamId = this.row.teamId || '';
        this.form.dpName = this.row.dpName;
        if (this.row.teamAvatar) {
          this.form.dpName = this.row.teamName;
        }
      }
      this.getTableList();
    },
    submit() {
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    search(type) {
      if (type === 0) {
        this.$refs['selectForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>

<style lang = "scss" scoped>

</style>

