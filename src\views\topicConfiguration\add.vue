<template>
  <el-dialog :title="type" :visible.sync="addVisible" width="50%" :before-close="addHandleClose" :close-on-click-modal="false">
    <el-form ref="ruleForm" :inline="true" :rules="rules" :model="formInline" class="demo-form-inline" label-width="150px">
      <el-form-item label="远智编号：" prop="yzCode">
        <el-input v-model="formInline.yzCode" placeholder="请输入远智编号" />
      </el-form-item>
      <el-form-item label="教师：" prop="empName">
        <el-input v-model="formInline.empName" placeholder="请输入教师名称" />
      </el-form-item>
      <el-form-item label="绑定答疑专区话题：" prop="topicId">
        <el-select v-model="formInline.topicId" filterable placeholder="请选择话题">
          <el-option v-for="item in talk" :key="item.id" :label="item.topicName" :value="item.id" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="账号：">
        <el-input v-model="formInline.user" disabled placeholder="请输入账号" />
      </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cal">取 消</el-button>
      <el-button type="primary" @click="ok">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    addVisible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    editId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      formInline: {
        empName: '',
        yzCode: '',
        topicId: ''
      },
      talk: [],
      rules: {
        yzCode: [
          { required: true, message: '请输入远智编号', trigger: 'blur' }
        ],
        empName: [
          { required: true, message: '请输入教师名称', trigger: 'blur' },
          { max: 10, message: '输入长度不超过10个字', trigger: 'change' }
        ],
        topicId: [
          { required: true, message: '请选择话题', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    editId(newVal) {
      if (newVal != null) {
        this.editInfo();
      }
    }
  },
  created() {
    this.talkList();
  },
  methods: {
    talkList(e) {
      const params = {
        topicType: 3

      };
      this.$http.post(`/circleDynamic/selTopic`, params).then(res => {
        this.talk = res.body;
      });
    },
    editInfo() {
      this.$http.get(`/qaConfig/detail/${this.editId}`).then(res => {
        console.log(res, '000');
        this.formInline = res.body;
      });
    },
    addHandleClose() {
      console.log(this.$refs.ruleForm, this.$refs);
      this.$refs.ruleForm.resetFields();
      this.$emit('editId', null);
      this.$emit('addVisible', false);
    },
    cal() {
      this.addHandleClose();
    },
    ok() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.type == '新增') {
            const params = {
              topicId: this.formInline.topicId,
              yzCode: this.formInline.yzCode,
              empName: this.formInline.empName
            };
            this.$http.post(`/qaConfig/add`, params, { json: true }).then(res => {
              if (res.ok) {
                this.$parent.list();
                this.$message({ message: '新增成功', type: 'success' });
                this.$refs.ruleForm.resetFields();
                this.$emit('addVisible', false);
                this.$emit('editId', null);
              }
            });
          } else {
            const params = {
              topicId: this.formInline.topicId,
              yzCode: this.formInline.yzCode,
              empName: this.formInline.empName,
              id: this.editId
            };
            this.$http.post('/qaConfig/update', params, { json: true }).then(res => {
              if (res.ok) {
                this.$parent.list();
                this.$message({ message: '编辑成功', type: 'success' });
                this.$refs.ruleForm.resetFields();
                this.$emit('addVisible', false);
                this.$emit('editId', null);
              }
            });
          }
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style lang = "scss" scoped>
.el-input {
    width: 130%;
}
</style>
