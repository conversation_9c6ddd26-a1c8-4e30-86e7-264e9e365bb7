<template>
  <div class="yz-base-container">
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane label="自营好物" name="autarky">
        <autarky />
      </el-tab-pane>
      <el-tab-pane label="虚拟礼品" name="virtaully" lazy>
        <virtaully />
      </el-tab-pane>
      <el-tab-pane label="京东百货" name="jdStore" lazy>
        <jdStore />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import autarky from './autarky';
import virtaully from './virtaully';
import jdStore from './jdStore';
export default {
  components: {
    autarky,
    virtaully,
    jdStore
  },
  data() {
    return {
      activeName: 'autarky'
    };
  }
};
</script>

<style lang="scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
