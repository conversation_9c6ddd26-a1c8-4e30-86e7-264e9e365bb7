<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <open-packup>
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='学业编码' prop='learnId'>
          <el-input v-model="form.learnId" placeholder='请输入学业编码' />
        </el-form-item>

        <el-form-item label='远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder='请输入远智编码' />
        </el-form-item>

        <el-form-item label='学员姓名' prop='stdName'>
          <el-input v-model="form.stdName" placeholder='请输入学员姓名' />
        </el-form-item>

        <el-form-item label='证件号码' prop='idCard'>
          <el-input v-model="form.idCard" placeholder='请输入证件号码' />
        </el-form-item>

        <el-form-item label='学号' prop='schoolRoll'>
          <el-input v-model="form.schoolRoll" placeholder='请输入学号' />
        </el-form-item>

        <el-form-item label='年级' prop='grade'>
          <el-select
            v-model="form.grade"
            filterable
            clearable
            placeholder="请选择"
            @change="handleJointChange"
          >
            <el-option
              v-for="(item,index) in grade"
              :key="index"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='院校' prop='unvsId'>
          <infinite-selects
            v-model="form.unvsId"
            :props="{
              apiName: 'getUnvs',
              value: 'unvs_id',
              label: 'unvs_name',
              query: 'sName'
            }"
            @change="handleJointChange"
          />
        </el-form-item>

        <el-form-item label='专业层次' prop='pfsnLevel'>
          <el-select v-model="form.pfsnLevel" placeholder="请选择" @change="handleJointChange">
            <el-option
              v-for="(item,index) in schoolLevel"
              :key="index"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='专业' prop='pfsnId'>
          <el-select
            v-model="form.pfsnId"
            v-loadmore="loadSubject"
            :remote-method="querySubject"
            filterable
            remote
            clearable
            placeholder="请选择"
            @clear="clearSubject"
          >
            <el-option
              v-for="(item,index) in subject"
              :key="index"
              :label="item.label"
              :value="item.pfsnId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='论文资料' prop='attrSeq'>
          <el-select v-model="form.attrSeq" placeholder="请选择">
            <el-option label="请选择" value="" />
            <el-option label="资料1" value="1" />
            <el-option label="资料2" value="2" />
            <el-option label="资料3" value="3" />
            <el-option label="资料4" value="4" />
            <el-option label="资料5" value="5" />
            <el-option label="资料6" value="6" />
            <el-option label="资料8" value="8" />
            <el-option label="资料9" value="9" />
            <el-option label="资料10" value="10" />
            <el-option label="资料11" value="11" />
            <el-option label="资料12" value="12" />
          </el-select>
        </el-form-item>

        <el-form-item label="资料上传时间" prop="time">
          <el-date-picker
            v-model="form.time"
            :disabled="!form.attrSeq"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <el-form-item label='资料状态' prop='dataStatus'>
          <el-select
            v-model="form.dataStatus"
            :disabled="!form.attrSeq"
            placeholder="请选择"
          >
            <el-option label="未审核" value="0" />
            <el-option label="通过" value="1" />
            <el-option label="驳回" value="2" />
            <!--            <el-option label="未上传" value="3" />-->
          </el-select>
        </el-form-item>

        <el-form-item label='学士学位申请' prop='isApplyBachelors'>
          <el-select v-model="form.isApplyBachelors" placeholder="请选择">
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>

        <el-form-item label='指导老师' prop='guideTeacher'>
          <el-input v-model="form.guideTeacher" placeholder='请输入' />
        </el-form-item>

        <el-form-item label='论文编号' prop='paperNo'>
          <el-input v-model="form.paperNo" placeholder='请输入' />
        </el-form-item>

        <el-form-item label='本科论文题目' prop='paperTitle'>
          <el-input v-model="form.paperTitle" placeholder='请输入' />
        </el-form-item>

        <el-form-item label='任务名称' prop='taskId'>
          <infinite-selects
            v-model="form.taskId"
            :param="{ taskType: '9'}"
            :props="{
              apiName: 'findTaskInfo',
              value: 'task_id',
              label: 'task_title',
              query: 'sName'
            }"
          />
        </el-form-item>

        <el-form-item label='审核状态' prop='checkStatus'>
          <el-select v-model="form.checkStatus" placeholder="请选择">
            <el-option label="未完成" value="0" />
            <el-option label="审核中" value="1" />
            <el-option label="审核通过" value="2" />
            <el-option label="审核不通过" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label='班主任备注' prop='remark'>
          <el-input v-model="form.remark" placeholder='请输入班主任备注' />
        </el-form-item>
        <el-form-item label='助学老师备注' prop='remarkTwo'>
          <el-input v-model="form.remarkTwo" placeholder='请输入助学老师备注' />
        </el-form-item>

        <el-form-item label='上传状态' prop='isUpload'>
          <el-select v-model="form.isUpload" placeholder="请选择">
            <el-option label="未上传" value="0" />
            <el-option label="全部上传" value="1" />
            <el-option label="部分上传" value="2" />
          </el-select>
        </el-form-item>

        <!--        <el-form-item label='审核文档' prop='hasCheck'>-->
        <!--          <el-select v-model="form.hasCheck" placeholder="请选择">-->
        <!--            <el-option label="待审核文档" value="0" />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->

        <el-form-item label='纸质资料状态' prop='paperDataStatus'>
          <el-select v-model="form.paperDataStatus" placeholder="请选择">
            <el-option label="未收到" value="0" />
            <el-option label="收到" value="1" />
            <el-option label="收到不合格" value="2" />
            <el-option label="收到且合格" value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label='是否查看' prop='isView'>
          <el-select v-model="form.isView" placeholder="请选择">
            <el-option label="未查看" value="0" />
            <el-option label="已查看" value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label='指导老师邮箱' prop='guideTeacherEmail'>
          <el-input v-model="form.guideTeacherEmail" placeholder='请输入指导老师邮箱' />
        </el-form-item>

        <el-form-item label='指导老师电话' prop='guideTeacherPhone'>
          <el-input v-model="form.guideTeacherPhone" placeholder='请输入指导老师电话' />
        </el-form-item>

        <el-form-item label='是否有论文题目' prop='isThesis'>
          <el-select v-model="form.isThesis" placeholder="请选择">
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>

        <el-form-item label='答辩状态' prop='replyStatus'>
          <el-select v-model="form.replyStatus" placeholder="请选择">
            <el-option label="未通过" value="1" />
            <el-option label="通过" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label='文件搜索' prop='fileName'>
          <el-input v-model="form.fileName" placeholder='请输入文件姓名' />
        </el-form-item>

        <el-form-item label='分配老师' prop='tutor'>
          <el-input v-model="form.distributionTeacher" placeholder='请输入姓名' />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>
    </open-packup>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button
        type="warning"
        size="small"
        @click="exportWideEcology"
      >广生态（全）导出资料</el-button>
      <el-button
        type="warning"
        size="small"
        @click="exportGuidanceRecordFile"
      >论文指导记录导出</el-button>
      <el-button
        type="primary"
        size="small"
        @click="etVisible = true"
      >毕业论文选题管理</el-button>
      <el-button
        type="success"
        size="small"
        @click="visible2 = true"
      >批量导入答辩结果</el-button>
      <el-button
        type="success"
        size="small"
        @click="visible3 = true"
      >Excel导入</el-button>
      <el-button
        type="warning"
        size="small"
        @click="exportExcel"
      >Excel导出</el-button>
      <el-button
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="pVisible = true"
      >审核评语设置</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="learnId" label="学业编码" align="center" width="160" />
      <el-table-column prop="yzCode" label="远智编码" align="center" width="100" />
      <el-table-column prop="goodSourceName" label="学员基本信息" width="300">
        <template slot-scope="scope">
          <div>
            <p>姓名：{{ scope.row.stdName }}</p>
            <p>年级：{{ scope.row.grade }}级</p>
            <p>
              院校与专业：
              {{ scope.row.unvsName }}
              {{ scope.row.pfsnLevel | pfsnLevel }}
              {{ scope.row.pfsnName }}
            </p>
            <p>学号：{{ scope.row.schoolRoll }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="taskTitle" label="任务名称" align="center" />
      <el-table-column prop="actPrice" label="学员是否查看" width="100" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isView === '1'" class="tag" type="success">是</el-tag>
          <el-tag v-else class="tag" type="danger">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="marketPrice" label="论文基本信息" width="200">
        <template slot-scope="scope">
          <div>
            <p>论文编号：{{ scope.row.paperNo }}</p>
            <p>本论文题目：{{ scope.row.paperTitle }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="actPrice" label="是否申请学士学位证" width="150" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isApplyBachelors === '1'" class="tag" type="success">是</el-tag>
          <el-tag v-if="scope.row.isApplyBachelors === '0'" class="tag" type="danger">否</el-tag>
          <span v-if="scope.row.isApplyBachelors === null">-</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="marketPrice"
        label="学生论文资料下载"
        width="300"
        class-name="text-top"
      >
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(file,index) in scope.row.attrInfoList"
              :key="index"
              class="paper-li"
            >
              <div v-width="180" class="left" :title="file.attachmentName">
                <span class="gray">资料{{ file.attrSeq }}：
                </span><span>{{ file.attachmentName ? file.attachmentName : '未上传' }}</span>
              </div>
              <div v-if="file.attachmentName" class="right">
                <el-tooltip effect="dark" content="预览" placement="top">
                  <i class="yz-icon-preview" @click="preview(file)"></i>
                </el-tooltip>
                <el-tooltip effect="dark" content="下载" placement="top">
                  <a target="_Blank" :href="encodeURIComponent(file.attachmentUrl) | splitOssUrl">
                    <i class="yz-icon-down"></i>
                  </a>
                </el-tooltip>
                <el-tooltip effect="dark" content="查看历史数据" placement="top">
                  <i class="yz-icon-look" @click="lookHistory(scope.row,index)"></i>
                </el-tooltip>
              </div>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column
        prop="marketPrice"
        label="学生论文资料状态"
        width="162"
        class-name="text-top"
        align="center"
      >
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(file,index) in scope.row.attrInfoList"
              :key="index"
              class="paper-li"
            >
              <span v-if="!file.attachmentName" class="no-file">未上传</span>
              <span v-else :class="'file-status-' + file.checkStatus">{{ file.checkStatus | fileStatus }}</span>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column
        prop="rejectNum"
        label="资料驳回次数"
        width="100"
        class-name="text-top"
        align="center"
      >
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(file,index) in scope.row.attrInfoList"
              :key="index"
              class="paper-li"
            >
              <span>{{ file.rejectNum }}</span>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column
        prop="marketPrice"
        label="论文操作"
        width="160"
        class-name="text-top"
        align="center"
      >
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li
              v-for="(file,index) in scope.row.attrInfoList"
              :key="index"
              class="paper-li"
            >
              <template v-if="!file.attachmentName">
                <span>-</span>
              </template>
              <template v-else>
                <el-button
                  v-if="file.checkStatus === '0' && file.paperUploadType !== '9'"
                  size="mini"
                  type="primary"
                  plain
                  @click="openPopup('drVisible',scope.row, file, '1')"
                >通过</el-button>
                <el-button
                  v-if="file.checkStatus === '0' && file.paperUploadType !== '9'"
                  size="mini"
                  type="primary"
                  plain
                  @click="openPopup('drVisible',scope.row, file,'2')"
                >驳回</el-button>
                <el-button
                  v-if="file.checkStatus === '0' && file.paperUploadType === '9'"
                  size="mini"
                  type="primary"
                  plain
                  @click="toVerify(scope.row,file)"
                >去批阅</el-button>
                <el-button
                  v-if="file.checkStatus === '1' || file.checkStatus === '2'"
                  type="text"
                  @click="revoke(scope.row, file)"
                >撤销</el-button>
              </template>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column prop="distributionTeacherName" label="分配老师" width="160" align="center" />
      <el-table-column
        prop="marketPrice"
        label="教师上传论文附件"
        class-name="text-top"
        width="300"
      >
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li v-for="(file,index) in scope.row.attrInfoList" :key="index" class="paper-li">
              <template v-if="file.attachmentId">
                <div class="left">
                  <span class="gray">附件{{ file.attrSeq }}：</span>
                  <span
                    v-if="file.teacherAttachmentUrtl && file.teacherAttachmentUrtl.isOnline === '0'"
                    :title="file.teacherAttachmentUrtl.attachmentName"
                  >{{ file.teacherAttachmentUrtl.attachmentName }}</span>
                </div>
                <div class="right">
                  <upload-file
                    class="inline-block"
                    list-type="text"
                    :action="uploadFileUrl + '?learnId=' + scope.row.learnId + '&attachmentId=' + file.attachmentId"
                    :show-file-list="false"
                    :max-limit="1"
                    :fileList="[]"
                    name="teacherAttachmentUrtl"
                    @success="uploadSuccess"
                  >
                    <el-tooltip effect="dark" content="上传" placement="top">
                      <i class="yz-icon-upload"></i>
                    </el-tooltip>
                  </upload-file>
                </div>
              </template>
              <template v-else>
                <div style="text-align: center">
                  <span>-</span>
                </div>
              </template>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column
        prop="marketPrice"
        label="教师最新修改版论文"
        class-name="text-top"
        width="300"
      >
        <template slot-scope="scope">
          <ul class="paper-ul">
            <li v-for="(file,index) in scope.row.attrInfoList" :key="index" class="paper-li">
              <template v-if="file.attrSeq === '9' && file.teacherOnlineAttachmentUrtl">
                <div class="left">
                  <span class="gray">资料{{ file.attrSeq }}：</span>
                  <span
                    :title="splicingFileName(file)"
                  >{{ splicingFileName(file) }}</span>
                </div>
                <div class="right">
                  <el-tooltip effect="dark" content="下载" placement="top">
                    <a
                      :href="encodeURIComponent(file.teacherOnlineAttachmentUrtl.attachmentUrl) | splitOssUrl"
                    >
                      <i class="yz-icon-down"></i>
                    </a>
                  </el-tooltip>
                </div>
              </template>
              <template v-else>
                <div style="text-align: center">
                  <span>-</span>
                </div>
              </template>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column
        label="论文整体状态"
        width="160"
      >
        <template slot-scope="scope">
          <div>
            <p>上传状态：{{ scope.row.isUpload | isUpload }}</p>
            <p>审核状态：{{ scope.row.checkStatus | checkStatus }}</p>
            <p>纸质编辑状态：{{ scope.row.paperDataStatus | paperDataStatus }}</p>
            <!-- <p>答辩结果：{{ scope.row.replyStatus === '1' ? '未通过' : '通过' }}</p>-->
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="班主任备注" width="200" align="center">
        <template slot-scope="scope">
          <p>{{ scope.row.remark }}</p>
          <el-button
            v-width="104"
            size="mini"
            type="primary"
            plain
            class="margin-5"
            @click="editMasterRemarks(scope.row)"
          >编辑备注</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="remarkTwo" label="助学老师备注" width="200" align="center">
        <template slot-scope="scope">
          <p>{{ scope.row.remarkTwo }}</p>
          <el-button
            v-width="104"
            size="mini"
            type="primary"
            plain
            class="margin-5"
            @click="editTeacherRemarks(scope.row)"
          >编辑备注</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="marketPrice" label="论文相关操作" align="center" width="160">
        <template slot-scope="scope">
          <el-button
            v-width="104"
            size="mini"
            type="primary"
            plain
            class="margin-5"
            @click="editStudentInfo(scope.row)"
          >编辑教务资料</el-button>
          <el-button
            v-width="104"
            size="mini"
            type="primary"
            plain
            class="margin-5"
            @click="openPopup('psVisible',scope.row)"
          >编辑纸质资料</el-button>
          <el-button
            v-width="104"
            size="mini"
            type="primary"
            plain
            class="margin-5"
            @click="openPopup('rVisible',scope.row)"
          >编辑答辩结果</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 查看点评历史 -->
    <paper-history :visible.sync="phVisible" :row="currentRow" />
    <!-- 编辑学生学务信息 -->
    <edit-student-file :visible.sync="edVisible" :form-data="currentRow" />
    <!-- 备注弹窗 -->
    <remarks :visible.sync="remarksVisible" :row="currentRow" :type="remarkType" />
    <!-- 纸质资料状态 -->
    <paper-status :visible.sync="psVisible" :row="currentRow" />
    <!-- 答辩结果 -->
    <reply :visible.sync="rVisible" :row="currentRow" />
    <!-- 毕业论文选题管理  -->
    <essay-topic :visible.sync="etVisible" />
    <!-- 审核评语库  -->
    <phrase :visible.sync="pVisible" />
    <!-- 论文指导记录导出（条件不足或无记录）弹窗 -->
    <guidance-record :visible.sync="grVisible" :tipsTitle="tipsTitle" :tipsContent="tipsContent" />
    <!-- 需点击搜索按钮的提示 -->
    <search-tips :visible.sync="stVisible" :tipsTitle="tipsTitle" :tipsContent="tipsContent" />
    <!-- 资料审核  -->
    <data-review
      :visible.sync="drVisible"
      :row="currentRow"
      :annex="annex"
      :type="auditFileType"
    />
    <!--    导入答辩结果 -->
    <excel-import :visible.sync="visible2" type="2" />
    <!--    excel导入 -->
    <excel-import :visible.sync="visible3" type="3" />

  </div>
</template>

<script>
import paperHistory from './indexDialog/paper-history';
import editStudentFile from './indexDialog/edit-student-file';
import remarks from './indexDialog/remarks';
import paperStatus from './indexDialog/paper-status';
import reply from './indexDialog/reply';
import essayTopic from '././indexDialog/essay-topic';
import phrase from './indexDialog/phrase';
import dataReview from './indexDialog/data-review';
import excelImport from './indexDialog/import-essay-topic';
import guidanceRecord from './indexDialog/guidance-record';
import searchTips from './indexDialog/search-tips';
import { getTextFromDict, exportExcel, handleDateControl } from '@/utils';
import downFile from '@/utils/downFile';
import { ossUri, downUri } from '@/config/request';

const form = {
  learnId: '', // 学业编码
  yzCode: '', // 远智编码
  stdName: '', // 学员姓名
  idCard: '', // 证件号码
  schoolRoll: '', // 学号
  grade: '', // 年级
  unvsId: '', // 院校
  pfsnLevel: '', // 专业层次
  pfsnId: '', // 专业
  attrSeq: '', // 论文资料
  dataUploadTimeStart: '', // 资料上传时间起
  dataUploadTimeEnd: '', // 资料上传时间止
  time: '', // 资料上传时间
  dataStatus: '', // 资料状态
  isApplyBachelors: '', // 学士学位申请
  guideTeacher: '', // 指导老师
  paperNo: '', // 论文编号
  paperTitle: '', // 本科论文题目
  taskId: '', // 任务名称
  checkStatus: '', // 审核状态
  // isRemark: '', // 是否有备注
  remark: '', // 班主任备注
  remarkTwo: '', // 助学老师备注
  isUpload: '', // 上传状态
  hasCheck: '', // 审核文档
  paperDataStatus: '', // 纸质资料状态
  isView: '', // 是否查看
  guideTeacherEmail: '', // 指导老师邮箱
  guideTeacherPhone: '', // 指导老师电话
  isThesis: '', // 是否有论文题目
  replyStatus: '', // 答辩状态
  fileName: '', // 文件搜索
  tipsTitle: '', // 提示标题
  tipsContent: '', // 提示内容
  isSearch: '', // 论文指导记录导出的筛选条件改变时是否进行了点击了搜索
  distributionTeacher: '' // 分配老师
};
export default {
  components: {
    paperHistory,
    editStudentFile,
    remarks,
    paperStatus,
    reply,
    essayTopic,
    phrase,
    dataReview,
    excelImport,
    guidanceRecord,
    searchTips
  },
  provide() {
    return {
      parentVm: this
    };
  },
  filters: {
    pfsnLevel(val) {
      const name = getTextFromDict('pfsnLevel', val);
      let text = '';
      if (name.indexOf('高中') !== -1) {
        text += '[专科]';
      } else if (name.indexOf('本科') !== -1) {
        text += '[本科]';
      } else {
        text += '[' + name + ']';
      }
      return text;
    },
    fileStatus(val) {
      if (!val) return;
      const data = {
        '0': '待审核',
        '1': '已通过',
        '2': '已驳回'
      };
      return data[val];
    },
    isUpload(val) {
      if (val === '1') {
        return '全部上传';
      } else if (val === '2') {
        return '部分上传';
      } else {
        return '未上传';
      }
    },
    checkStatus(val) {
      if (!val) return;
      if (val === '1') {
        return '审核中';
      }
      if (val === '2') {
        return '审核通过';
      }
      if (val === '3') {
        return '审核不通过';
      }
      return '未上传';
    },
    paperDataStatus(val) {
      if (!val) return;
      const data = {
        '0': '未收到',
        '1': '收到',
        '2': '收到不合格',
        '3': '收到且合格'
      };
      return data[val];
    }
  },
  data() {
    return {
      uploadFileUrl: '/graduatePaper/webuploaderNew.do',
      pVisible: false,
      grVisible: false,
      stVisible: false,
      phVisible: false,
      edVisible: false,
      remarksVisible: false,
      psVisible: false,
      rVisible: false,
      etVisible: false,
      drVisible: false,
      visible2: false,
      visible3: false,
      form: form,
      tableData: [],
      remarkType: '', // 备注类型
      grade: [], // 年级
      subject: [], // 专业
      subjectQuery: '',
      office: 'https://view.officeapps.live.com/op/view.aspx?src=',
      subjectPage: {
        page: 1,
        total: 0,
        limit: 10
      },
      schoolLevel: [], // 院校类型
      tableLoading: false,
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      currentRow: {}, // 当前操作行的信息
      annex: {}, // 附件id
      auditFileType: '',
      tipsTitle: '',
      tipsContent: ''
    };
  },
  watch: {
    // 监听论文指导记录导出的搜索条件是否有变化
    'form.grade'(val) { this.isSearch = false; },
    'form.unvsId'(val) { this.isSearch = false; },
    'form.pfsnId'(val) { this.isSearch = false; },
    'form.pfsnLevel'(val) { this.isSearch = false; },
    'form.attrSeq'(val) { this.isSearch = false; },
    'form.dataStatus'(val) { this.isSearch = false; }
  },
  mounted() {
    this.grade = this.$dictJson.grade;
    this.schoolLevel = this.$dictJson.pfsnLevel;
    this.getTableList();
    this.getSubject();
  },
  methods: {
    handleJointChange() {
      this.clearSubject();
    },
    download(file) {
      const fileName = this.splicingFileName(file);
      const fileUrl = ossUri + file.teacherOnlineAttachmentUrtl.attachmentUrl;
      downFile(fileUrl, fileName);
    },
    splicingFileName(file) {
      return file.teacherOnlineAttachmentUrtl.attachmentName +
      '-' + file.teacherOnlineAttachmentUrtl.updateUser + '老师修改';
    },
    // 撤销审批
    revoke(row, file) {
      this.$confirm('您是否撤销此次审批操作？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          attachmentName: file.attachmentName,
          attachmentId: file.attachmentId,
          learnId: row.learnId,
          checkStatus: '0',
          commentContent: ''
        };
        this.$post('updateAttachment', params)
          .then(res => {
            const { fail, body } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.getTableList();
            }
          });
      });
    },
    // 去批阅
    toVerify(row, file) {
      let attachmentId = file.attachmentId;
      if (file.teacherAttachmentUrtl) {
        if (file.teacherAttachmentUrtl.isOnline === '1') {
          attachmentId = file.teacherAttachmentUrtl.teacherAttachmentId;
        }
      }

      const paramsUrl = '?learnId=' + row.learnId + '&attachmentId=' + attachmentId + '&studentId=' + file.attachmentId + '&unvsCode=' + row.unvsCode;
      // + '&url=' + file.attachmentUrl
      window.open('/school-paper/index.html#/verify' + paramsUrl);
      // window.top.location.href = '/school-paper/index.html#/verify' + paramsUrl;
    },
    clearSubject() {
      this.subjectQuery = '';
      this.subject = [];
      this.subjectPage.page = 1;
      this.getSubject();
    },
    querySubject(query) {
      this.subjectQuery = query;
      this.subjectPage.page = 1;
      this.subject = [];
      setTimeout(() => {
        this.getSubject();
      }, 100);
    },
    getSubject() {
      const params = {
        sName: this.subjectQuery,
        sId: this.form.unvsId,
        page: this.subjectPage.page,
        rows: this.subjectPage.limit,
        ext1: this.form.pfsnLevel, // 专业层次
        ext2: this.form.grade // 年级
      };
      this.$http.get('/baseinfo/sPfsn.do', { params: params })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            body.data.forEach(item => {
              const levelName = getTextFromDict('pfsnLevel', item.pfsnLevel);
              item.label = `(${item.pfsnCode})${item.pfsnName}[${levelName}]`;
            });
            this.subject = this.subject.concat(body.data);
          }
        });
    },
    loadSubject() {
      if (this.subjectPage.total === this.subject.length) {
        return;
      }
      this.subjectPage.page += 1;
      this.getSubject();
    },
    exportExcel() {
      exportExcel('exportPaperInfo', this.form);
    },
    openPopup(attr, row, annex, type) {
      if (annex) {
        this.annex = annex;
      }
      if (type) {
        this.auditFileType = type;
      }
      this.currentRow = row;

      this[attr] = true;
    },
    // 编辑班主任备注
    editMasterRemarks(row) {
      this.currentRow = row;
      this.remarkType = 'master';
      this.remarksVisible = true;
    },
    // 编辑助学老师备注
    editTeacherRemarks(row) {
      this.currentRow = row;
      this.remarkType = 'teacher';
      this.remarksVisible = true;
    },
    // 查看历史点评
    lookHistory(row, index) {
      row.currentFileIndex = index;
      this.currentRow = row;
      this.phVisible = true;
    },
    preview(file) {
      var previewUrl = '';
      var canPreview = this.judgeType(file.attachmentUrl);
      if (canPreview) {
        previewUrl = ossUri + file.attachmentUrl;
        window.open(previewUrl, '_blank');
      } else {
        previewUrl = this.office + ossUri + file.attachmentUrl + '?v=' + new Date().getTime();
        window.open(previewUrl, '_blank');
      }
    },
    // 判断预览类型
    judgeType(attachmentUrl) {
      // .docx .doc .xlsx 使用在线预览
      // .png .jpg .pdf 使用直接预览
      var canPreview = false;
      var fileType = ['png', 'jpg', 'pdf'];
      for (var i = 0; i < fileType.length; i++) {
        canPreview = attachmentUrl.endsWith(fileType[i]);
        if (canPreview) return canPreview;
      }
      return canPreview;
    },
    // 编辑档案资料
    editStudentInfo(row) {
      this.currentRow = row;
      this.edVisible = true;
    },
    uploadSuccess({ response, file, fileList }) {
      this.$message({
        message: '附件上传成功',
        type: 'success'
      });
      this.getTableList();
    },
    handleQUeryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      formData.dataUploadTimeStart = date[0];
      formData.dataUploadTimeEnd = date[1];
      delete formData.time;
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQUeryParams();
      this.$post('getAllPaperList', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableLoading = false;
            body.data.forEach(item => {
              if (typeof item.remark === 'string') {
                if (item.remark.trim() === '') {
                  item.remark = '-';
                }
              } else {
                item.remark = '-';
              }

              if (Array.isArray(item.attrInfoList)) {
                item.attrInfoList = item.attrInfoList.filter(obj => {
                  return obj.attrSeq !== '13';
                });
              }
            });
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
            window.scrollTo(0, 0);
          }
        });
    },
    search(type) {
      this.isSearch = true;
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    exportGuidanceRecordFile() {
      if (this.form.grade && this.form.unvsId && this.form.pfsnId && this.form.pfsnLevel && this.form.attrSeq && this.form.dataStatus) {
        if (!this.tableData.length && this.isSearch) {
          this.tipsTitle = '无数据可导出';
          this.tipsContent = '请检查一下搜索条件年级、院校、专业 专业层次、论文资料、资料状态';
          this.grVisible = true;
        } else {
          if (this.isSearch) {
            var result = this.subject.find(ele => ele.pfsnId === this.form.pfsnId);
            var exportUrl = downUri + '/graduatePaper/exportStudentThesisGuidanceRecord.do?grade=' + this.form.grade + '&schoolRoll=' + this.form.schoolRoll + '&unvsId=' + this.form.unvsId + '&pfsnLevel=' + this.form.pfsnLevel + '&pfsnId=' + this.form.pfsnId + '&attrSeq=' + this.form.attrSeq + '&pfsnName=' + result.pfsnName + '&dataStatus=' + this.form.dataStatus;
            // 导出文件
            window.location.href = exportUrl;
          } else {
            this.tipsTitle = '请点击搜索按钮更新数据';
            this.tipsContent = '';
            this.stVisible = true;
          }
        }
      } else {
        this.tipsTitle = '请输入搜索条件';
        this.tipsContent = '其中必填选项为年级、院校、专业、专业层次、论文资料及资料状态';
        this.grVisible = true;
      }
    },
    exportWideEcology() {
      var exportUrl = downUri + '/graduatePaper/exportStudentPaperThesis.do?' + '&unvsId=' + this.form.unvsId + '&pfsnId=' + this.form.pfsnId;
      // 导出文件
      window.location.href = exportUrl;
    }
  }
};

</script>

<style lang='scss' scoped>
.paper-ul {
  .paper-li {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px dashed #DCDFE6;

    .left {
      float: left;
      width: 230px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .right {
      float: right;
    }
  }
}

.margin-5 {
  margin: 5px 0;
}

.tag {
  background: transparent;
  border: none;
}

.right {
  padding: 7px;
  box-sizing: border-box;
  height: 40px;
  text-align: right;

  i {
    margin: 0 2px;
    cursor: pointer;
  }
}

.placeholder {
  width: 100%;
  text-align: center;
}

.align-center {
  align-items: center;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.gray {
  color: #C0C4CC;
}

::v-deep .el-table td {
  padding: 0;
}

::v-deep .text-top {
  vertical-align: top;
}

.no-file {
  color: #E8AA4D;
}

.file-status-1 {
  color: #6EC543;
}

.file-status-2 {
  color: #F57373;
}

</style>
