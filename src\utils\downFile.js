
import FileSaver from 'file-saver';
import axios from 'axios';

/**
 * 获取文件
 * @param url 文件的网络地址
 * @param fileName 文件名
 * @param callback 钩子函数
 */
const downFile = (url, fileName, callback) => {
  const config = {
    method: 'GET',
    responseType: 'blob'
  };
  axios(url, config).then(res => {
    FileSaver.saveAs(res.data, fileName || '文件'); // 利用file-saver保存文件,注意：第一个参数是blod二进制
    callback && callback();
  }).catch(error => {
    console.log(error);
  });
};

export default downFile;

// url需要传入完整地址
// 如:downFile(ossUri + file, file);

