<template>
  <div>
    <el-table ref="table" v-loading="tableLoading" size="small" :data="tableData" style="width: 100%; margin: 25px 0" border>
      <el-table-column prop="orderId" label="订单号" align="center" />
      <el-table-column prop="productId" label="商品id" align="center" />
      <el-table-column prop="productName" label="商品名称" align="center" />
      <el-table-column label="商品类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.productType | goodsTypeEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="productSpecName" label="规格名称" align="center" />
      <el-table-column prop="totalPrice" label="商品单价（元）" align="center" />
      <el-table-column prop="amount" label="购买数量" align="center" />
      <el-table-column prop="totalPrice" label="商品总价（元）" align="center" />
      <el-table-column prop="freightAmount" label="运费（元）" align="center" />
      <el-table-column prop="couponDiscount" label="优惠券抵扣（元）" align="center">
        <template slot-scope="scope">
          <p>-{{ scope.row.couponDiscount }}</p>
        </template>
      </el-table-column>
      <el-table-column label="下单用户信息" align="center">
        <template slot-scope="scope">
          <p>远智编号: {{ scope.row.yzCode }}</p>
          <p>真实姓名: {{ scope.row.realName }}</p>
          <p>手机号: {{ scope.row.mobile }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="orderTime" label="下单时间" align="center" />
      <el-table-column prop="refundTime" label="退款申请时间" align="center" />
      <el-table-column prop="refundReason" label="退款原因" align="center" />
      <el-table-column prop="refundZm" label="智米退款金额（元）" align="center" />
      <el-table-column prop="refundBalance" label="余额退款金额（元）" align="center" />
      <el-table-column prop="refundCash" label="现金退款金额（元）" align="center" />
      <el-table-column label="退款审批状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.refundApprovalStatus | refundExamineStatusEnum }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button class="mt10" type="primary" size="small" @click="handleExamine(scope.row)">立即审核</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <examine-modal :visible.sync="examineVisible" :currentId="currentId" />
  </div>
</template>

<script>
import examineModal from './examine-modal';
import { arrToEnum } from '@/utils';
import { goodsType, refundExamineStatus } from './../../type';
const goodsTypeEnum = arrToEnum(goodsType);
const refundExamineStatusEnum = arrToEnum(refundExamineStatus);
export default {
  components: {
    examineModal
  },
  filters: {
    goodsTypeEnum(val) {
      return goodsTypeEnum[val] || '/';
    },
    refundExamineStatusEnum(val) {
      return refundExamineStatusEnum[val] || '/';
    }
  },
  data() {
    return {
      refundExamineStatus: refundExamineStatus, // 退款审核状态
      examineVisible: false, // 审核弹框
      currentId: '',
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableLoading: false,
      tableData: []
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    // 立即审核
    handleExamine(row) {
      this.currentId = row.id;
      this.examineVisible = true;
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = {
        refundApprovalStatus: 2, // 退款审核状态 【2:待审核，3:审核通过，4:审核驳回】
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
      this.$post('getRefundList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>

<style lang = "scss" scoped>
.option-box {
  margin: 15px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    color:red;
  }
}
</style>
