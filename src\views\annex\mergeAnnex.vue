<template>
  <div class="yz-base-container">

    <h3 class="titVal">注册信息</h3>
    <el-descriptions class="margin-top" :column="2" :size="size" border>
      <el-descriptions-item :span='2'>
        <template slot="label">
          姓名
        </template>
        {{ stuInfo.xm }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          性别
        </template>
        {{ stuInfo.xbname }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          出生日期
        </template>
        {{ stuInfo.csrq }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          证件类型
        </template>
        {{ stuInfo.zjlxname }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          证件号
        </template>
        {{ stuInfo.zjdm }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          民族
        </template>
        {{ stuInfo.mzname }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          户口所在地
        </template>
        {{ stuInfo.hkname }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          密码
        </template>
        {{ ruleForm.password || stuInfo.password }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          确认密码
        </template>
        {{ ruleForm.password || stuInfo.password }}
      </el-descriptions-item>
      <el-descriptions-item :span='2'>
        <template slot="label">
          移动电话
        </template>
        {{ stuInfo.lxsj }}
        <span class="tips">(此手机号码用于绑定您的网上报名号)</span>
      </el-descriptions-item>
    </el-descriptions>

    <h3 class="titVal">基本信息</h3>
    <el-descriptions class="margin-top" :column="2" :size="size" border>

      <el-descriptions-item>
        <template slot="label">
          政治面貌
        </template>
        {{ stuInfo.zzmmname }}
      </el-descriptions-item>
      <el-descriptions-item :span='2'>
        <template slot="label">
          考试语种
        </template>
        {{ stuInfo.wyyzname }}
        <span class="tips">(专升本考生只能选报英语)</span>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          考试类型
        </template>
        {{ stuInfo.kslxdm === '0' ? '参加考试' : '免试入学' }}
        <span class="tips">(免试生请选择免试入学)</span>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          照顾加分
        </template>
        <!-- {{ stuInfo.kslbname }} -->
        <span class="tips">(山区县和25岁不用勾选)</span>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          考试类别
        </template>
        {{ stuInfo.kslbname }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          报考科类
        </template>
        {{ stuInfo.jhlbname }}
        <span class="tips">(报考专升本的外语类专业请选择"文史类")</span>
      </el-descriptions-item>
      <el-descriptions-item :span='2'>
        <template slot="label">
          考试科目组
        </template>
        {{ stuInfo.kmzname }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          考试县区
        </template>
        {{ stuInfo.xqname }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          报名点
        </template>
        {{ stuInfo.bmddmmc }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          考前学历
        </template>
        {{ stuInfo.kqxlname }}
      </el-descriptions-item>

      <el-descriptions-item :span='2'>
        <template slot="label">
          职业
        </template>
        {{ stuInfo.zyname }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template slot="label">
          毕业学校
        </template>
        {{ stuInfo.byxx }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          毕业年月
        </template>
        {{ stuInfo.byrq }}
      </el-descriptions-item>
      <el-descriptions-item :span='2'>
        <template slot="label">
          毕业专业
        </template>
        {{ stuInfo.byzy }}
      </el-descriptions-item>
      <el-descriptions-item :span='2'>
        <template slot="label">
          毕业证书号
        </template>
        {{ stuInfo.byzshm }}
        <span class="tips">(未取得专科毕业证的专科生请输入"待定")</span>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          邮政编码
        </template>
        {{ stuInfo.yzbm }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          固定电话
        </template>
        {{ stuInfo.lxdh }}
      </el-descriptions-item>

      <el-descriptions-item :span='2'>
        <template slot="label">
          通讯地址
        </template>
        {{ stuInfo.txdz }}
      </el-descriptions-item>
    </el-descriptions>

    <!-- copy 专用 -->
    <h3 class="titVal">报考志愿</h3>
    <div class="table-b">
      <table v-if="stuInfo.kslbdm === '1'">
        <thead>
          <tr>
            <th>批次</th>
            <th>报考院校</th>
            <th>报考专业</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td :rowspan="2">专科升本科</td>
            <td style="color:red;">
              院校1：{{ stuInfo.zsbpc1bkyx1 || '' }}
            </td>
            <td class="td-flex">
              <span class="red">
                专业1：{{ stuInfo.zsbpc1bkyx1zy1 || '' }}
              </span>
              <span>专业2：不必填写</span>
            </td>
          </tr>
          <tr>
            <!-- <td style="color:red;">
              院校2：{{ stuInfo.zsbpc1bkyx2 || '' }}
              <span class="copyTxt" @click="copy(stuInfo.zsbpc1bkyx2)">复制</span>
            </td> -->
            <td>
              院校2：不必填写
            </td>
            <td class="td-flex">
              <!-- <span class="red">
                专业1：{{ stuInfo.zsbpc1bkyx2zy1 || '' }}
              </span> -->
              <span>专业1：不必填写</span>
              <span>专业2：不必填写</span>
            </td>
          </tr>
        </tbody>
      </table>

      <table v-else-if="stuInfo.kslbdm === '5'">
        <thead>
          <tr>
            <th colspan="2">批次</th>
            <th>报考院校</th>
            <th>报考专业</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td :rowspan="999">高起专</td>
            <td rowspan="2"> 脱产班</td>
            <td class="taleft">院校1：不必填写</td>

            <td class="td-flex">
              <span>专业1：不必填写</span>
              <span>专业2：不必填写</span>
            </td>
          </tr>
          <tr>
            <td class="taleft">院校2：不必填写</td>

            <td class="td-flex">
              <span>专业1：不必填写</span>
              <span>专业2：不必填写</span>
            </td>
          </tr>
          <tr>
            <td rowspan="3">非脱产班</td>
            <td class="taleft" style="color:red">
              院校1：{{ stuInfo.zsbpc1bkyx1 }}
            </td>
            <td class="td-flex">
              <span class="red">
                专业1：{{ stuInfo.zsbpc1bkyx1zy1 }}
              </span>
              <span>专业2：不必填写</span>
            </td>
          </tr>

        </tbody>
      </table>
    </div>
    <div class="upload-box">
      <p>注：您的备用资料下载（如无用上请忽略）
        <span class="down-click" @click="downAllFileFn">一键下载</span> </p><br />
      <template v-for="(item,index) in annexInfo">
        <p v-if="item.isRequire === '1'" :key="index">
          {{ item.annexName }}:
          <span class="down-click" @click="downFileFn(item)">点击下载</span>
        </p>
      </template>

      <template v-for="item in annexlist">
        <p v-if="item.annexUrl" :key="item.annexId">
          {{ item.annexTypeName }}:
          <span class="down-click" @click="downLiveFileFn(item)">点击下载</span>
        </p>
      </template>
      <div class="pdf-box">
        <el-button v-if="liveList.length > 1" type="primary" size="small" @click="downLivePdf(1)">下载居住证pdf</el-button>
        <el-button v-if="houseHoldList.length > 1" type="primary" size="small" @click="downLivePdf(2)">下载户口本pdf</el-button>
      </div>
      <div v-for="(item) in annexlist" :key="item.annexType" class="fg-box">
        <img :id="'fg' + item.annexId" :src="item.annexUrl | imgFilters" alt="" />
      </div>

    </div>

  </div>
</template>

<script>

import downFile from '@/utils/downFile';
import { ossUri } from '@/config/request';
import downScript from '@/utils/down-script';
import buildZipDownload from '@/utils/zip';
import Driver from 'driver.js';
import { jsPDF } from 'jspdf';
import 'driver.js/dist/driver.min.css';
export default {
  filters: {
    imgFilters(val) {
      return ossUri + val;
    }
  },
  data() {
    return {
      selFiles: [],
      pdfWidth: 19,
      size: 'medium',
      type: 'undergraduate1',
      checked: true,
      stuInfo: {},
      annexInfo: [],
      tempPwd: '',
      ruleForm: {
        examNo: '',
        username: '',
        password: ''
      },
      driver: '',
      copyBtnShow: true,
      sceneRegisterList: [],
      annexlist: [],
      liveList: [], // 居住证
      houseHoldList: [], // 户口本
      checkedAct: '1',
      frontIdcardUrl: '', // 身份证正面url
      backIdcardUrl: '', // 身份证反面url
      diplomaUrl: '', //  毕业证扫描件
      bluePhotoUrl: '', // 蓝底证件照
      learnId: '',
      confirmId: ''
    };
  },
  mounted() {
    this.learnId = this.$route.query.learnId;
    this.confirmId = this.$route.query.confirmId;
    this.stdId = this.$route.query.stdId;
    if (new Date().getTime() > 1631329200000) {
      this.copyBtnShow = false;
    }
    this.getDataInfo();
    this.getRegisterList();
    this.getannexInfo();
    this.getStuInfoUrl(); // 下载
  },
  methods: {
    downLivePdf(type) {
      if (type === 1) {
        this.selFiles = this.liveList;
      } else {
        this.selFiles = this.houseHoldList;
      }
      this.downPdf(type);
    },
    // 下载脚本
    downSript() {
      downScript(this.stuInfo);
      this.copy(downScript(this.stuInfo));
    },
    // 获取预报名列表信息
    getRegisterList() {
      this.$post('getSceneRegisterList', { learnId: this.learnId }).then(res => {
        const { code, body } = res;
        if (code === '00' && body) {
          this.sceneRegisterList = body;
          this.sceneRegisterList.forEach(item => {
            if (item.register_status === '1') {
              this.checkedAct = item.register_id;
              this.ruleForm.username = item.username || '';
              this.ruleForm.password = item.password || this.tempPwd;
            }
          });
        }
      });
    },
    // 获取学生基本信息
    getDataInfo() {
      this.$post('getSudentInfo', { learnId: this.learnId }).then(res => {
        const { code, body } = res;
        if (code === '00' && body) {
          this.stuInfo = body;
          this.tempPwd = body.password;
        }
      });
    },
    downFileFn(item) {
      downFile(ossUri + item.annexUrl + '?time=' + new Date().getTime(), item.annexName);
    },
    downLiveFileFn(item) {
      downFile(ossUri + item.annexUrl + '?time=' + new Date().getTime(), item.annexTypeName);
    },
    // 获取附件资料
    getStuInfoUrl() {
      const data = {
        learnId: this.learnId,
        recruitType: 1
      };
      this.$post('getAnnexList', data)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.annexInfo = body.data;
          }
        });
    },
    // 一键下载
    downAllFileFn() {
      this.$message({
        message: '正在下载中，请耐心等待...',
        type: 'success'
      });
      const fileList = [];
      this.annexInfo.forEach(item => {
        if (item.isRequire === '1' && item.annexUrl) {
          const urlFormat = item.annexUrl.substring(item.annexUrl.lastIndexOf('.'));
          fileList.push({
            fileUrl: ossUri + item.annexUrl,
            fileName: this.stuInfo.xm + item.annexName + urlFormat
          });
        }
      });
      this.annexlist.forEach(item => {
        if (item.annexUrl) {
          const urlFormat = item.annexUrl.substring(item.annexUrl.lastIndexOf('.'));
          fileList.push({
            fileUrl: ossUri + item.annexUrl + '?v=' + new Date().getTime(),
            fileName: this.stuInfo.xm + item.annexTypeName + urlFormat
          });
        }
      });
      this.selFiles = [...this.liveList, ...this.houseHoldList];
      if (this.liveList.length > 1) {
        this.downLivePdf(1);
      }
      if (this.houseHoldList.length > 1) {
        this.downLivePdf(2);
      }
      buildZipDownload(fileList, `${this.stuInfo.xm}+${this.stuInfo.zjdm}`);
    },

    // 户口本等附件
    getannexInfo() {
      const data = {
        learnId: this.learnId,
        start: '0',
        length: '10'
      };
      this.$post('getannexlist', data).then(res => {
        console.log(res, 'getannexlist');
        const { code, body } = res;
        if (code === '00' && body) {
          this.annexlist = body.data;
          if (this.annexlist.length === 0) return;
          this.annexlist.map(item => {
            if (item.annexUrl) {
              if (item.annexType === 8 || item.annexType === 9) {
                this.liveList.push({ 'id': 'fg' + item.annexId });
              }
              if (item.annexType === 10 || item.annexType === 11) {
                this.houseHoldList.push({ 'id': 'fg' + item.annexId });
              }
            }
          });
          // this.selFiles = this.liveList;
        }
      });
    },
    // 得到pdf内容的高度
    getPdfHeight() {
      // const img = document.getElementById('fg80');
      // console.log(img.height, 'img.height111');
      // console.log(this.selFiles);
      let allHeight = 0;
      // const psflist =
      for (var i = 0; i < this.selFiles.length; i++) {
        const one = this.selFiles[i];
        // 得到高度
        const img = document.getElementById(one.id);
        console.log(img.height, 'img.height');
        allHeight = allHeight + img.height;
      }
      const pdfHeight = (allHeight * this.pdfWidth) / 400;
      return pdfHeight;
    },
    // 下载pdf
    downPdf(type) {
      console.log('下载pdf');
      const pdfHeight = this.getPdfHeight();
      // eslint-disable-next-line new-cap
      const recordPdf = new jsPDF({ unit: 'cm', format: [21, pdfHeight + 2] });
      // 遍历图片
      let top = 1;
      for (var i = 0; i < this.selFiles.length; i++) {
        const one = this.selFiles[i];
        // 得到图片所占内容的高度
        const img = document.getElementById(one.id);
        const destHeight = (img.height * 19) / img.width;
        // pdf内容添加图片
        recordPdf.addImage(img.src + '?v=' + new Date().getTime(), 'jpeg', 1, top, this.pdfWidth, destHeight);
        top = top + destHeight + 1;
      }
      // 保存pdf
      let strName = type === 1 ? '居住证.pdf' : '户口本.pdf';
      strName = this.stuInfo.xm + strName;
      recordPdf.save(strName);
    }
  }
};
</script>

<style lang = "scss" scoped>
.yz-base-container{
  width: 50vw;
  /* margin: 0 auto; */
}
.margin-top{
  margin-top: 30px;
  margin-bottom: 30px;
  span{
    /* float: right; */
    color: #3693f7;
    cursor: pointer;
    margin-left: 20px;
    font-weight: 600;
  }
  .tips{
    color: red;
    cursor: auto;
  }
}
.table-b {
  table {
    width: 100%;
    border-top: 1px solid #EBEEF5;
    border-left: 1px solid #EBEEF5;
    border-right: 1px solid #EBEEF5;

    th, td {
      font-size: 16px;
      font-weight: 400;
      vertical-align: middle;
      border-bottom: 1px solid #EBEEF5;
      border-right: 1px solid #EBEEF5;
      background-color: #fff;
      color: #606266;
      /*border-right: 1px solid #72dfff;*/
      padding: 4px;
      text-align: center;
      &:last-of-type {
        border-right: none;
      }
      &.text-l {
        text-align: left;
      }
      span{
        /* display: block;
        line-height: .28rem; */
        margin-right: 50px;
      }
    }
    .td-flex{
      display: flex;
      span{
        flex:1;
      }
      &>span{
        width: 50%;
        text-align: left;
        padding-left: 20px;
      }
    }

  }
}
.demo-ruleForm{
  width: 900px;
  margin-top: 20px;
}
.titVal{
  text-align: center;
  color: #000;
  opacity: .8;
}
.upload-box{
  margin: 20px 0;
  p{
    display: inline-block;
  }
  span{
    color: #3693f7;
    cursor: pointer;
    margin-right: 20px;
    line-height: 30px;
    font-weight: 600;
  }

}
.hidden {
  width: 0;
  height: 0;
  position: absolute;
  top:0;
  opacity: 0 !important;
}
.copy{
  color: #3693f7;
  cursor: pointer;
  margin-right: 20px;
  font-weight: 600;
}
.table{
  margin-bottom: 20px;
  .checked{
    margin-right: 10px;
  }
}
.tableRecord ::v-deep .el-table__header-wrapper{
  display: none;
}
.red{
  color: red;
}

.copyTxt{
  color: #3693f7;
  margin-left: 30px;
  cursor: pointer;
}
::v-deep .el-descriptions-item__content{
  color: #000;
}
.table-b table td{
  color: #000;
}

.down-click{
  color: #3693f7;
  font-weight: 600;
}
.taleft{
  text-align:left!important;
  padding-left: 20px!important;
}
.form-box{
  margin-bottom: 15px;
  background-color: #f7f7f7;
  padding: 10px;
  .form-head{
    padding-top: 16px;
    padding-left: 20px;
    font-size: 15px;
    color: #666;
    .isfinish{
      color: red;
    }
  }
}
.tips-box{
  img{
    /* width: 1100px;
    height: 2000px; */
    width: 100%;
    height: 100%;
  }
}
.header{
  padding-top: 20px;
  width: 1000px;
  /* height: 100px; */
  line-height: 25px;
  font-weight: 600;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  div{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    flex-shrink: 0;
    p{
      margin-top: 5px;
    }
  }
  .step-title{

    position: relative;
    display: inline-block;
    margin-right: 30px;
    width: 80px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    background-color: #ebf4fe;
    color: #5ea8f8;
    font-weight: 400;
  }
    .step-title::before{
      position:absolute;
      content: "";
      width: 20px;
      height: 30px;
      right: -20px;
      top: 0;
      /* background-color: pink; */
      border-style: solid;
      border-color: transparent;
      border-width: 15px 0 15px 20px;
      border-left-color: #ebf4fe;
    }
}
.handle-step{
  color:#1890ff;
  cursor: pointer;
  font-weight: 400;

}
.pop-head{
  display: flex;
  align-items: center;
  margin-left: 57px;
  span{
    font-size: 22px;
    font-weight: 600;
    margin-right: 10px;
    color: #1a1b1d;
  }
  ::v-deep .el-button--small{
    font-size: 16px;
  }
}
.split{
  width: 100%;
  height: 1px;
  margin-top: 20px;
  background-color: #eee;
}
.fg-box{
  display: none;
}
.intro-second{
  padding-top: 20px;
}
::v-deep .el-step__description.is-finish{
  color: #000;
}
::v-deep .el-step__description{
  font-size: medium;
}
::v-deep .el-step.is-vertical .el-step__title{
  font-size: 18px;
  color: #000;
  font-weight: 600;
}
::v-deep  .el-step:last-of-type .el-step__line{
  display: block;
  background-color: #409EFF;
}
.update-form{
  margin-left: 30px;
}
.pdf-box{
  margin: 10px 0;
}
</style>

