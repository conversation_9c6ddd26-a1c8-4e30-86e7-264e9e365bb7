<template>
  <div class="attached">
    <div class="attached-text" @click="open">功能演示</div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="1000px"
      append-to-body
    >
      <div v-if="dialogVisible" ref="video" class="video"></div>
      <div></div>
    </el-dialog>
  </div>
</template>

<script>
import Player from 'xgplayer';
import cookie from '@/utils/cookie';
export default {
  components: {},
  data() {
    return {
      dialogVisible: false,
      player: null
    };
  },
  created() {
  },
  mounted() {
    const status = cookie.getItem('isEditFirst');
    console.log(status, '11');
    if (!status) {
      this.open();
      cookie.setItem('isEditFirst', 1, '365d');
    }
  },
  methods: {
    open() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.initPlayer();
      });
    },
    initPlayer() {
      this.player = new Player({
        el: this.$refs['video'],
        width: '100%',
        height: 520,
        videoInit: true,
        autoplay: true, // 自动播放
        fitVideoSize: 'auto',
        volume: 0,
        playbackRate: [1, 1.25, 1.5, 2],
        url: 'https://yzims.oss-cn-shenzhen.aliyuncs.com/paperDemon/demo.mp4'
      });
    }
  }
};
</script>

<style lang='scss' scoped>
.attached {
  width: 40px;
  height: 144px;
  position: fixed;
  right: 0;
  top: 300px;
  background: #303133;
  border-radius: 18px 0px 0px 18px;
  cursor: pointer;
  user-select: none;
  z-index: 600;
}
.attached-text {
  width:20px;
  line-height: 29px;
  color: #fff;
  margin: 0 auto;
  text-align: center;
  padding: 14px 0;
  font-size: 21px;
  font-weight: 600;
}

::v-deep .el-dialog {
  background: transparent;
  box-shadow: none;

  .el-dialog__headerbtn {
    top: 10px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    color: #fff;
    font-size: 30px;
  }
}

</style>
