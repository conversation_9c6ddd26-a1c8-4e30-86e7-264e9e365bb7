VUE_APP_RUN_ENV = 'testing'

# just a flag
ENV = 'development'

# base api
# VUE_APP_DOWN_URL_API = 'http://pre-bms.yzwill.cn'
# -- VUE_APP_DOWN_URL_API = 'http://test2-bms.yzwill.cn'
# 自己使用的
VUE_APP_DOWN_URL_API = 'http://pre-bms.yzwill.cn'
# VUE_APP_OSS_URL = //yzpres.oss-cn-guangzhou.aliyuncs.com/
VUE_APP_BST_URL = 'http://24-bst.yzwill.cn'
VUE_APP_DOWN_OSS_URL = //senstest.yzwill.cn

# 192 pre 环境
# VUE_APP_OSS_URL = //img2.yzwill.cn/
VUE_APP_OSS_URL = //new-yzpres.oss-cn-shenzhen.aliyuncs.com/

# 171 环境
# VUE_APP_OSS_URL = //yzimstest.oss-cn-shenzhen.aliyuncs.com/
# VUE_APP_OSS_URL = //yzimstemp.oss-cn-shenzhen.aliyuncs.com/

# bms 51 环境
# VUE_APP_DOWN_URL_API = 'http://pre-bms.yzwill.cn/'
# VUE_APP_OSS_URL = //yzpres.oss-cn-guangzhou.aliyuncs.com/
# VUE_APP_BST_URL = 'http://pre-bst.yzwill.cn'

# 网报api请求接口地址
# VUE_APP_NET_NEWSPAPER_API = https://bms.yzwill.cn
VUE_APP_NET_NEWSPAPER_API = https://new.yzou.cn

# 知识库api请求接口地址
VUE_APP_NET_KNOWLEDGE_API = http://pre3-bms.yzwill.cn/

# 成人教育教育考试院代理地址
# VUE_APP_EEA_PROXY_URL = https://35-ijiaolian-mp.yzwill.cn
VUE_APP_EEA_PROXY_URL = https://nrs.yzou.cn

# BMS 域名
# VUE_APP_BMS_URL = //bms.yzwill.cn
VUE_APP_BMS_URL = //test0-bms.yzwill.cn
