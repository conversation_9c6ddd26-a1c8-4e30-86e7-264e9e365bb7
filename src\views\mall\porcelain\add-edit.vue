<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    :title="`${ isEdit ? '修改' : '新增'}` + '商城瓷片区配置'"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        size="small"
      >
        <el-form-item label="瓷片区名称:" prop="name">
          <el-input v-model="form.name" placeholder="用于内部显示" />
        </el-form-item>

        <el-form-item label="展示时间:" required>
          <div style="display: flex;">
            <el-form-item prop="showTimeStatus">
              <el-radio-group v-model="form.showTimeStatus">
                <el-radio label="PERMANENT_EFFECTIVE">永久有效</el-radio>
                <el-radio label="TIME_EFFECTIVE">固定时间</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="form.showTimeStatus == 'TIME_EFFECTIVE'" class="ml10" prop="unveilTime">
              <el-date-picker
                v-model="form.unveilTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </div>
        </el-form-item>

        <el-form-item label="瓷片区权重:" prop="weight">
          <div style="display: flex;">
            <el-input-number v-model="form.weight" :controls="false" :min="0" :max="9999" placeholder="请输入瓷片区权重" style="width: 200px" />
            <span>（数字越大，排序越靠前）</span>
          </div>
        </el-form-item>

        <el-form-item label="瓷片区正方图:" prop="squareImgUrl">
          <p>建议图片尺寸为330*330, 图片大小限制1M内</p>
          <upload-file
            :size="1"
            :max-limit="1"
            exts="jpg|png"
            :file-list="squareImgUrl"
            @remove="squareImgRemove"
            @success="squareImgSuccess"
          />
        </el-form-item>

        <el-form-item label="瓷片区长方图:" prop="rectangleImgUrl">
          <p>建议图片尺寸为336*156, 图片大小限制1M内</p>
          <upload-file
            :size="1"
            :max-limit="1"
            exts="jpg|png"
            :file-list="rectangleImgUrl"
            @remove="rectangleImgRemove"
            @success="rectangleImgSuccess"
          />
        </el-form-item>

        <el-form-item label="跳转页面:" required>
          <div class="banner-page">
            <el-form-item prop="jumpType">
              <el-radio-group v-model="form.jumpType" @change="jumpTypaChange">
                <el-radio label="ACTIVITY">商品活动页</el-radio>
                <el-radio label="PRODUCT">商品详情页</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="form.jumpType" prop="jumpId" class="banner-page-input-box">
              <el-input v-if="form.jumpType == 'ACTIVITY'" v-model="form.jumpId" placeholder="请输入活动id" />
              <el-input v-if="form.jumpType == 'PRODUCT'" v-model="form.jumpId" class="goods-id-input" placeholder="请输入商品id" />
            </el-form-item>
          </div>
        </el-form-item>

        <el-form-item label="状态:" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      confirmLoading: false,
      show: false,
      isEdit: false, // 是否编辑
      form: {
        squareImgUrl: '',
        rectangleImgUrl: '',
        jumpId: undefined
      },
      squareImgUrl: [],
      rectangleImgUrl: [],
      rules: {
        name: [
          { required: true, message: '请输入瓷片区名称', trigger: 'blur' }
        ],
        showTimeStatus: [
          { required: true, message: '请选择展示时间', trigger: 'change' }
        ],
        unveilTime: [
          { required: true, message: '请选择展示时间', trigger: 'change' }
        ],
        weight: [
          { required: true, message: '请输入瓷片区权重', trigger: 'blur' }
        ],
        squareImgUrl: [
          { required: true, message: '请上传瓷片区正方图', trigger: 'change' }
        ],
        rectangleImgUrl: [
          { required: true, message: '请上传瓷片区长方图', trigger: 'change' }
        ],
        jumpType: [
          { required: true, message: '请选择跳转页面', trigger: 'change' }
        ],
        jumpId: [
          { required: true, message: '请输入id', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    jumpTypaChange() {
      this.form.jumpId = undefined;
    },
    // 瓷片区正方图删除
    squareImgRemove({ file, fileList }) {
      this.form.squareImgUrl = '';
    },
    // 瓷片区正方图上传成功
    squareImgSuccess({ response, file, fileList }) {
      this.form.squareImgUrl = response;
    },
    // 瓷片区长方图删除
    rectangleImgRemove({ file, fileList }) {
      this.form.rectangleImgUrl = '';
    },
    // 瓷片区长方图上传成功
    rectangleImgSuccess({ response, file, fileList }) {
      this.form.rectangleImgUrl = response;
    },
    // 打开弹框
    open() {
      if (this.currentId) {
        this.isEdit = true;
        this.$http.get(`/mallPorcelainConfig/getById/${this.currentId}`).then(res => {
          const { fail, body } = res;
          if (!fail) {
            const {
              id,
              name,
              showTimeStatus,
              showStartTime,
              showEndTime,
              squareImgUrl,
              rectangleImgUrl,
              jumpType,
              jumpId,
              status,
              weight } = body;
            this.form = {
              id,
              name,
              showTimeStatus,
              squareImgUrl,
              rectangleImgUrl,
              jumpType,
              jumpId,
              status,
              weight,
              unveilTime: showTimeStatus == 'TIME_EFFECTIVE' ? [showStartTime, showEndTime] : []
            };
            this.squareImgUrl.push({ url: ossUri + squareImgUrl });
            this.rectangleImgUrl.push({ url: ossUri + rectangleImgUrl });
          }
        });
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        this.confirmLoading = true;

        const form = JSON.parse(JSON.stringify(this.form));
        if (form.showTimeStatus == 'TIME_EFFECTIVE') {
          form.showStartTime = form.unveilTime[0];
          form.showEndTime = form.unveilTime[1];
        } else {
          delete form.showStartTime;
          delete form.showEndTime;
        }
        delete form.unveilTime;

        let apiKey = 'addZMPorcelain';
        if (this.isEdit) {
          apiKey = 'updateZMPorcelain';
        }
        this.$post(apiKey, form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>

<style lang='scss' scoped>
.banner-page {
  display: flex;
  flex-direction: row;

  .el-radio {
    display: block;
    line-height: 32px;
  }
  .banner-page-input-box {
    flex: 1;
  }
  .goods-id-input {
    margin-top: 30px;
  }
}
</style>
