<template>
    <div class="yz-base-container">
        <el-form :model="form" label-width="100px" inline>
            <el-form-item label="学业编码：">
                <el-input v-model="form.learnId" placeholder="请输入学业编码"></el-input>
            </el-form-item>
            <el-form-item label="远智编码：">
                <el-input v-model="form.yzCode" placeholder="请输入远智编码"></el-input>
            </el-form-item>
            <el-form-item label="消息创建人：">
                <el-input v-model="form.createEmpName"></el-input>
            </el-form-item>
            <el-form-item label="消息名称：">
                <el-select v-model="form.mpMsgId" placeholder="请选择" style="width: 187PX;" clearable filterable :filter-method="getMsgTitleList">
                    <el-option v-for="item in msgTitleList" :key="item.mpMsgId" :label="item.name" :value="item.mpMsgId"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="接收人：">
                <el-input v-model="form.receiver"></el-input>
            </el-form-item>
            <el-form-item label="发送方式：">
                <el-select v-model="form.sendType" placeholder="发送方式" clearable>
                    <el-option label="远智成教君" value="1"></el-option>
                    <el-option label="远智开放君" value="2"></el-option>
                    <el-option label="远智全日制" value="3"></el-option>
                    <el-option label="远智自考君" value="4"></el-option>
                    <el-option label="远智研究生" value="5"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="发送状态：">
                <el-select v-model="form.status" placeholder="请选择" style="width: 187PX;" clearable>
                    <el-option label="成功" value="1"></el-option>
                    <el-option label="失败" value="0"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="开始时间：">
                <el-date-picker v-model="form.sendStartTime" type="datetime" placeholder="选择日期时间"
                    style="width: 187PX;"></el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间：">
                <el-date-picker v-model="form.SendEndTime" type="datetime" placeholder="选择日期时间"
                    style="width: 187PX;"></el-date-picker>
            </el-form-item>
            <el-form-item label="班主任：">
                <el-input v-model="form.tutorName"></el-input>
            </el-form-item>
            <el-form-item class="search-btn">
                <el-button type="primary" @click="search(1)">搜索</el-button>
                <el-button @click="search()">重置</el-button>

            </el-form-item>
        </el-form>
        <el-table :data="tableData" border style="width: 100%" v-loading="loading">
            <el-table-column prop="learnId" label="学业编码" align="center"></el-table-column>
            <el-table-column prop="yzCode" label="远智编码" align="center"></el-table-column>
            <el-table-column prop="receiver" label="消息接收人" align="center"></el-table-column>
            <el-table-column prop="sendTypeName" label="发送方式" align="center"></el-table-column>
            <el-table-column prop="status" label="发送状态" align="center">
                <template slot-scope="scope">
                    {{scope.row.status == 1 ? '成功' : '失败'}}
                </template>
            </el-table-column>
            <el-table-column prop="remark" label="失败编码" align="center"></el-table-column>
            <el-table-column prop="sendTime" label="发送时间" align="center"></el-table-column>
            <el-table-column prop="msgName" label="消息名称" align="center"></el-table-column>
            <el-table-column prop="msgContent" label="消息内容" align="center" width="200"></el-table-column>
            <el-table-column prop="tutorName" label="班主任" align="center"></el-table-column>
            <el-table-column prop="createEmpName" label="创建人" align="center"></el-table-column>

        </el-table>
        <div class="block">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page.sync="page.pageNum" layout="prev, pager, next, sizes,jumper"  :page-sizes="[10, 20, 30, 40]"
                :total="page.total">
            </el-pagination>
        </div>

    </div>
</template>

<script>
import moment from 'moment'
export default {
    data() {
        return {
            page: {
                total: 0,
                pageSize: 10,
                pageNum: 1
            },
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            form: {
                learnId: '',
                yzCode: '',
                createEmpName: '',
                mpMsgId: '',
                receiver: '',
                sendType: '',
                status: '',
                sendStartTime: '',
                SendEndTime: '',
                tutorName: ''
            },
            tableData: [],
            loading:true,
            msgTitleList:[]
        };
    },
    created() {
        this.form.sendStartTime = new Date().getTime() - 3600 * 1000 * 24 * 30,
        this.form.SendEndTime = new Date(),
        this.getMessageRecordList()
        this.getMsgTitleList()
    },
    methods: {
        getMsgTitleList(e){
            this.$http.get(`/msgManage/getMsgTitleList?name=${e||''}&pageNum=1&pageSize=10000`,{json:true})
            .then((res)=>{
                this.msgTitleList = res.body.data
            })
        },
        getMessageRecordList() {
            this.loading = true
            let data = {
                learnId: this.form.learnId,
                yzCode: this.form.yzCode,
                createEmpName: this.form.createEmpName,
                mpMsgId: this.form.mpMsgId,
                receiver: this.form.receiver,
                sendType: this.form.sendType,
                status: this.form.status,
                sendStartTime: this.form.sendStartTime?moment(this.form.sendStartTime).format('YYYY-MM-DD HH:mm:ss') : '',
                SendEndTime:  this.form.SendEndTime?moment(this.form.SendEndTime).format('YYYY-MM-DD HH:mm:ss'):'',
                tutorName: this.form.tutorName,
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize
            }
            this.$http.post('/msgManage/getMsgSendLog', data, { json: true }).then(res => {
                if (res.ok) {
                    const responseData = res.body.data;
                    responseData.forEach(item => {
                        this.setSendTypeName(item);
                    });
                    this.tableData = res.body.data;
                    this.page.total = res.body.recordsTotal
                    this.loading = false

                }
            });
        },
        setSendTypeName(item) {
            const sendTypeMap = {
                1: '远智成教君',
                2: '远智开放君',
                3: '全日制',
                4: '远智自考君',
                5: '远智研究生',
                6: '中专'
            };
            item.sendTypeName = sendTypeMap[item.sendType] || '';
        },
        search(num) {
            if (num) {
                this.getMessageRecordList()
            } else {
                this.page.pageNum = 1
                this.page.pageSize = 10
                this.form = []
                this.getMessageRecordList()
            }
        },
        handleLook(index, row) {
            // 处理编辑逻辑
        },
        handleSizeChange(val) {
            this.loading = true
            this.page.pageSize = val
            this.getMessageRecordList()
        },
        handleCurrentChange(val) {
            this.loading = true
            this.page.pageNum = val
            this.getMessageRecordList()
        },
        expectClick() {
            this.$http.post('/messageConfiguration/list').then(res => {
                if (res.ok) {
                    // this.tableData = res.body;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.search-btn {
    float: right;
}

.btn {
    text-align: right;
    margin-bottom: 20px;
}

/* 可以在这里添加一些自定义样式 */
.el-form-item {
    margin-bottom: 15px;
}

.block {
    margin-top: 20px;
    text-align: right;
}
</style>