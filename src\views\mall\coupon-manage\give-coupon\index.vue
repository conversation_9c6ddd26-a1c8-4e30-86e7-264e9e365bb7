<template>
  <div>
    <!-- 顶部筛选 -->
    <el-form
      ref="searchForm"
      :model="form"
      label-width="120px"
      class="yz-search-form"
      size="mini"
      @submit.native.prevent='search'
    >
      <el-form-item label="远智编号:" prop="yzCode">
        <el-input v-model="form.yzCode" placeholder="请输入远智编号" clearable />
      </el-form-item>
      <el-form-item label="姓名:" prop="realName">
        <el-input v-model="form.realName" placeholder="请输入姓名" clearable />
      </el-form-item>
      <el-form-item label="手机:" prop="mobile">
        <el-input v-model="form.mobile" placeholder="请输入手机" clearable />
      </el-form-item>
      <el-form-item label="赠送优惠券名称:" prop="couponName">
        <el-input v-model="form.couponName" placeholder="请输入赠送优惠券名称" clearable />
      </el-form-item>
      <el-form-item label="赠送优惠券id:" prop="couponId">
        <el-input v-model="form.couponId" placeholder="请输入赠送优惠券id" clearable />
      </el-form-item>
      <el-form-item label="赠送状态:" prop="couponGiveStatus">
        <el-select v-model="form.couponGiveStatus" placeholder="请选择赠送状态" clearable>
          <el-option v-for="item in giveStatus" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="派券人:" prop="couponGiver">
        <el-input v-model="form.couponGiver" placeholder="请输入派券人" clearable />
      </el-form-item>
      <el-form-item label='赠送时间:' prop='giveTime'>
        <el-date-picker
          v-model="form.giveTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button size="mini" @click="search(0)">重置</el-button>
      </div>
    </el-form>

    <!-- 新增栏 -->
    <div style="float:right;margin:15px 25px 15px 0;">
      <el-button type="primary" size="mini" @click="handleExport">导出数据</el-button>
      <el-button type="primary" size="mini" @click="giveCouponVisible = true">新增</el-button>
    </div>

    <el-table ref="table" v-loading="tableLoading" size="small" :data="tableData" style="width: 100%; margin: 25px 0" border>
      <el-table-column label="赠送对象" align="center" width="150">
        <template slot-scope="scope">
          <div style="text-align: left;">
            <p>远智编号: {{ scope.row.yzCode || '无' }}</p>
            <p>真实姓名: {{ scope.row.realName || '无' }}</p>
            <p>昵称: {{ scope.row.nickname || '无' }}</p>
            <p>手机号: {{ scope.row.mobile || '无' }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="couponId" label="赠送优惠券id" align="center" />
      <el-table-column prop="couponName" label="赠送优惠券名称" align="center" />
      <el-table-column prop="createTime" label="赠送时间" align="center" />
      <el-table-column prop="couponGiveNum" label="每人赠送数量" align="center" />
      <el-table-column prop="couponGiveActualNum" label="实际赠送数量" align="center" />
      <el-table-column label="赠送状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.couponGiveStatus | giveStatusEnum }}
        </template>
      </el-table-column>
      <el-table-column label="异常原因" align="center">
        <template slot-scope="scope">
          {{ scope.row.couponGiveErrorReason || '/' }}
        </template>
      </el-table-column>
      <el-table-column prop="couponGiver" label="派券人" align="center" />
      <el-table-column prop="remark" label="赠送优惠券备注" align="center">
        <template slot-scope="scope">
          {{ scope.row.remark || '/' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button class="mt10" type="primary" size="small" @click="handleRemark(scope.row)">备注</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 备注弹框 -->
    <remark-modal :visible.sync="remarkVisible" :currentRow="currentRow" />
    <!-- 赠送优惠券弹框 -->
    <give-coupon-modal :visible.sync="giveCouponVisible" @refresh="getTableList" />
  </div>
</template>

<script>
import { arrToEnum } from '@/utils';
import { giveStatus } from './../../type';
import { httpPostDownFile } from '@/utils/downExcelFile';
const giveStatusEnum = arrToEnum(giveStatus);

import RemarkModal from './remark-modal.vue';
import giveCouponModal from './give-coupon-modal.vue';
export default {
  components: {
    RemarkModal,
    giveCouponModal
  },
  filters: {
    giveStatusEnum(val) {
      return giveStatusEnum[val] || '/';
    }
  },
  data() {
    return {
      remarkVisible: false, // 备注弹框
      giveCouponVisible: false, // 赠送优惠券弹框
      currentRow: {}, // 当前行数据
      giveStatus: giveStatus,
      form: {
        yzCode: '',
        realName: '',
        mobile: '',
        couponName: '',
        couponId: '',
        couponGiveStatus: '',
        couponGiver: '',
        giveTime: [] // 赠送时间范围
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableLoading: false,
      tableData: []
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    // 导出
    handleExport() {
      const params = this.handleQueryParams();
      httpPostDownFile({
        url: '/couponGiveRecord/exportRecord',
        params
      });
    },
    handleRemark(row) {
      this.currentRow = row;
      this.remarkVisible = true;
    },
    // 查询和重置
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 处理筛选参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      formData.giveStartTime = formData.giveTime[0] ?? '';
      formData.giveEndTime = formData.giveTime[1] ?? '';
      delete formData.giveTime;
      return {
        ...formData,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQueryParams();
      this.$post('getZMCouponGiveRecordList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>

<style lang = "scss" scoped>
.text-left {
  text-align: left;
}
</style>
