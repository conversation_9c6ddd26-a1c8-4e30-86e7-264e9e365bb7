export const REG = {
  idCard: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  mobile: /^1[3456789]\d{9}$/,
  email: /^\w+@[a-z0-9]+\.[a-z]+$/i,
  zipCode: /^[1-9][0-9]{5}$/,
  emoji: /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g,
  space: /\s+/g, // 空格
  numAndEnglish: /^[A-Za-z0-9]+$/, // 数字和字符
  positiveInteger: /^[+]{0,1}(\d+)$/, // 正整数
  numChinaEng: /[^a-zA-Z0-9\u4e00-\u9fa5\u3002\uff0c\uff1a\uff08\uff09\uff1f\u201c\u201d\u3001\uff01,/.!:()?_""—-【】/@]/, // 只可输入中文、英文、数字、符号（部分常用符号）
  patrn: /[`~#$^&*_"{}|.\/;'\\·#￥*]/ // 特殊字符
};

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor'];
  return valid_map.indexOf(str.trim()) >= 0;
}

/**
 * 公共的正则校验方法
 * @param {String} key 正则的名字 对应上面的REG
 * @param {String} val 值
 */
export function validate(key, val) {
  const reg = REG[key];
  if (!reg.test(val)) {
    return false;
  }
  return true;
}
