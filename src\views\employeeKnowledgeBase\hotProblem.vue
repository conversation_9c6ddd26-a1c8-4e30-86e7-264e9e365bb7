<template>
  <div>
    <common-dialog
      is-full
      class="common-dialog"
      width="1100px"
      :title="optionsType"
      :visible.sync="show"
      @open="open"
      @close="close"
    >
      <el-form ref="form" :model="form" label-width="110px" class="yz-search-form">
        <el-form-item label="问题：">
          <el-input v-model="form.searchWord" placeholder="请输入问题" />
        </el-form-item>
        <el-form-item label="开始时间:">
          <el-date-picker v-model="form.startSearchTime" type="date" placeholder="选择日期" />
        </el-form-item>
        <el-form-item label="结束时间:">
          <el-date-picker v-model="form.endSearchTime" type="date" placeholder="选择日期" />
        </el-form-item>
        <el-form-item label="问题排序：">
          <el-select v-model="form.sortType" placeholder="请选择问题排序">
            <el-option label="自然排名优先" value="1" />
            <el-option label="手动排名优先" value="2" />
          </el-select>
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" @click="query">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </el-form>
      <div style="float:right;margin:15px 25px 15px 0;">
        <el-button type="primary" @click="exportList()">导出Excel</el-button>
      </div>
      <el-table ref="multipleTable" :data="tableData" style="width: 100%; margin: 25px 0" border>
        <el-table-column prop="searchWord" label="问题" align="center" />
        <el-table-column prop="count" label="查询次数" align="center" />
        <el-table-column prop="naturalSort" label="自然排名" align="center" />
        <el-table-column prop="manualSort" label="手动排名" align="center" />
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <div class="iconStyle">
              <div class="el-icon-edit-outline" @click="handleEdit(scope.$index, scope.row)"></div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pageClass">
        <el-pagination
          :current-page.sync="page.currentPage"
          :page-size="page.pageSize"
          :total="page.total"
          :page-sizes="[10, 20, 30, 40]"
          layout="total, prev, pager, next, sizes,jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </common-dialog>
    <el-dialog
      title="修改排名"
      :visible.sync="rankingShow"
      width="30%"
      :before-close="handleClose"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="110px">
        <el-form-item label="手动排名：" prop="number">
          <el-input v-model="ruleForm.number" placeholder="请输入排名" />
        </el-form-item>
        <el-form-item label="注：">
          <div>1：数字越小，排名越前</div>
          <div>2：输入0代表不参与手动排名</div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <el-button type="primary" @click="ok('ruleForm')">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ossUri, downUri } from '@/config/request';
import moment from 'moment';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    optionsType: {
      type: String,
      default: ''
    },
    infoList: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      form: {
        sortType: '1',
        startSearchTime: '',
        endSearchTime: '',
        searchWord: ''
      },
      ruleForm: {},
      tableData: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 1
      },
      rankingShow: false,
      rules: {
        number: [
          { required: true, message: '请输入0~9999的数字', trigger: 'blur' }
        ]
      },
      showList: {}
    };
  },
  watch: {
    visible(val) {
      this.show = val;
      if (val) {
        this.loadList();
      }
    }
  },
  created() {},
  methods: {
    query() {
      this.loadList();
    },
    reset() {
      this.form = {};
      this.loadList();
    },
    loadList() {
      const params = {
        searchWord: this.form.searchWord,
        startSearchTime: this.form.startSearchTime ? moment(this.form.startSearchTime).format('YYYY-MM-DD') : undefined,
        endSearchTime: this.form.endSearchTime ? moment(this.form.endSearchTime).format('YYYY-MM-DD') : undefined,
        sortType: this.form.sortType,
        start: this.page.currentPage,
        length: this.page.pageSize
      };
      this.$http.get('/question/log/getAllQuestionSearchLog.do', { params: params }, {
        headers: {
          // 'Content-Type': 'application/json'
        }
      }).then(res => {
        if (res.ok) {
          this.tableData = res.body.data;
          this.page.total = res.body.recordsTotal;
        }
      });
    },
    open() {
      // this.$emit('dialogVisible', false);
    },
    close() {
      this.$emit('dialogVisible', false);
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      console.log(`每页 ${val} 条`);
      this.loadList();
    },
    handleCurrentChange(val) {
      this.page.currentPage = val;
      console.log(`当前页: ${val}`);
      this.loadList();
    },
    exportList() {
      if (this.form.startSearchTime !== '') {
        this.form.startSearchTime = moment(this.form.startSearchTime).format('YYYY-MM-DD');
      } else {
        this.form.startSearchTime = '';
      }
      if (this.form.endSearchTime !== '') {
        this.form.endSearchTime = moment(this.form.endSearchTime).format('YYYY-MM-DD');
      } else {
        this.form.endSearchTime = '';
      }
      var exportUrl = downUri + '/question/log/exportQuestionSearchLog.do?searchWord=' + this.form.searchWord + '&startSearchTime=' + this.form.startSearchTime + '&endSearchTime=' + this.form.endSearchTime + '&sortType=' + this.form.sortType;
      // 导出文件
      window.location.href = exportUrl;
    },
    handleClose() {
      this.rankingShow = false;
    },
    handleEdit(index, row) {
      this.showList = row;
      this.rankingShow = true;
    },
    ok(formName) {
      const params = {
        id: this.showList.id,
        number: this.ruleForm.number
      };
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$http.post('/question/log/updateManualSort.do', params).then(res => {
            if (res.ok) {
              this.$message.success('修改成功');
              this.loadList();
            } else {
              this.$message.error('修改失败');
            }
          });
          this.ruleForm = {};
          this.rankingShow = false;
        }
      });
    }
  }
};
</script>

<style lang = "scss" scoped>
.center{
  i{
    color:red
  }
  .center_txt{
    float: right;
    height: 40px;
    line-height: 40px;
    /* text-align: right; */
  }
  .center_content{
    float: left;
    height: 40px;
    line-height: 40px;
    /* text-align:left ; */
  }
}
  .pageClass{
  float: right;
  margin-top: -15px;
}
</style>
