<template>
  <common-dialog
    class="common-dialog"
    width="1000px"
    title="分配老师"
    :visible.sync="show"
    :paperList="paperList"
    @open="open"
    @close="close"
  >
    <div class="dialog-main"></div>
    <!-- 表单 -->
    <el-form
      ref="searchForm"
      size="mini"
      label-width="120px"
      class="yz-search-form"
      :model="form"
      @submit.native.prevent="search"
    >
      <el-form-item label="用户姓名" prop="realName">
        <el-input v-model="form.realName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="登录名" prop="userName">
        <el-input v-model="form.userName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="是否职员" prop="isStaff">
        <el-select v-model="form.isStaff" placeholder="请选择">
          <el-option label="请选择" value="" />
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <div class="search-reset-box margin-right">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
      </div>
    </el-form>
    <!-- 按钮区 -->
    <div class="yz-table-btnbox"></div>
    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
    >
      <el-table-column
        prop="stdName"
        align="center"
        type="selection"
        width="50"
      />
      <el-table-column prop="realName" label="用户姓名" align="center" />
      <el-table-column prop="userName" label="登录名" align="center" />
      <el-table-column prop="titleLists" label="角色" align="center" />
      <el-table-column prop="isStaff" label="是否职员" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isStaff === '1'" class="tag">是</el-tag>
          <el-tag v-if="scope.row.isStaff === '0'" class="tag">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template v-slot="scope">
          <el-popover
            :ref="`popover-audit-${scope.row.id}`"
            popper-class="popper-class"
            placement="top"
            width="240"
          >
            <div>
              <p>确认分配？</p>
              <div style="text-align: right; margin: 0">
                <el-button
                  type="text"
                  size="mini"
                  @click="cancel()"
                >取消</el-button>
                <el-button
                  type="primary"
                  size="mini"
                  @click="confirm(scope.row)"
                >确定</el-button>
              </div>
            </div>
            <el-button
              slot="reference"
              size="small"
              type="primary"
            >分配</el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination margin-bottom">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTeacherList"
      />
    </div>
  </common-dialog>
</template>

<script>
const form = {
  realName: '', // 用户姓名
  userName: '', // 登录名
  isBlock: '', // 是否启用
  roleId: '', // 角色ID
  isStaff: '' // 是否职员
};
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    paperList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      show: false,
      form: form,
      tableLoading: false,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    cancel() {
      document.body.click();
    },
    confirm(row) {
      document.body.click();
      this.studentAssgin(row);
    },
    open() {
      this.getTeacherList();
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    handleQUeryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    getTeacherList() {
      this.tableLoading = true;
      const params = this.handleQUeryParams();
      this.$post('getUserEmpList', params).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTeacherList();
      }
    },
    studentAssgin(row) {
      this.tableLoading = true;
      const datas = new FormData();
      datas.distributionTeacher = row.userId;
      var gpIds = '';
      for (let i = 0; i < this.paperList.length; i++) {
        if (i === 0) {
          gpIds = this.paperList[i];
        } else {
          gpIds = gpIds + ',' + this.paperList[i];
        }
      }
      datas.gpIds = gpIds;
      this.$post('assginTeacher', datas).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.show = false;
          this.$parent.getTableList();
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.margin-right {
  margin-right: 10px;
}
.margin-bottom {
  margin-bottom: 10px;
}
</style>
