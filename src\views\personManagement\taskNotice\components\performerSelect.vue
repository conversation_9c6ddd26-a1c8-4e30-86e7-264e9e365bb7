<template>
  <el-dialog
    width="835px"
    top="50vh"
    :visible.sync='show'
    :destroy-on-close="true"
    :show-close="false"
    @close='close'
    @open="open"
  >
    <div class="content-box">
      <div class="select-box">
        <el-input v-model="searchValue" placeholder="搜索" class="search-box" />
        <div class="check-box">
          <el-checkbox-group v-model="checkEmpList" change="change">
            <el-checkbox v-for="item in empList" :key="item.value" :label="item.value" :value="item.value" @change="checked=>checkboxClick(checked, item.value)">{{ item.key }}</el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="horizontal-line"></div>
        <div class="tree-box">
          <el-tree
            ref="tree"
            :data="performerData"
            show-checkbox
            node-key="id"
            :props="defaultProps"
            :filter-node-method="filterNode"
            @check-change="handleCheckChange"
          />
        </div>
      </div>
      <div class="vertical-line"></div>
      <div class="list-box">
        <div class="list-title">
          已选择了{{ performerSlect.length }}个联系人
        </div>
        <div class="list-content">
          <div v-for="(item,index) in performerSlect" :key=index class="list-item">
            <div class="item-text">{{ item.selectPerson }}</div>
            <img src="../../../../assets/imgs/close-icon.png" class="item-close" alt="" @click="removePerformer(index)" />
          </div>
        </div>
        <div class="list-options">
          <el-button class="options-btn" @click="cancelSelect">取消</el-button>
          <el-button class="options-btn" type="primary" @click="confirmSelect">确认</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedlist: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      searchValue: '',
      performerData: [],
      performerSlect: [],
      checkEmpList: [],
      empList: [],
      defaultProps: {
        children: 'children',
        label: 'title'
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    searchValue(val) {
      this.$refs.tree.filter(val);
    },
    performerSlect(val) {
      if (val.length === 0) {
        this.checkEmpList = [];
      }
    }
  },
  methods: {
    getTreeData() {
      this.$post('getPerformerTree').then((res) => {
        if (res.code === '00') {
          this.performerData = res.body;
          this.checkedPerformer();
        }
      });
    },
    open() {
      this.performerSlect = this.selectedlist;
      this.getEmpList();
      if (!this.$refs.tree) {
        this.getTreeData();
      } else {
        this.checkedPerformer();
      }
    },
    close() {
      this.checkEmpList = [];
      this.searchValue = '';
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // flag 点击的勾选的当前状态
    checkboxClick(flag, value) {
      this.checkedEmpPerformer(flag, value);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.title.indexOf(value) !== -1;
    },
    getEmpList() {
      this.$post('getDictInfoList', { name: 'perfEmpType' }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.empList = body;
        }
      });
    },
    // 根据分类勾选
    checkedEmpPerformer(flag, value) {
      if (flag) {
        this.checkEmpList.forEach(item => {
          this.getPerformerId(item, flag);
        });
      } else {
        this.getPerformerId(value, flag);
      }
    },
    // 遍及树结构获取id
    getPerformerId(empType, flag) {
      this.performerData.forEach(item => {
        this.ergodicTree(item.children, empType, flag);
        if (flag) {
          this.checkedPerformer();
        }
      });
    },
    // 遍历树结构
    ergodicTree(tree, empType, flag) {
      for (var i in tree) {
        if (tree[i].empType === empType) {
          if (flag) {
            if (this.performerSlect.indexOf(tree[i]) === -1) { this.performerSlect.push(tree[i]); }
          } else {
            this.performerSlect.map((item, index) => {
              if (item.id === tree[i].id) {
                this.performerSlect.splice(index, 1);
                this.$refs.tree.setChecked(tree[i].id, false);
              }
            });
          }
        }
        if (tree[i].children) {
          this.ergodicTree(tree[i].children, empType, flag);
        }
      }
    },
    checkedPerformer() {
      this.performerSlect.forEach(value => {
        this.$nextTick(() => {
          this.$refs.tree.setChecked(value.id, true, false);
        });
      });
    },
    removePerformer(index) {
      this.$refs.tree.setChecked(this.performerSlect[index].id, false);
      this.handleCheckChange();
    },
    handleCheckChange(data, checked, indeterminate) {
      this.performerSlect = this.$refs.tree.getCheckedNodes(true, true);
      this.performerSlect.forEach((item, index) => {
        item.parentInfo = [];
        this.getParentInfos(item.id, index);
        item.selectPerson = JSON.parse(JSON.stringify(item.title));
        // 给执行者加上公司部门后缀
        for (let i = 0; i < item.parentInfo.length - 1; i++) {
          item.selectPerson = item.selectPerson + '+' + item.parentInfo[i].title;
        }
      });
    },
    // 使用递归获取所有已勾选的子叶的所有父节点信息
    getParentInfos(id, index) {
      const parentInfoInfos = this.$refs.tree.getNode(id).parent.data;
      if (parentInfoInfos.id) {
        this.performerSlect[index].parentInfo.push(parentInfoInfos);
        this.getParentInfos(parentInfoInfos.id, index);
      }
    },
    cancelSelect() {
      this.performerSlect = [];
      this.close();
    },
    confirmSelect() {
      this.$emit('handleCheckPerformer', this.performerSlect);
      this.close();
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  border-radius: 10px;
  .el-dialog__header {
    display: none;
  }
  .el-checkbox-group {
    .el-checkbox {
      margin-right: 15px;
      .el-checkbox__label {
      padding-left: 5px;
    }
    }

  }

  .el-dialog__body {
    padding: 0;
    .content-box {
      display: flex;
      height: 537px;

      .select-box {
        width: 451px;
        padding: 20px;
        .search-box {
          width: 268px;
        }
        .tree-box {
          // margin-top: 20px;
          height: 402px;
          display: block;
          overflow-y: auto;
        }
      }
      .check-box {
        margin: 15px 0;
      }
      .horizontal-line {
        margin-bottom: 15px;
        height: 1px;
        width: 100%;
        background: #D8D8D8;
      }
      .vertical-line {
        width: 1px;
        height: 100%;
        background: #D8D8D8;
      }
      .list-box {
        padding: 20px;
        width: 383px;
        .list-title {
          margin-top: 5px;
          margin-bottom: 16px;
          font-size: 14px;
          color: #606266;
          line-height: 20px;
        }
        .list-content {
          // height: 456px;
          height: 406px;
          font-size: 14px;
          color: #606266;
          line-height: 20px;
          display: block;
          overflow-y: auto;
          .list-item{
            margin-bottom: 16px;
            display: flex;
            cursor: pointer;
            .item-text {
              width: 327px;
              white-space: nowrap;
              text-overflow: ellipsis;
              -o-text-overflow: ellipsis;
              overflow: hidden;
            }
            .item-close {
              margin-top: 2px;
              width: 16px;
              height: 16px;
            }
          }
          .list-item:hover {
            background: #f1f1f1;
          }
        }
        .list-options {
          text-align: right;
          margin-top: 20px;
          .options-btn {
            width: 82px;
            // height: 30px;
            // line-height: 30px;
            text-align: center;
          }
        }

      }
    }

  }
}
</style>
