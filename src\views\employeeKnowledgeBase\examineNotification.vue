<template>
  <div>
    <el-dialog
      title="提示"
      :visible.sync="examineShow"
      width="25%"
      :before-close="handleClose"
    >
      <el-form ref="form" :model="form" label-width="100px" :rules="rules">
        <el-form-item label="审核状态：">
          <el-select v-model="form.auditStatus" placeholder="请选择" @change="changeStatus">
            <el-option label="通过" value='1' />
            <el-option label="不通过" value='2' />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.auditStatus == 2" label="理由：" prop="remark">
          <el-input v-model="form.remark" type="textarea" maxlength="100" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel()">取 消</el-button>
        <el-button type="primary" @click="ok()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    examineShow: {
      type: Boolean,
      default: false
    },
    examineInfoList: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      form: {
        auditStatus: ''
      },
      questionId: [],
      show: false,
      rules: {
        remark: [
          { required: true, message: '请输入理由' }
        ]
      }
    };
  },
  watch: {
    examineInfoList(newval, oldval) {
      // const arr = newval.questionId;
      const ids = [];
      newval.forEach(item => {
        ids.push(item.id);
      });
      this.questionId = ids;
      console.log(this.questionId, 'id');
      // this.questionId.push(arr);
      // console.log(this.questionId, 'this.questionId');
    }
  },
  created() {
  },
  methods: {
    changeStatus(val) {
      if (val !== '1') {
        this.show = true;
      } else {
        this.show = false;
      }
    },
    handleClose() {
      this.form = {};
      this.$emit('show', false);
    },
    ok() {
      if (this.form.auditStatus === '' || this.form.auditStatus === undefined) {
        return;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = {
            questionIds: this.questionId,
            remark: this.form.remark,
            auditStatus: Number(this.form.auditStatus)
          };
          this.$http.post('/question/updateAuditBatchBdQuestion.do', params, { json: true }).then(res => {
            if (res.ok) {
              this.$parent.loadList();
              this.$message.success('审批成功');
            }
          });
          this.form = {};
          this.$emit('show', false);
        }
      });
    },
    cancel() {
      this.form = {};
      this.$emit('show', false);
    }
  }
};
</script>

<style>

</style>
