@import "./variables.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./table.scss";
@import "./icon.scss";
@import "./steps.scss";

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
}

label {
  // font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a {
  color: #1890ff;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  text-decoration: none;
}

div:focus {
  outline: none;
}
ul,
ol,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}

p {
  padding: 0;
  margin: 0;
}

.tac {
  text-align: center;
}

.tar {
  text-align: right;
}

.p20 {
  padding: 20px;
}

.mt10 {
  margin-top: 10px;
}

.mt20 {
  margin-top: 20px;
}

.ml10 {
  margin-left: 10px;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.break-all {
  word-break: break-all;
}

// main-container global css
.app-container {
  padding: 20px;
}

@font-face {
  font-family: "ruizi";
  src: url("../assets//font/ruizi.ttf") format("truetype");
}
.yz-base-container {
  background: #fff;
  border-radius: 4px;
  padding: 10px 10px 30px 10px;
  margin: 6px;
  box-shadow: 0 0 20px rgba($color: #000000, $alpha: 0.1);
}

.dialog-main {
  padding: 20px;
}

/* 滚动条样式 - @cell-x/el-table-sticky插件 */
.el-table {
  --scrollbar-height: 10px;
  .gm-scrollbar {
    border-radius: 5px;
  }
}
