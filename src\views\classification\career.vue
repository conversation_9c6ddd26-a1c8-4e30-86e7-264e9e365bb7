<template>
  <div class="yz-base-container">
    <div style="margin: 15px 0 15px;">
      <el-input v-model.trim="input" clearable placeholder="请输入内容" class="input-with-select" @change="inputChange">
        <el-button slot="append" type="primary" @click="query">查询</el-button>
      </el-input>
    </div>
    <div v-if="!show" class="btn_txt">
      <el-button v-for="item in btnTxt" :key="item.parentId" @click="btnClick(item.parentId,item.name )"><div v-html=" item.joinStr.includes(input)?item.joinStr.replace(input,`<span class='lightheight'>${input}</span>`):item.joinStr "></div></el-button>
    </div>
    <div v-if="show">
      <el-tree ref="tree" :data="treeData" :props="defaultProps" :check-strictly="true" show-checkbox node-key="id" @check-change="treeCheckedChange" />
    </div>
    <div class="txt">{{ !show&&btnTxt.length==0?'暂无匹配职位？':'没有合适职位？' }}<a @click="txtInput">请点击此处进行输入</a></div>
    <el-dialog title="请输入职位" :visible.sync="dialogVisible" width="100%" :fullscreen="true" :before-close="handleClose">
      <el-form ref="form" :rules="rules" :model="form" label-width="80px">
        <el-form-item label="职位 :" prop="name">
          <el-input v-model.trim="form.name" clearable maxlength="30" />
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        name: ''
      },
      dialogVisible: false,
      input: '',
      show: true,
      btnShow: false,
      btnTxt: [],
      treeData: [],
      testdata: [],
      defaultProps: {
        children: 'childs',
        label: 'name'
      },
      rules: {
        name: [
          { required: true, message: '请输入职业', trigger: 'change' },
          { max: 30, message: '长度在30个字内', trigger: 'change' }
        ]
      },
      obj: {}
    };
  },
  watch: {
    'form.name'(newVal, oldVal) {
      console.log(newVal.length, String(newVal).length, oldVal);
      if (newVal.length <= 30) {
        const obj = {
          name: newVal
        };
        setTimeout(() => {
          localStorage.setItem('obj', JSON.stringify(obj));
        }, 500);
      }
    }
  },
  created() {
    this.list();
  },
  mounted() {
    window.globalFn = () => {
      this.btnClick();
    };
  },
  methods: {
    inputChange(e) {
      console.log(e, '00');
      if (!e) {
        this.show = true;
        this.list();
      }
    },
    treeCheckedChange(data, isChecked) {
      if (isChecked) {
        const checked = [data.id]; // id为tree的node-key属性
        this.$refs.tree.setCheckedKeys(checked);
      }
      if (isChecked) {
        const obj = {
          name: data.name,
          parentId: data.parentId
        };
        localStorage.setItem('obj', JSON.stringify(obj));
      }
    },
    list(name) {
      if (name) {
        const params = {
          name: name
        };
        this.$post('jobTreeList', params).then((res) => {
          if (res.ok) {
            this.btnTxt = res.body;
          }
        });
      } else {
        const params = {};
        this.$post('jobTreeList', params).then((res) => {
          if (res.ok) {
            this.treeData = res.body;
            this.setDisabled(this.treeData);
          }
        });
      }
    },
    setDisabled(treeData) {
      treeData.forEach(item => {
        if (item.childs && item.childs.length) {
          item.disabled = true;
          this.setDisabled(item.childs);
        }
      });
    },
    query() {
      if (this.input) {
        this.show = false;
      }
      this.list(this.input);
    },
    txtInput() {
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
      this.form.name = '';
      localStorage.clear();
    },
    btnClick(id, name) {
      const obj = {
        name: name,
        parentId: id
      };
      localStorage.setItem('obj', JSON.stringify(obj));
    }
  }
};
</script>

<style scoped lang="scss">
.title{
    font-size: 18px;
    font-weight: 700;
    margin: 10px 0 15px 15px;
}
.txt{
    margin: 15px 0 10px;
}
.btn_txt{
    display: flex;
    justify-self: start;
    flex-wrap: wrap;
    button{
        margin: 15px 10px 15px 0;
    }
}
::v-deep .el-tree-node__label {
    font-size: 16px;
}
::v-deep .el-checkbox__input.is-disabled{
    display: none;
  }
::v-deep .el-input-group__append, .el-input-group__prepend {
    color: #fff;
    background-color: #409eff;
    border-color: #409eff
    }
::v-deep .el-input-group__append:active, .el-input-group__prepend {
    color: #fff;
    background-color: #2c91fd;
    border-color: #2c91fd
}
::v-deep .lightheight{
    color: #409eff;
}
</style>
