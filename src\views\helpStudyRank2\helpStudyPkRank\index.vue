<template>
  <div v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" class="wrap">
    <div class="header">
      <div class="header-main">
        <div class="l">
          <div class="itemFirst" :class="{'itemFirstCheck' : itemStyleShow === 3}">
            <div @click="switchTab('zxWarRank', 3)">战区PK</div>
          </div>
          <div class="item" :class="{'itemStyle' : itemStyleShow === 2}">
            <div @click="switchTab('zxDepartRank', 2)">团队PK</div>
          </div>
          <div class="item" :class="{'itemStyle' : itemStyleShow === 1}">
            <div @click="switchTab('zxPersonRank', 1)">个人PK</div>
          </div>
        </div>
        <h1 class="center">{{ title }} </h1>
        <div class="r">
          <div v-show="showTimeInfo">
            <div class="refresh" @click="refresh">刷新</div>
            <p class="text">截止{{ nowDate }}数据</p>
          </div>
        </div>
      </div>
      <div class="header-bottom">
        <el-select v-model="actChild" class="selectChild" filterable placeholder="切换PK" @change="change">
          <el-option v-for="item in childData" :key="item.pkChildId" :label="item.pkChildName" :value="item.pkChildId" />
        </el-select>
        <div v-if="endTime" class="countdown-container">
          <countdown :endTime="endTime" />
        </div>
      </div>
    </div>

    <component :is="componentName" v-if="childData.length > 0" ref="child" :row="currentRow" @endTime="sethShowTimeInfo" />

    <div v-if="childData.length === 0 && !loading" class="data-null">
      <img src="../../../assets/imgs/helpStudyPkRank/data-null.png" alt="" />
      <p>暂无数据</p>
    </div>
  </div>
</template>

<script>
import zxPersonRank from './zxPersonRank'; // 个人
import zxDepartRank from './zxDepartRank'; // 团队
import zxWarRank from './zxWarRank'; // 战区
import countdown from './countdown'; // 倒计时
import { formatTimeStamp } from '@/utils';
export default {
  components: {
    zxPersonRank,
    zxDepartRank,
    zxWarRank,
    countdown
  },
  data() {
    return {
      test: false,
      componentName: 'zxWarRank',
      itemStyleShow: 3,
      count: 0,
      currentRow: null,
      selectChildType: '3',
      actChild: '',
      childData: [],
      title: '',
      nowDate: null,
      showTimeInfo: false,
      endTime: 0, // 结束时间戳
      loading: false
    };
  },
  mounted() {
    this.nowDate = formatTimeStamp(new Date().getTime(), 'MM月DD日  HH:mm:ss');
    this.getOptions();
  },
  methods: {
    sethShowTimeInfo(endTime) {
      this.endTime = endTime;
      const curTime = new Date().getTime();
      this.showTimeInfo = curTime <= endTime;

      if (this.showTimeInfo) {
        this.nowDate = formatTimeStamp(new Date().getTime(), 'MM月DD日  HH:mm:ss');
      }
    },
    setTitle(str) {
      this.title = str;
    },
    change(value) {
      let obj = {};
      obj = this.childData.find((item) => {
        return item.pkChildId === value;
      });
      this.setTitle(obj.pkChildName);
      this.currentRow = obj;
      this.endTime = 0;
      this.$nextTick(() => {
        this.$refs.child.refresh();
      });
    },
    switchTab(name, type) {
      this.endTime = 0;
      this.currentRow = null;
      this.itemStyleShow = type;
      this.selectChildType = type;
      this.componentName = name;
      this.childData = [];
      this.title = null;
      this.actChild = null;
      this.$nextTick(() => {
        this.getOptions();
      });
    },
    getOptions() {
      this.loading = true;
      const params = {
        pkChildType: this.selectChildType
      };
      this.$post('pkChildActivityList', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail && body?.data?.length) {
            this.childData = body.data;
            this.change(this.childData[0].pkChildId);
            this.actChild = this.childData[0].pkChildName;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    refresh() {
      this.nowDate = formatTimeStamp(new Date().getTime(), 'MM月DD日  HH:mm:ss');
      this.$refs.child.refresh();
    }
  }
};
</script>

<style lang = "scss" scoped>
.yz-base-container {
  min-height: 100%;
  background-color: #180E36;
  color: #fff;
}

.wrap {
  min-height: 100vh;
  background-color: #180E36;
  color: #fff;
}

.item{
  display: inline-block;
  width: 90px;
  height: 50px;
  line-height: 50px;
  transform: skewX(-10deg);
  margin-left: 8px;
  margin-top: 20px;
  background-color: #201e53;
  cursor: pointer;
  color:rgba(255, 255, 255, .5);
  border-radius: 2px;
  border: 1px solid #5F99FE66;

}
.item:nth-child(2){
  margin-left: -4px;
}
.itemFirst{
  display: inline-block;
  width: 110px;
  height: 70px;
  line-height: 70px;
  margin-left: 8px;
  text-align: center;
  cursor: pointer;
  color: #fff;
  color:rgba(255, 255, 255, .5);
  background: url('../../../assets/imgs/helpStudyPkRank/zx-bg-icon1.png') no-repeat;
  background-size: contain;
}
.itemFirstCheck{
  background: url('../../../assets/imgs/helpStudyPkRank/zx-bg-icon.png') no-repeat;
  background-size: contain;
  color:rgba(255, 255, 255, 1);
}
.selectChild{
  display: inline-block;
  vertical-align: bottom;
}
.el-select{
  width: 260px;
  margin: 20px 0 20px 16px;
}
::v-deep .el-input--suffix .el-input__inner{
  height: 30px;
  background-color: #180E36FF;
  color: #fff;
  border-color: rgba(255, 255, 255, 0.4);
}
::v-deep .el-input__icon{
  line-height: 25px;
}
::v-deep .el-select-dropdown__list{
  background-color: #180E36FF;
}

.item>div{
  text-align: center;
  transform: skewX(10deg)
}
.header{
  width: 100%;
  .header-main{
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    .center{
      flex:1;
      text-align: center;
      margin: 0;
      background: linear-gradient(180deg, #FAEEE3 0%, #D9B271 100%);
      background-clip: text;
      -webkit-background-clip: text;
      line-height: 80px;
      color: transparent;

      font-family: PingFangSC, PingFang SC !important;
      font-size: 36px;
      font-style: normal;
      font-weight: 400;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .r {
      width: 195px;
      padding-right: 20px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .refresh{
        width: 60px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
        background-image: radial-gradient(rgba(124, 203, 0, 0.3) 0%, rgba(124, 203, 0, 0.5) 100%);
        border-radius: 2px;
        border: 1px solid rgba(124, 203, 0, 0.6);
        font-size: 12px;
        margin-left: auto;
      }
      .text {
        margin-top: 10px;
        font-size: 14px;
      }
    }
  }
  .header-bottom {
    position: relative;
    width: 100%;
    .countdown-container{
      position: absolute;
      width: 70%;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
.itemStyle{
  background-image: radial-gradient(rgba(95, 153, 254, 0.4) 0%, rgba(73, 119, 254, 0.8) 100%);
  box-shadow: 0px 0px 10px 0px rgba(95, 153, 254, 0.6);
  color:rgba(255, 255, 255, 1);
}
.connect{
  width: 100%;
  height: 700px;
}
.myRank{
  width: 100%;
  height: 138px;
  background: linear-gradient(90deg, rgba(234, 105, 69, 0.7) 0%, rgba(255, 149, 69, 0.05) 100%);
  border-radius: 2px;
  margin-bottom: 20px;
  padding-top: 30px;
  padding-left: 20px;
  box-sizing: border-box;

  .l{
    width: 30%;
    float: left;

    .imgBox{
      float: left;
      width: 76px;
      height: 76px;
      background-color: pink;
      border-radius: 50%;
    }
    .myInfo{
      float: left;
      margin-left: 20px;
      p{
        font-size: 24px;
        color:rgba(255, 255, 255, .2);
        span{
          margin-left: 5px;
          font-size: 28px;
          color: #FFD387;
        }
      }
      div{
        margin-top: 10px;
        font-size: 18px;
        span{
          display: inline-block;
          width: 104px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          background: linear-gradient(90deg, #EA6945 0%, #FF9545 100%);
          box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
          border-radius: 12px;
          font-size: 14px;
        }
      }
    }
  }
  .total{
    width: 70%;
    height: 100px;
    float: right;
    display: flex;
    span{
      margin-right: 20px;
    }
    .total-l{
      div{
        display: inline-block;
        width: 145px;
        height: 80px;
        text-align: center;
        background:rgba(255, 255, 255, .05);
        margin-right: 5px;
        border-radius: 5px;
        padding-top: 20px;
        box-sizing: border-box;
        p{
          margin-bottom: 5px;

        }
        p:nth-child(2){
            font-size: 24px;
            font-weight: 600;
            color: #FFD387;
          }
      }
    }
    .total-r{
      flex: 1;
      p{
        width: 100%;
        height: 38px;
        line-height: 38px;
        margin-bottom: 5px;
        margin-left: 5px;
        background:rgba(255, 255, 255, .05);
        display: flex;
        span{
          margin: 0 2px;
          width: 25%;
          font-size: 15px;
          i{
            color: #FFD387;
            font-style:normal
          }
        }
      }
    }
  }
}
.data-null {
  width: 119px;
  height: 130px;
  text-align: center;
  position:absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
}
</style>

<style lang="scss">
.selectChild .el-input__inner {
  border: none;
  background-image: url('../../../assets/imgs/helpStudyPkRank/selectChild-bg.png');
  background-position: 0 0;
  background-size: 100% 100%;
}
.el-select-dropdown__empty {
  background-color: #ffffff;
}
</style>
