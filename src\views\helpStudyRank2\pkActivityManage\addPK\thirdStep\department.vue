<template>
  <!--  第三步：查看表格 -->
  <div v-show="activeInx==3&&addType==2" v-loading="tableLoading" class="main">
    <div class="header">
      <div class="l"></div>
      <div class="center">
        <h1>{{ pkChildName }}(截止{{ nowDate }}数据)</h1>
        <span>活动时间：{{ startTime | transformTimeStamp }} ~ {{ endTime | transformTimeStamp }}</span>
      </div>
      <div class="r"></div>
    </div>

    <!-- 表格 -->
    <depart-table
      :tableLoading="tableLoading"
      :tableData="tableData"
      :pkType="pkType"
      :pkRange="pkRange"
      :targetType="targetType"
      :targetConfigList="targetConfigList"
    />
  </div>
</template>

<script>
import departTable from '../../components/depart-table';
import { formatTimeStamp } from '@/utils';

export default {
  components: { departTable },
  props: {
    activeInx: { type: Number, default: 0 },
    addType: { type: String, default: '1' }, // 1:个人, 2:部门, 3:战队
    addParams: { type: Object, default: () => {} }
  },
  data() {
    return {
      tableLoading: false,
      nowDate: null,

      startTime: 0, // 开始时间戳
      endTime: 0, // 结束时间戳
      pkType: 0, // 1：助学积分 2：助学人数 3：活动人均
      pkRange: [], // pk范围，1：成教 2：国开 3：全日制 4：自考 5：研究生 6：职业教育 7：海外教育
      targetType: 0, // 1: 普通目标, 2: 奖励目标
      targetConfigList: [], // 普通目标的配置
      pkChildName: '', // 标题

      tableData: []
    };
  },
  inject: ['newRow'],
  computed: {
    // 编辑的数据
    row() {
      return this.newRow();
    },
    pkChildId() {
      const isEdit = this.row?.isEdit;
      return isEdit ? this.row?.pkChildId : this.addParams?.childActivityId;
    }
  },
  methods: {
    thirdInit() {
      this.nowDate = formatTimeStamp(new Date().getTime(), 'MM月DD日  HH:mm:ss');
      this.getChildActivityInfo(this.pkChildId);
      this.getPkStream(this.pkChildId);
    },
    // 获取活动信息
    getChildActivityInfo(pkChildId) {
      this.$post('getChildActivityInfo', { pkChildId }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.startTime = body.startTime;
          this.endTime = body.endTime;
          this.pkType = body.pkType;
          this.pkRange = body.pkRange.split(',');
          this.targetType = body.targetType;
          this.targetConfigList = body.targetConfigList;
          this.rewardTarget = body.rewardTarget;
          this.pkChildName = body.pkChildName;
        }
      });
    },
    // 获取pk流水（助学榜单用）
    getPkStream(pkChildId) {
      this.tableLoading = true;
      this.$post('getPkStream', { pkChildId }).then(async res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body;
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
</style>

