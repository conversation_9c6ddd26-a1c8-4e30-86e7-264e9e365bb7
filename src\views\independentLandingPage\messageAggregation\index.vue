<template>
  <div class="messageAggregation">
    <el-form
      class="messageAggregation-forms"
      :model="querys"
      size="mini"
      label-width="120px"
      @submit.native.prevent="searchBtn(1)"
    >
      <el-form-item label="发送时间起止：">
        <el-date-picker
          v-model="querys.activityTime"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="请选择开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="发送途径：">
        <el-select
          v-model="querys.type"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option label="APP" value="1" />
          <el-option label="公众号" value="2" />
          <el-option label="短信" value="3" />
          <el-option label="成教服务号" value="4" />
          <el-option label="国开服务号" value="5" />
          <el-option label="自考服务号" value="6" />
          <el-option label="研究生服务号" value="7" />
        </el-select>
      </el-form-item>
      <el-form-item label="消息标题：">
        <el-select
          v-model="querys.title"
          v-loadmore="loadmoreBtn"
          popper-class="popclass"
          placeholder="请选择"
          filterable
          clearable
          :remote="true"
          :loading="newLoading"
          :remote-method="newsRemote"
        >
          <el-option v-for="item in newsList" :key="item.id" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="消息内容：">
        <el-input v-model="querys.content" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="发送状态：">
        <el-select
          v-model="querys.status"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option label="成功" value="1" />
          <el-option label="失败" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <p style="color: red;text-align: right;">（温馨提示：可查近6个月内记录）</p>
      </el-form-item>
      <div class="forms-btn">
        <el-button type="primary" icon="el-icon-search" native-type="submit" size="mini" v-text="'搜索'" />
        <el-button icon="el-icon-refresh" size="mini" @click="searchBtn(0)" />
      </div>
    </el-form>
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      :data="tableData"
      header-cell-class-name="table-cell-header"
    >
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="notice" label="发送途径" width="150" align="center" />
      <el-table-column prop="receiver" label="接收人" width="150" align="center" />
      <el-table-column prop="sendTime" label="发送时间" width="180" align="center" />
      <el-table-column prop="title" label="消息标题" width="280" align="center" />
      <el-table-column prop="content" label="消息内容" align="center" />
      <el-table-column prop="statusText" label="发送状态" width="100" align="center" />
    </el-table>
    <div class="yz-table-pagination">
      <pagination :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.rows" @pagination="getTableList" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      querys: { learnId: '' },
      pagination: {
        total: 0,
        page: 1,
        rows: 10
      },
      tableLoading: false,
      tableData: [],
      newsNotice: {
        name: '',
        page: 1,
        total: 0
      },
      newLoading: false,
      newsList: []
    };
  },
  created() {
    this.querys.learnId = this.$route.query.learnId;
    this.getTableList();
    this.getNewsTitleList();
  },
  methods: {
    // 加载更多
    loadmoreBtn() {
      // 没有数据，不触发提示
      if (!this.newsList?.length) return;
      // 判断是否已经加载全部
      if (this.newsList.length > this.newsNotice.total) {
        this.$message({ message: '已经到底了', type: 'warning' });
        return;
      }
      this.newLoading = true;
      this.newsNotice.page += 1;
      this.getNewsTitleList();
    },
    // 输入title搜索查询
    newsRemote(vas) {
      const tomer = setTimeout(() => {
        this.newLoading = true;
        this.newsList = [];
        this.newsNotice.page = 1;
        this.newsNotice.name = vas;
        clearTimeout(tomer);
        this.getNewsTitleList();
      }, 500);
    },
    // 消息标题数据
    getNewsTitleList() {
      this.$http.post('/msg/integration/getMsgTitle',
        { ...this.newsNotice, learnId: this.querys.learnId },
        { json: true }).then(res => {
        const { code, body } = res;
        if (code === '00') {
          let data = body?.data || [];
          const { page } = this.newsNotice;
          data = data.map((item, inx) => {
            return {
              id: `${page}${inx}`,
              name: item || ''
            };
          });
          const newsData = this.newsList.concat(data);
          this.newsList = Array.from(new Set(newsData.map(JSON.stringify))).map(JSON.parse);
          this.newsNotice.total = Math.ceil(Number(body?.recordsTotal || 0) / 20);
        }
        this.newLoading = false;
      }).catch(err => {
        this.newLoading = false;
        console.log('消息标题数据-err', err);
      });
    },
    // 查询
    searchBtn(type) {
      if (type === 1) {
        this.pagination.page = 1;
        if (this.querys.activityTime) {
          this.querys['startTime'] = this.querys.activityTime[0];
          this.querys['endTime'] = this.querys.activityTime[1];
        } else {
          this.querys['startTime'] = '';
          this.querys['endTime'] = '';
        }
        this.getTableList();
      } else {
        // 重置
        this.querys = {};
        this.querys.learnId = this.$route.query.learnId;
      }
    },
    // 表格数据
    getTableList() {
      this.tableLoading = true;
      const params = { ...this.pagination, ...this.querys };
      this.$http.post('/msg/integration/list', params, { json: true }).then(res => {
        const { code, body } = res;
        if (code === '00') {
          const data = body?.data || [];
          data.map(item => {
            item.statusText = Number(item?.status || 0) ? '成功' : '失败';
            item.notice = '';
            // 1:APP消息,2:公众号【微信】消息,3:短信消息
            switch (Number(item?.type || 0)) {
              case 1:
                item.notice = 'APP';
                break;
              case 2:
                item.notice = '公众号';
                break;
              case 3:
                item.notice = '短信';
                break;
              case 4:
                item.notice = '成教服务号';
                break;
              case 5:
                item.notice = '国开服务号';
                break;
              case 6:
                item.notice = '自考服务号';
                break;
              case 7:
                item.notice = '研究生服务号';
                break;
              default:
                break;
            }
          });
          this.tableData = data;
          this.pagination.total = body?.recordsTotal || 0;
        }
        this.tableLoading = false;
      }).catch(err => {
        this.tableLoading = false;
        console.log('表格数据-err', err);
      });
    }
  }
};
</script>

<style lang="scss">
.messageAggregation {
  padding: 40px 30px;

  &-forms {
    margin-bottom: 30px;

    .el-form-item {
      width: 32%;
      display: inline-block;
    }
    .forms-btn {
      display: flex;
      justify-content: flex-end;
    }
  }

  .popclass {
    height: 200px;
    overflow: hidden;
    overflow-y: scroll;
  }
}
</style>
