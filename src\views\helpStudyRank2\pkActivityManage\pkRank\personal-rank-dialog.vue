<template>
  <common-dialog
    is-full
    :title="title"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div v-loading="tableLoading" class="main">
      <div class="header">
        <div class="l"></div>
        <div class="center">
          <h1>{{ pkChildName }}(截止{{ nowDate }}数据)</h1>
          <span>活动时间：{{ startTime | transformTimeStamp }} ~ {{ endTime | transformTimeStamp }}</span>
        </div>
        <div class="r">
          <div class='yz-table-btnbox'>
            <el-button type="success" size="small" plain @click="refresh">刷新</el-button>
            <el-button type="primary" size="small" @click="exportData">导出</el-button>
            <el-button type="danger" size="small" plain @click="editInfo">调整</el-button>
          </div>
        </div>
      </div>
      <div v-if="pkStream" class="total">
        <template v-if="pkRange.includes('1')">
          <span>今日成教：{{ pkStream.adultEduToday }}</span>
          <span>活动成教：{{ pkStream.adultEduTotal }}</span>
        </template>
        <template v-if="pkRange.includes('2')">
          <span>今日国开：{{ pkStream.nationalOpenToday }}</span>
          <span>活动国开：{{ pkStream.nationalOpenTotal }}</span>
        </template>
        <template v-if="pkRange.includes('3')">
          <span>今日全日制：{{ pkStream.fullTimeToday }}</span>
          <span>活动全日制：{{ pkStream.fullTimeTotal }}</span>
        </template>
        <template v-if="pkRange.includes('4')">
          <span>今日自考：{{ pkStream.selfStudyToday }}</span>
          <span>活动自考：{{ pkStream.selfStudyTotal }}</span>
        </template>
        <template v-if="pkRange.includes('5')">
          <span>今日研究生：{{ pkStream.postgraduateToday }}</span>
          <span>活动研究生：{{ pkStream.postgraduateTotal }}</span>
        </template>
        <template v-if="pkRange.includes('6')">
          <span>今日职业教育：{{ pkStream.vocationalEduToday }}</span>
          <span>活动职业教育：{{ pkStream.vocationalEduTotal }}</span>
        </template>
        <template v-if="pkRange.includes('7')">
          <span>今日海外教育：{{ pkStream.overseasEduToday }}</span>
          <span>活动海外教育：{{ pkStream.overseasEduTotal }}</span>
        </template>
        <span>活动合计：{{ pkStream.totalOrders }}</span>
      </div>

      <!-- 表格 -->
      <personal-table
        :tableLoading="tableLoading"
        :tableData="tableData"
        :pkType="pkType"
        :pkRange="pkRange"
        :targetType="targetType"
        :rewardTarget="rewardTarget"
        :targetConfigList="targetConfigList"
        :coefficientSwitch="coefficientSwitch"
      />

      <!-- 弹窗 - 编辑个人PK -->
      <firstAddPk :title="addPersonalTitle" addType="1" :visible.sync="addPersonalVisible" @refreshParent="refresh" />
    </div>
  </common-dialog>
</template>

<script>
import personalTable from '../components/personal-table';
import { exportExcel, formatTimeStamp } from '@/utils';

export default {
  components: {
    personalTable,
    firstAddPk: () => import('../addPK/firstStep')
  },
  props: {
    title: { type: String, default: '新增' },
    visible: { type: Boolean, default: false }
  },
  data() {
    return {
      show: false,
      addPersonalTitle: '', // 调整-编辑个人活动
      addPersonalVisible: false,
      tableLoading: false,
      nowDate: null,

      startTime: 0, // 开始时间戳
      endTime: 0, // 结束时间戳
      pkType: 0, // 1：助学积分 2：助学人数 3：活动人均
      pkRange: [], // pk范围，1：成教 2：国开 3：全日制 4：自考 5：研究生 6：职业教育 7：海外教育
      targetType: 0, // 1: 普通目标, 2: 奖励目标
      targetConfigList: [], // 普通目标的配置
      rewardTarget: 0, // 达成奖励目标
      pkChildName: '', // 标题
      coefficientSwitch: false, // 个人系数开关

      pkStream: null, // 总的pk流水
      tableData: []
    };
  },
  inject: ['newRow'],
  computed: {
    row() {
      return this.newRow();
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      this.init();
    },
    init() {
      this.nowDate = formatTimeStamp(new Date().getTime(), 'MM月DD日  HH:mm:ss');
      this.getChildActivityInfo(this.row.pkChildId);
      this.getTotalPkStream(this.row.pkChildId);
      this.getPkStream(this.row.pkChildId);
    },
    // 获取活动信息
    getChildActivityInfo(pkChildId) {
      this.$post('getChildActivityInfo', { pkChildId }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.pkType = body.pkType;
          this.startTime = body.startTime;
          this.endTime = body.endTime;
          this.pkRange = body.pkRange.split(',');
          this.targetType = body.targetType;
          this.targetConfigList = body.targetConfigList;
          this.pkChildName = body.pkChildName;
          this.coefficientSwitch = Boolean(body.coefficientSwitch);
          this.rewardTarget = body.rewardTarget;
        }
      });
    },
    // 获取总的pk流水
    getTotalPkStream(pkChildId) {
      this.$post('getTotalPkStream', { pkChildId }).then(async res => {
        const { fail, body } = res;
        if (!fail) {
          this.pkStream = body;
        }
      });
    },
    // 获取pk流水（助学榜单用）
    getPkStream(pkChildId) {
      this.tableLoading = true;
      this.$post('getPkStream', { pkChildId }).then(async res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body;
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('refresh-list');
      this.$emit('close');
    },
    // 编辑PK子活动
    editInfo() {
      this.addPersonalVisible = true;
      this.addPersonalTitle = '编辑个人PK';
    },
    // 刷新
    refresh() {
      this.init();
    },
    exportData() {
      const params = {
        pkChildActivityId: this.row.pkChildId
      };
      exportExcel('pkChildActivityPerformanceExportV2', params);
    }
  }
};
</script>

<style lang = "scss" scoped>
.main{
  text-align: center;
  padding: 10px;
}
.header{
  display: flex;
  .l,.r{
    flex-grow:1
  }
  .center{
    flex-grow:1;
    h1{
      margin-bottom: .2rem;
    }
    span{
      font-size: 16px;
    }
  }
  .r{
    margin-top: 20px;
    margin-right: 15px;
  }
}
.total{
  padding: 20px 10px;
  text-align: left;
  line-height: 24px;
  span{
    font-size: 18px;
    color: #fe80aa;
    margin-right: 30px;
  }
}
</style>

