<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    :title="`库存${stockType === 'increase' ? '增加': '减少'}`"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form ref="formModal" :model="form" :rules="rules">
        <el-form-item prop="stockAmount">
          <el-input-number
            v-model="form.stockAmount"
            style="width: 100%"
            :controls="false"
            :precision="0"
            :min="0"
            placeholder="请输入数字"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentRow: {
      type: Object,
      default: () => ({})
    },
    stockType: {
      type: String,
      default: 'increase' // increase: 增加，reduce: 减少
    }
  },
  data() {
    return {
      confirmLoading: false,
      show: false,
      form: {
        stockAmount: undefined
      },
      rules: {
        stockAmount: [
          { required: true, message: '请输入数字', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框
    open() {},
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        const { couponNum, couponReceiveNum } = this.currentRow;
        // 减少库存 && 总数量 - 已领取-减少库存量 < 0?
        if (this.stockType === 'reduce' && couponNum - couponReceiveNum - this.form.stockAmount < 0) {
          return this.$message.error('操作失败！库存不可扣减为负数');
        }

        this.confirmLoading = true;

        const form = {
          couponId: this.currentRow.couponId,
          num: this.form.stockAmount,
          operate: this.stockType == 'increase' ? 1 : 0
        };

        this.$post('updateZMproductCouponCount', form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.show = false;
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.common-dialog {
  ::v-deep .yz-common-dialog {
    margin-top: 30vh !important;
  }
}
</style>
