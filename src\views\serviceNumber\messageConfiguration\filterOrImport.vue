<template>
    <el-dialog :title="title" :visible.sync="show" width="86%" :before-close="handleClose">
        <el-form ref="form" :model="form" label-width="140px" inline>
            <el-form-item label="学业编码：">
                <el-input v-model="form.studentCode" placeholder="输入学业编码" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="远智编码：">
                <el-input v-model="form.yuanzhiCode" placeholder="输入远智编码" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="是否选中：">
                <el-select v-model="form.isSelected" placeholder="请选择" style="width: 200px;">
                    <el-option label="是" value="true"></el-option>
                    <el-option label="否" value="false"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="报考层次：">
                <el-select v-model="form.enrollmentLevel" placeholder="请选择" style="width: 200px;">
                    <el-option label="本科" value="undergraduate"></el-option>
                    <el-option label="专科" value="college"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="年级：">
                <el-select v-model="form.grade" placeholder="请选择" style="width: 200px;">
                    <el-option v-for="year in latestGrades" :key="year" :label="year" :value="year"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="院校：">
                <el-select v-model="form.institution" placeholder="请选择" style="width: 200px;">
                    <el-option label="北京大学" value="pku"></el-option>
                    <el-option label="清华大学" value="tsinghua"></el-option>
                </el-select>
            </el-form-item>
            <template v-if="formList">
                <el-form-item label="专业：">
                    <el-select v-model="form.major" placeholder="请选择" style="width: 200px;">
                        <el-option label="计算机科学与技术" value="cs"></el-option>
                        <el-option label="软件工程" value="se"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="考区：">
                    <el-select v-model="form.examArea" placeholder="请选择" style="width: 200px;">
                        <el-option label="北京" value="beijing"></el-option>
                        <el-option label="上海" value="shanghai"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="学员姓名：">
                    <el-input v-model="form.studentName" placeholder="输入学员姓名" style="width: 200px;"></el-input>
                </el-form-item>
                <el-form-item label="证件号码：">
                    <el-input v-model="form.idNumber" placeholder="输入证件号码" style="width: 200px;"></el-input>
                </el-form-item>
                <el-form-item label="手机号：">
                    <el-input v-model="form.phoneNumber" placeholder="输入手机号" style="width: 200px;"></el-input>
                </el-form-item>
                <el-form-item label="班主任：">
                    <el-input v-model="form.classTeacher" placeholder="输入班主任姓名" style="width: 200px;"></el-input>
                </el-form-item>
                <el-form-item label="招生类型：">
                    <el-select v-model="form.enrollmentType" placeholder="请选择" style="width: 200px;">
                        <el-option label="服务号" value="serviceNumber"></el-option>
                        <el-option label="官网" value="officialWebsite"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="优惠类型：">
                    <el-select v-model="form.discountType" placeholder="请选择" style="width: 200px;">
                        <el-option label="优惠A" value="discountA"></el-option>
                        <el-option label="优惠B" value="discountB"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="招生分校：">
                    <el-select v-model="form.enrollmentBranch" placeholder="请选择" style="width: 200px;">
                        <el-option label="分校A" value="branchA"></el-option>
                        <el-option label="分校B" value="branchB"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="招生部门：">
                    <el-input v-model="form.enrollmentDepartment" placeholder="输入招生部门" style="width: 200px;"></el-input>
                </el-form-item>
                <el-form-item label="招生老师：">
                    <el-input v-model="form.enrollmentTeacher" placeholder="输入招生老师姓名" style="width: 200px;"></el-input>
                </el-form-item>
                <el-form-item label="缴费时间起：">
                    <el-date-picker v-model="form.paymentStartDate" type="date" placeholder="选择日期"
                        style="width: 200px;"></el-date-picker>
                </el-form-item>
                <el-form-item label="缴费时间止：">
                    <el-date-picker v-model="form.paymentEndDate" type="date" placeholder="选择日期"
                        style="width: 200px;"></el-date-picker>
                </el-form-item>
                <el-form-item label="是否缴费：">
                    <el-select v-model="form.isPaid" placeholder="请选择" style="width: 200px;">
                        <el-option label="是" value="true"></el-option>
                        <el-option label="否" value="false"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="报读时间起：">
                    <el-date-picker v-model="form.enrollmentStartDate" type="date" placeholder="选择日期"
                        style="width: 200px;"></el-date-picker>
                </el-form-item>
                <el-form-item label="报读时间止：">
                    <el-date-picker v-model="form.enrollmentEndDate" type="date" placeholder="选择日期"
                        style="width: 200px;"></el-date-picker>
                </el-form-item>
            </template>
            <el-form-item>
                <el-button @click="formList = !formList">{{ formList ? '收起' : '展开' }}</el-button>
            </el-form-item>
            <el-form-item style="float: right;">
                <el-button type="primary" @click="search(1)">搜索</el-button>
                <el-button @click="search(0)">重置</el-button>
            </el-form-item>
            <div>
                <el-form-item label="学员状态：">
                    <el-checkbox-group v-model="form.type">
                        <el-checkbox :label="item" :name="item" v-for="(item, index) in studentStatusList"
                            :key="index">{{ item
                            }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>

            </div>
        </el-form>
        <div class="filter-btn">
            <div>正在勾选0条记录</div>
            <div>
                <span>共添加选中 2199 学员</span>
                <el-button type="primary" @click="addStudent">添加选中</el-button>
                <el-button type="primary" @click="addStudent">清空选中</el-button>
                <el-button type="primary" @click="addStudent">按搜索结果添加全部</el-button>
                <el-button type="primary" @click="addStudent">按搜索结果清空全部</el-button>
            </div>
        </div>
        <el-table :data="tableData" style="width: 100%" border stripe highlight-current-row
            @current-change="handleCurrentChange">
            <el-table-column prop="isTargetStudent" label="是否选中" align="center" width="120">
                <template slot-scope="scope">
                    <el-tag :type="scope.row.isTargetStudent ? 'success' : 'info'">{{ scope.row.isTargetStudent ? '是' :
                        '否'
                        }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="studentCode" label="学业编码" align="center" width="120"></el-table-column>
            <el-table-column prop="yuanzhiCode" label="远智编码" align="center" width="120"></el-table-column>
            <el-table-column prop="studentName" label="学员姓名" align="center" width="120"></el-table-column>
            <el-table-column prop="idNumber" label="身份证" align="center" width="120"></el-table-column>
            <el-table-column prop="grade" label="年级" align="center" width="120"></el-table-column>
            <el-table-column prop="enrollmentType" label="招生类型" align="center" width="120"></el-table-column>
            <el-table-column prop="institutionMajor" label="院校专业" align="center" width="180"></el-table-column>
            <el-table-column prop="studentStage" label="学员阶段" align="center" width="120"></el-table-column>
        </el-table>
    </el-dialog>
</template>
<script>
export default {
    props: {
        show: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            page:{
                total:0,
                pageSize: 10,
                pageNum: 1
            },
            studentStatusList: ['考前辅导', '考前确认', '入学考试', '已录取', '注册学员', '在读学员', '毕业学员', '留级学员', '退学学员', '其他', '待录取', '待注册', '休学学员', '服务终止'],
            formList: false,
            dialogVisible: true,
            form: {
                studentCode: '',
                yuanzhiCode: '',
                isGraduationEligible: '',
                isSelected: '',
                enrollmentLevel: '',
                grade: '',
                institution: '',
                major: '',
                examArea: '',
                studentName: '',
                idNumber: '',
                phoneNumber: '',
                classTeacher: '',
                enrollmentType: '',
                discountType: '',
                enrollmentBranch: '',
                enrollmentDepartment: '',
                enrollmentTeacher: '',
                isExternalSchool: '',
                isOverduePayment: '',
                hasExamScore: '',
                paymentType: '',
                paymentStartDate: '',
                paymentEndDate: '',
                materialStatus: '',
                attachmentStatus: '',
                hasCandidateNumber: '',
                isOnlineRegistration: '',
                isPaid: '',
                isConfirmed: '',
                enrollmentStartDate: '',
                enrollmentEndDate: '',
                selfExamPeriod: '',
                type: ''
            },
            latestGrades: ['2023', '2024', '2025'], // 假设这是最新的年级选项
            tableData: [
                {
                    isTargetStudent: true,
                    studentCode: '123456',
                    yuanzhiCode: '789012',
                    studentName: '张三',
                    idNumber: '123456789012345678',
                    grade: '2023',
                    enrollmentType: '服务号',
                    institutionMajor: '北京大学 计算机科学与技术',
                    studentStage: '报名'
                },
                {
                    isTargetStudent: false,
                    studentCode: '234567',
                    yuanzhiCode: '890123',
                    studentName: '李四',
                    idNumber: '234567890123456789',
                    grade: '2024',
                    enrollmentType: '官网',
                    institutionMajor: '清华大学 软件工程',
                    studentStage: '缴费'
                }
            ],
            currentRow: null
        };
    },
    methods: {
        // 其他方法...
        handleCurrentChange(val) {
            this.currentRow = val;
            console.log('当前选中的行数据:', val);
        },
        handleClose() {
            this.$emit('show', false);
        },
        addStudent(){}
    }
};
</script>
<style lang = "scss" scoped>
.filter-btn{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    span{
        margin-right: 10px;
    }
}
</style>