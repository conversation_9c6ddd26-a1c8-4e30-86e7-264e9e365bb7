<template>
  <div class="wrap">
    <div class="left">
      <p>我们的系统</p>
      <p>学员信息</p>
      <p v-if="studentRegisterNum">
        预报名号：{{ studentRegisterNum }}
      </p>
    </div>

    <div class="right">
      <iframe
        id="cgzcIframe"
        ref="cgzcIframe"
        name="cgzcIframe"
        class="iframe"
        :src="iframeSrc"
      ></iframe>
    </div>
  </div>
</template>

<script>
import iDCards from './idCards';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      iframeWin: null,
      iframeSrc: 'https://35-ijiaolian-mp.yzwill.cn/crtest/cgbm/cgzc.jsp',
      // iframeSrc: 'https://www.eeagd.edu.cn/crtest/cgbm/cgzc.jsp'
      // iframeSrc: '//test2.haha.cn/crtest/cgbm/cgzc.jsp',
      studentRegisterNum: null, // 学员预报名号
      num: 0
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.cgzcIframe.addEventListener('load', this.onIframeLoad);
    });
  },
  methods: {
    getIframeElem(selectors) {
      return this.iframeWin.document.documentElement.querySelector(selectors);
    },
    // 学员注册
    studentRegister() {
      this.num = localStorage.getItem('num') ? Number(localStorage.getItem('num')) + 1 : 0;

      const idCard = iDCards[this.num];
      this.iframeWin.document.querySelector('#xm').value = '温宝琳';
      // iframeWin.document.querySelector('#xbdm').checked = true;
      this.iframeWin.document.querySelector('#csrq').value = '1997-09-12';
      this.iframeWin.document.querySelector('#lxsj').value = `18888` + idCard.substring(12, idCard.length);
      this.iframeWin.document.querySelector('#zjdm').value = idCard;
      this.iframeWin.document.querySelector('#hkdm').value = '440112';
      this.iframeWin.document.querySelector('#zjlxdm').value = '1';
      this.iframeWin.document.querySelector('#pwd').value = 'Yz#78g83';
      this.iframeWin.document.querySelector('#qrpwd').value = 'Yz#78g83';
      this.iframeWin.document.querySelector('#mzdm').value = '01';
      this.iframeWin.document.querySelector('#yzm').value = '1234';
    },
    // 注册成功
    registerSuccess() {
      localStorage.setItem('num', JSON.stringify(this.num));
      this.studentRegisterNum = this.getRegisterNum();
      this.$message({
        message: '注册成功，您的预报名号为：' + this.studentRegisterNum,
        type: 'success'
      });
    },
    // 获取报名号
    getRegisterNum() {
      return this.iframeWin.document.documentElement.querySelector('#xm').value;
    },
    // 填写学员基本信息
    fillStudentBaseInfo() {
      // 居住证信息
      this.getIframeElem('#jzzszd').value = '4401';
      // 政治面貌
      this.getIframeElem('#zzmmdm').value = '01';
      // 考试语种
      this.getIframeElem('#dybmks_wyyzdm').value = '1';
      // 考试类型
      this.getIframeElem('#kslxdm').value = '0';
      // 照顾加分
      this.getIframeElem('#zgjfbj').checked = true;
      // 考生类别
      this.getIframeElem('#kslbdm').value = '1';
      this.getIframeElem('#kslbdm').onchange();
      // 报考科类
      this.getIframeElem('#jhlbdm').value = '1';
      // 考试科目组
      this.getIframeElem('#kmzdm').value = '103';
      // 考试县区
      this.getIframeElem('#xqdm').value = '0112';
      this.getIframeElem('#xqdm').onchange();
      // 报名点
      this.getIframeElem('#bmddm').value = '011202';
      // 考前学历
      this.getIframeElem('#kqxl').value = '6';
      // 职业
      this.getIframeElem('#zydm').value = '01';
      // 毕业学校
      this.getIframeElem('#byxx').value = '广州华南师范大学';
      // 毕业年月
      this.getIframeElem('#byrq').value = '2015-06';
      // 毕业专业
      this.getIframeElem('#byzy').value = '计算机网络';
      // 毕业证书号
      this.getIframeElem('#byzshm').value = '11111111111111';
      // 邮政编码
      this.getIframeElem('#yzbm').value = '510000';
      // 固定电话
      this.getIframeElem('#lxdh').value = '18888888888';

      setTimeout(() => {
        // 通讯地址--省
        this.getIframeElem('#sf').value = '44';
        this.getIframeElem('#sf').onchange();
        // 通讯地址--市
        this.getIframeElem('#ds').value = '4401';
        this.getIframeElem('#ds').onchange();
        // 通讯地址--县
        this.getIframeElem('#xq').value = '440112';
      }, 200);

      // 通讯地址--详细地址
      this.getIframeElem('#txdz').value = '广州市黄埔奥园';

      // 专升本--报考院校
      this.getIframeElem('#zsbpc1bkyx1').value = '10559';
      // 专升本--报考专业1
      this.getIframeElem('#zsbpc1bkyx1zy1').value = '2003';
      // 专升本--报考专业2
      this.getIframeElem('#zsbpc1bkyx1zy2').value = '2009';

      // 高起专--报考院校
      this.getIframeElem('#gqgpc4bkyx1').value = '';
      // 高起专--报考专业1
      this.getIframeElem('#gqgpc4bkyx1zy1').value = '';
      // 高起专--报考专业2
      this.getIframeElem('#gqgpc4bkyx1zy2').value = '';
      // 验证码
      this.getIframeElem('#yzm').value = '123456';
    },
    onIframeLoad() {
      this.iframeWin = this.$refs.cgzcIframe.contentWindow;
      const pathname = this.iframeWin.location.pathname;
      console.log('当前页面路径：' + pathname);

      if (pathname.indexOf('/cgbm/cgzc.jsp') !== -1) {
        console.log('注册表单-加载完成');
        this.studentRegister();
        return;
      }

      if (pathname.indexOf('/cgbm/cgtxzl.jsp') !== -1) {
        console.log('预报名后填写具体信息-加载完成');
        this.registerSuccess();
        console.log('填充学员信息');
        this.fillStudentBaseInfo();
      }
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
.wrap {
  display: flex;
}

.left,.right {
  width: 50%;
  min-height: 100vh;
}

.left {
  text-align: center;
  font-size: 20px;
  padding: 100px 0;
}

.right {
  border-left: 1px solid #ccc;

  .iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}

</style>
