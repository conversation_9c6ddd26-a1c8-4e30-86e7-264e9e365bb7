<template>

  <div>
    <common-dialog
      class="common-dialog"
      width="1000px"
      title="学员详情页"
      :visible.sync="show"
      :checkStudentList="checkStudentList"
      @open="open"
      @close="close"
    >
      <div class="dialog-main"></div>

      <!-- 表格 -->
      <el-table
        ref="table"
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%;"
        header-cell-class-name="table-cell-header"
        :data="tableData"
      >
        <!-- <el-table-column
          prop="stdName"
          align="center"
          type="selection"
          width="50"
        /> -->
        <el-table-column
          label="是否目标学员"
          prop="taskId"
          align="center"
        >
          <template>
            <span>是</span>
          </template>
        </el-table-column>
        <el-table-column prop="stdName" label="学员姓名" align="center" />
        <el-table-column prop="learnId" label="学业编码" align="center" />
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="grade" label="年级" align="center" />
        <el-table-column prop="recruitType" label="招生类型" align="center" />
        <el-table-column
          label="院校专业"
          width="220"
          align="center"
        >
          <template v-slot="scope">
            <div>
              {{ scope.row.unvsName }};{{ scope.row.pfsnName }}[{{ scope.row.pfsnLevel }}]({{ scope.row.grade }}级)
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="stdStage" label="学员阶段" align="center" />
        <el-table-column prop="empName" label="跟进老师" align="center" />
        <el-table-column prop="empStatus" label="在职情况" align="center" />
        <el-table-column prop="empDeptName" label="所属跟进部门" align="center" />
        <el-table-column prop="tutorEmpName" label="辅导员" align="center" />
        <el-table-column prop="coordinationEmpName" label="协作人" align="center" />
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination" style="margin-bottom: 10px">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableData"
        />

      </div>
    </common-dialog>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    checkStudentList: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      form: {},
      tableLoading: false,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      this.form = this.checkStudentList;
      this.getTableData();
    },
    getCampusList() {
      this.$post('getCampusList').then((res) => {
        if (res.code === '00') {
          this.campusList = res.body;
        }
      });
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
      // this.$refs['searchForm'].resetFields();
      // this.$emit('getPaperDatas');
    },
    handleQUeryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    getTableData() {
      this.tableLoading = true;
      const params = this.handleQUeryParams();
      this.$post('getRemindStudentList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableData();
      }
    }
  }
};
</script>
