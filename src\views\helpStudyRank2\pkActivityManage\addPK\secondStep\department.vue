<template>
  <!-- 第二步：添加编辑表格 && 第三步：查看表格 -->
  <div v-show="activeInx==2&&addType==2">
    <!-- 按钮区（第二步） -->
    <div class='yz-table-btnbox'>
      <span>团队pk人员名单：</span>
      <el-button type="primary" size="small" @click="exportDepPK">导出PK团队名单</el-button>
      <el-button type="primary" size="small" @click="importData">导入PK团队名单</el-button>
    </div>

    <!-- 表格 -->
    <div v-loading="tableLoading" class="table-box">
      <div class="table">
        <div class="head">
          <div v-width="180">团队名称</div>
          <div v-width="180">队长</div>
          <div v-width="180">分校长</div>
          <div v-width="180">所属部门</div>
          <div v-width="180">参与人数</div>
          <div v-width="180">计算人力</div>
          <div v-width="80">操作</div>
        </div>
        <div class="body">
          <div v-for="(item, index) in tableData" :key="'info1-'+index" class="tr">
            <div v-width="180" class="td cell" label="团队名称">
              <div class="center">
                <p>{{ item.teamName }}</p>
                <el-link type="error" @click="memberDetails({zdys:1,...item})">队员管理</el-link>
              </div>
            </div>
            <div v-width="180" label="队长" class="td">
              <div class="center">{{ item.empName }}</div>
            </div>
            <div v-width="180" label="分校长">
              <div v-for="force in item.pkTeamDetailsVOS" :key="force.empName + generateKey()" class="td">
                <div class="center">{{ force.empName }}</div>
              </div>
            </div>
            <div v-width="180" label="所属部门">
              <div v-for="force in item.pkTeamDetailsVOS" :key="force.teamName" class="td">
                <div class="center">{{ force.teamName }}</div>
              </div>
            </div>
            <div v-width="180" label="参与人数">
              <div v-for="force in item.pkTeamDetailsVOS" :key="force.totalNumber + generateKey()" class="td">
                <div class="center">
                  <el-link type="success" @click="memberDetails({zdys:0,...force})">{{ force.totalNumber || 0 }} </el-link>
                </div>
              </div>
            </div>
            <div v-width="180" label="计算人力">
              <div v-for="force in item.pkTeamDetailsVOS" :key="force.manPower + generateKey()" class="td">
                <div class="center">{{ force.manPower }}</div>
              </div>
            </div>
            <div v-width="80" label="操作">
              <div v-for="force in item.pkTeamDetailsVOS" :key="force.empName + generateKey()" class="td">
                <i class="center el-icon-delete delete-icon" @click.stop="deleteBtn(item.pkTeamDetailsVOS,force)"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 弹窗 - 业绩调整 -->
      <import-achieve-dialog type='2' :params="addParams" :title="reduceDialogTitle" :visible.sync="raVisible" @success="getTableList" />
      <!-- 弹窗 - 战队人员选择 -->
      <select-member :pkChildId="pkChildId" :row="currentRow" :title="selectMemberTitle" :visible.sync="smVisible" @close="getTableList" @refresh-list="getTableList" />
    </div>
  </div>
</template>

<script>
import importAchieveDialog from '../../import-achieve-dialog';
import selectMember from '../select-member';
import { httpPostDownFile } from '@/utils/downExcelFile';

export default {
  components: { importAchieveDialog, selectMember },
  props: {
    activeInx: { type: Number, default: 0 },
    addType: { type: String, default: '1' }, // 1:个人, 2:部门, 3:战队
    addParams: { type: Object, default: () => {} }
  },
  data() {
    return {
      currentRow: {},
      tableLoading: false,
      raVisible: false,
      smVisible: false,
      importWarShow: false,
      reduceDialogTitle: '',
      selectMemberTitle: '',
      teamId: '',
      pkTeamList: [],
      pkRangeList: [],
      tableData: [],
      allData: [],
      pkjxAllList: {},
      pkjxTodayList: {},
      pkjxTempList: {},
      pkChildAct: {},
      moreAim: [],
      groupId: '',
      spanArr: []
    };
  },
  inject: ['newRow'],
  computed: {
    row() {
      return this.newRow();
    },
    pkChildId() {
      const isEdit = this.row?.isEdit;
      return isEdit ? this.row?.pkChildId : this.addParams?.childActivityId;
    }
  },
  methods: {
    generateKey() {
      return Math.random();// uuid();
    },
    // 初始化编辑
    secondInit() {
      this.getTableList();
    },
    // 导出PK团队名单
    exportDepPK() {
      httpPostDownFile({
        url: '/pkTeam/exportPkUserList',
        params: { pkChildId: this.pkChildId },
        name: '导出PK团队名单',
        config: { json: false }
      });
    },
    importData() {
      this.raVisible = true;
      this.reduceDialogTitle = '导入团队人员名单';
    },
    delectTeacher(row) {
      this.teamId = row.teamId;
      if (this.tableData.length === 1) {
        this.$message({ showClose: true, message: '最后一个团队人员不能删除哦!', type: 'warning' });
        return;
      }
      this.$confirm('您确定从该团队pk中移除该团队吗？移除后，该团队将不统计进团队pk排中', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除个人
        const params = {
          teamId: row.teamId,
          teamType: '2',
          pkChildId: this.pkChildId
        };

        this.$post('deleteDepartToDepart', params).then(res => {
          const { fail } = res;
          if (!fail) {
            this.init();
            this.$message({ type: 'success', message: '删除成功!' });
          }
        });
      });
    },
    getTableList() {
      this.tableLoading = true;
      const childData = {
        pkChildId: this.pkChildId,
        start: 1,
        length: 100000
      };
      this.$post('departList', childData).then(res => {
        const { fail, body } = res;
        this.tableLoading = false;
        if (!fail) {
          this.groupId = body[0].groupId;
          const datas = body[0].pkTeamDetailsVOS;
          // 赋值
          this.tableData = datas;
        }
      });
    },
    memberDetails(row) {
      if (row.zdys) {
        delete row.dpName;
        row.topTeamId = row.teamId;
      }
      row.groupId = this.groupId;
      this.currentRow = row;
      this.selectMemberTitle = '团队人员选择';
      this.smVisible = true;
    },
    deleteBtn(list, force) {
      if (list.length === 1) {
        this.$message.error('删除失败，至少有一个部门参与pk');
        return;
      }
      this.$confirm('是否确定删除该选项？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableLoading = true;
        const params = {
          teamId: force.teamId,
          teamType: '3',
          pkChildId: this.pkChildId
        };
        this.$post('deleteTeam', params)
          .then(res => {
            const { fail, body } = res;
            this.tableLoading = false;
            if (!fail) {
              this.$message({ type: 'success', message: '删除成功！' });
              this.getTableList();
            } else {
              this.$message({ type: 'warning', message: '删除失败！' });
            }
          });
      }).catch(() => {
        this.$message({ type: 'warning', message: '已取消删除！' });
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
      this.$emit('showParent', false);
      this.$emit('refreshParent', true);
    }
  }
};
</script>

<style lang="scss" scoped>
.table-box {
  display: flex;
  justify-content: center;
  .table {
    .head {
      width: max-content;
      border: 1px solid #ebeef5;
      display: flex;
      div {
        color: #909399;
        font-size: 14px;
        text-align: center;
        padding: 12px 0;
        border-right: 1px solid #ebeef5;
      }

      div:last-child {
        border-right: none;
      }

      .cell {
        &:last-child {
          border-right: 1px solid #ebeef5;
        }
      }
    }
    .body {
      min-height: 60px;
      .tr {
        display: flex;
        .td {
          width: 100%;
          padding: 12px 0;
          min-height: 60px;
          text-align: center;
          border: 1px solid #ebeef5;
          display: table;
          flex-shrink: 0;
        }
        .center {
          display: table-cell;
          vertical-align: middle;
          text-align: center;
        }
      }
    }
}

.delete-icon {
  color: red;
  cursor: pointer;
}

}
.header {
  text-align: center;
  margin-bottom: 20px;
  h1 {
    margin-bottom: 20px;
  }
  p {
    font-size: 16px;
  }
}
.paper-ul {
  .paper-li {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ebeef5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .left {
      float: left;
      width: 230px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .right {
      float: right;
    }
  }
}

.pointer {
  cursor: pointer;
}

.vs {
  font-size: 38px;
  color: red;
  line-height: 38px;
}

::v-deep .text-top {
  padding: 0;
  vertical-align: top;
  text-align: center;

  .cell {
    padding: 0;
  }
}

.yz-table-btnbox {
  span {
    float: left;
  }
}

.el-avatar--circle {
  border-radius: 0;
}

::v-deep .el-avatar {
  background: #fff;

  img {
    background: #fff;
    width: 100%;
    height: 100%;
  }
}
</style>
