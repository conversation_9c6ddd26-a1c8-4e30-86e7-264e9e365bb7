<template>
    <el-dialog title="操作记录" :visible.sync="showRecordPop" width="50%" :before-close="handleClose">
        <el-table ref="multipleTable" :data="tableData" style="width: 100%; margin: 25px 0" border>
            <el-table-column prop="type" label="类型" align="center">
                <template slot-scope="scope">
                    {{scope.row.type == 1 ? '单个变更收取方式' : '批量变更收取方式'}}
                </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" align="center"></el-table-column>
            <el-table-column prop="createEmpName" label="操作人" align="center"></el-table-column>
            <el-table-column prop="createTime" label="操作时间" align="center"></el-table-column>
        </el-table>
    </el-dialog>
</template>

<script>
export default {
    props: {
        showRecordPop: {
            type: Boolean,
            default: false
        },
        recordId:{
            type:String
        }
    },
    data() {
        return {
            tableData: []
        }
    },
    watch: {
        showRecordPop(newVal) {
            if (newVal) {
                this.getRecordList();
            }
        }
    },
    methods: {
        getRecordList() {
            this.$http.get(`/feeTypeChange/getLogs?learnId=${this.recordId}&page=${1}&row=${10}`).then((res) => {
                const { fail, body } = res;
                if (!fail) {
                    this.tableData = body.data;
                }
            });
        },

        handleClose() {
            this.$emit('showRecordPop', false); // 使用正确的事件名称
        }
    }
}
</script>

<style></style>