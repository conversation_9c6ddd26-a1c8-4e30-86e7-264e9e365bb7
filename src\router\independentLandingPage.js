import Layout from '@/layout';
// 存放所有新建的落地页面的路由文件
export default [
  {
    path: '/messageAggregation',
    component: () =>
      import('@/views/independentLandingPage/messageAggregation/index'),
    hidden: true
  },
  {
    path: '/assistantTeacher',
    component: () =>
      import(
        '@/views/independentLandingPage/deskmateScholarship/assistantTeacher'
      ),
    hidden: true,
    meta: { title: '助学老师后台' }
  },
  {
    path: '/administrators',
    component: () =>
      import(
        '@/views/independentLandingPage/deskmateScholarship/administrators'
      ),
    hidden: true,
    meta: { title: '管理员后台' }
  },
  {
    path: '/invitateMain',
    component: () =>
      import('@/views/independentLandingPage/transferIntroductionConfig/invitateMain/index'),
    hidden: true,
    meta: { title: '邀约有礼配置' }
  },
  {
    path: '/invitateUserList',
    component: () =>
      import(
        '@/views/independentLandingPage/transferIntroductionConfig/invitationUserList/index'
      ),
    hidden: true,
    meta: { title: '邀约有礼用户名单' }
  },
  {
    path: '/stageSet',
    component: Layout,
    meta: {
      title: '学历分期配置',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/stageSet/index'),
        meta: {
          title: '学历分期配置'
        }
      }
    ]
  }
];
