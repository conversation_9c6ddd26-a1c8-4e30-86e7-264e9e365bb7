<template>
  <div>
    <el-form
      ref='personalForm'
      class='main-forms'
      label-width='180px'
      size='mini'
      :model='form'
      :rules="rules"
    >
      <el-form-item :label='labelTitle' prop='pkChildName'>
        <el-input v-model="form.pkChildName" placeholder="请输入" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item label='是否对外可见：' prop='enable'>
        <el-radio-group v-model="form.enable">
          <el-radio label="1">是</el-radio>
          <el-radio label="2">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="显示时间止：" prop="showStopTime">
        <el-input-number v-model="form.showStopTime" class="main-days" :controls="false" :min="0" />
      </el-form-item>

      <el-form-item label="PK范围(可多选)：" prop="pkRangeList">
        <el-checkbox-group v-model="form.pkRangeList" multiple placeholder="请选择">
          <el-checkbox label="1">成教</el-checkbox>
          <el-checkbox label="2">国开</el-checkbox>
          <el-checkbox label="3">全日制</el-checkbox>
          <el-checkbox label="4">自考</el-checkbox>
          <el-checkbox label="5">研究生</el-checkbox>
          <el-checkbox label="7">海外教育</el-checkbox>
          <el-checkbox label="6">职业教育</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item v-if="showRange" label="选择职业教育：" :prop="showRange?'pkVocationalVOList':''">
        <span class="main-text">已选 {{ getVocLength || 0 }} 门课程</span>
        <el-button type="success" icon="el-icon-plus" @click="careerOpen">添加</el-button>
      </el-form-item>

      <el-form-item label='PK方式：' prop='pkType'>
        <el-radio-group v-model="form.pkType" filterable clearable placeholder="请选择">
          <el-radio label="1">积分PK</el-radio>
          <el-radio label="2">助学人数PK</el-radio>
          <el-radio label="3">人均PK</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="addType == 1 || addType == 2" label='目标类型：' prop='targetType'>
        <el-select v-model="form.targetType" placeholder="请选择目标类型">
          <el-option label="常规目标" :value="1" />
          <el-option label="奖励目标" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="form.pkType == 1" label="积分设置：" :prop="form.pkType == 1?'jifens':''">
        <span class="main-text">已选 {{ form.jifens || 0 }} 门积分</span>
        <el-button type="success" @click="integralOpen">积分设置</el-button>
      </el-form-item>

      <el-form-item v-if="addType == 3" label="展示个人排行榜">
        <el-radio-group v-model="form.isSynShow">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="form.targetType === 2" label='达成奖励目标值：' prop='rewardTarget'>
        <el-input-number v-model="form.rewardTarget" :controls="false" :precision="2" placeholder="请输入达成奖励目标值" />
      </el-form-item>

      <!-- 目标设定显示的条件：个人pk或团队pk目标类型选择了常规目标，或者战队pk -->
      <el-form-item v-if="form.targetType === 1 || addType == 3" label="目标设定：" class="add_tasget" prop="targetConfigList">
        <div class="add_tasget-btn">
          <div v-for="(item, ins) in form.targetConfigList" :key="item" class="btn-li" @click.stop="targetOpen({title:item,ins})">
            <span>{{ item }}</span>
            <i class="el-icon-delete btn-icon" @click.stop="deleteTagget(ins)"></i>
          </div>
          <el-button
            style="margin: 0;height: 30px;display: flex;align-items: center;"
            type="success"
            icon="el-icon-plus"
            @click="targetOpen({})"
          >添加</el-button>
        </div>
        <span class="add_tasget-desc">（最少设定 1 个目标，最多设定 5 个目标）</span>
      </el-form-item>

      <el-form-item label="排序号：" prop="sort">
        <el-input-number v-model="form.sort" :controls="false" :min="0" :precision="0" />
      </el-form-item>

      <el-form-item v-if="addType == 1" label="个人招生系数显示：" prop="coefficientSwitch">
        <el-radio-group v-model="form.coefficientSwitch">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <!-- 添加职业教育 -->
    <dialogCareer :visible="careerShow" :list.sync="form.pkVocationalVOList" @close="careerClose" />
    <!-- 积分设置 -->
    <dialogIntegral :visible="integralShow" :pkdata="pkAddData" @close="integralClose" />
    <!-- 添加目标 -->
    <dialogTarget :visible="targetShow" :viTitle="targetObs.title" @close="targetClose" />
  </div>
</template>

<script>
import dialogCareer from './dialogCareer';
import dialogIntegral from './dialogIntegral';
import dialogTarget from './dialogTarget';

export default {
  components: { dialogCareer, dialogIntegral, dialogTarget },
  props: {
    addType: { type: String, default: '1' } // 1:个人, 2:部门, 3:战队
  },
  data() {
    return {
      isEdit: false,
      labelTitle: '',
      form: {
        pkActId: '', // 主活动ID
        pkChildType: '', // 子活动名称
        pkChildId: '',
        pkChildName: '',
        enable: '1',
        pkRangeList: [],
        pkRange: '',
        pkType: '',
        targetType: 1, // 目标类型,默认常规类型，1:常规类型，2:奖励类型
        rewardTarget: undefined, // 达成奖励目标值
        sort: 0,
        cjScore: 0,
        gkScore: 0,
        zkScore: 0,
        qrScore: 0,
        yjScore: 0,
        hwScore: 0,
        showStopTime: 3,
        jifens: 0,
        isSynShow: '1',
        pkVocationalVOList: [],
        targetConfigList: [],
        coefficientSwitch: 0 // 个人招生系数开关
      },
      rules: {
        pkChildName: [{ required: true, message: '请输入', trigger: 'blur' }],
        showStopTime: [{ required: true, message: '请输入', trigger: 'blur' }],
        enable: [{ required: true, message: '请输入', trigger: 'blur' }],
        pkRangeList: [{ required: true, message: '请输入', trigger: 'blur' }],
        pkType: [{ required: true, message: '请输入', trigger: 'blur' }],
        targetType: [{ required: true, message: '请选择目标类型', trigger: 'change' }],
        rewardTarget: [{ required: true, message: '请输入达成奖励目标值', trigger: 'blur' }],
        jifens: [{ required: true, message: '请输入', trigger: 'blur' }],
        pkVocationalVOList: [{ required: true, message: '请选择', trigger: 'change' }],
        targetConfigList: [{ required: true, message: '目标设定不能为空，请选择', trigger: 'change' }],
        sort: [{
          required: true,
          message: '请输入0~99999的数字',
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (value === '' || value === undefined || value === null || value > 99999) {
              callback(new Error('请输入0~99999的数字'));
            } else {
              callback();
            }
          }
        }]
      },
      // 添加职业教育
      careerShow: false,
      // 积分设置
      integralShow: false,
      pkAddData: [],
      // 添加目标
      targetShow: false,
      targetObs: {}
    };
  },
  inject: ['newRow'],
  computed: {
    // 编辑的数据
    row() {
      return this.newRow();
    },
    // 根据职业教育显示弹窗
    showRange() {
      return Boolean(this.form?.pkRangeList?.find(item => item === '6'));
    },
    // 多少门课程
    getVocLength() {
      return this.form?.pkVocationalVOList?.length || 0;
    }
  },
  methods: {
    firstInit() {
      this.labelTitle = this.addType == 1 ? '个人PK榜单名称' : this.addType == 2 ? '团队榜单名称：' : '战队名称';
      this.form.pkActId = this.row?.pkId;
      this.isEdit = this.row?.isEdit || Boolean(this.form?.pkChildId);
      // 编辑：获取表单数据回显
      if (this.isEdit) {
        const { pkChildId, pkId } = this.row;
        this.form.pkActId = pkId;
        this.form.pkChildId = pkChildId;
        // 获取子活动表单
        this.$post('getChildActivityInfo', { pkChildId }).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form = { ...this.form, ...body };
            this.form.enable = body.enable + '';
            this.form.pkRangeList = body.pkRange?.split(',') || [];
            this.form.pkVocationalVOList = body.pkVocationalVOList || [];
            this.form.pkType = body.pkType + '';
            this.form.cjScore = body.cjScore / 100;
            this.form.gkScore = body.gkScore / 100;
            this.form.zkScore = body.zkScore / 100;
            this.form.qrScore = body.qrScore / 100;
            this.form.yjScore = body.yjScore / 100;
            this.form.hwScore = body.hwScore / 100;
            const { cjScore, gkScore, zkScore, qrScore, yjScore, hwScore, pkRangeList, pkVocationalVOList } = this.form;
            if (pkVocationalVOList?.length) {
              pkVocationalVOList.map(item => {
                item.score = item.score / 100;
              });
            }
            // 积分复现
            if (pkRangeList?.length) {
              const prarr = [];
              const obs = {
                '1': { joinId: '0101', shopKey: 'cjScore', score: cjScore || 0, joinName: '成教' },
                '2': { joinId: '0102', shopKey: 'gkScore', score: gkScore || 0, joinName: '国开' },
                '3': { joinId: '0103', shopKey: 'qrScore', score: qrScore || 0, joinName: '全日制' },
                '4': { joinId: '0104', shopKey: 'zkScore', score: zkScore || 0, joinName: '自考' },
                '5': { joinId: '0105', shopKey: 'yjScore', score: yjScore || 0, joinName: '研究生' },
                '7': { joinId: '0107', shopKey: 'hwScore', score: hwScore || 0, joinName: '海外教育' }
              };
              // 有，则加入数据中
              pkRangeList.forEach(item => {
                if (obs[item]) prarr.push(obs[item]);
              });
              const pkEditData = [...prarr, ...pkVocationalVOList];
              this.form.jifens = pkEditData?.length || 0;
            }
          }
        });
      }
    },
    // 打开-添加职业教育
    careerOpen() {
      this.careerShow = true;
    },
    // 关闭-添加职业教育
    careerClose(list) {
      if (list) {
        this.form.pkVocationalVOList = [...list];
      }
      this.careerShow = false;
    },
    // 打开-积分设置
    integralOpen() {
      const prarr = [];
      const { cjScore, gkScore, zkScore, qrScore, yjScore, hwScore, pkRangeList, pkVocationalVOList } = JSON.parse(JSON.stringify(this.form));
      if (pkRangeList?.length) {
        const obs = {
          '1': { joinId: '0101', shopKey: 'cjScore', score: cjScore || 0, joinName: '成教' },
          '2': { joinId: '0102', shopKey: 'gkScore', score: gkScore || 0, joinName: '国开' },
          '3': { joinId: '0103', shopKey: 'qrScore', score: qrScore || 0, joinName: '全日制' },
          '4': { joinId: '0104', shopKey: 'zkScore', score: zkScore || 0, joinName: '自考' },
          '5': { joinId: '0105', shopKey: 'yjScore', score: yjScore || 0, joinName: '研究生' },
          '7': { joinId: '0107', shopKey: 'hwScore', score: hwScore || 0, joinName: '海外教育' }
        };
        // 有，则加入数据中
        pkRangeList.forEach(item => {
          if (obs[item]) prarr.push(obs[item]);
        });
      }
      const newsData = [...prarr, ...pkVocationalVOList];
      this.pkAddData = newsData;
      this.integralShow = true;
    },
    // 关闭-添加积分设置
    integralClose(arr = []) {
      if (arr.length) {
        this.form.jifens = arr.length;
        // 解构赋值给表单
        const newsArr = [];
        arr.forEach(item => {
          // PK范围的数据
          if (item.shopKey) {
            this.form[item.shopKey] = item.score;
          } else {
            newsArr.push({ ...item });
          }
        });
        // 职业教育及自定义的数据
        if (newsArr.length) this.form.pkVocationalVOList = newsArr;
      }
      this.integralShow = false;
    },
    // 打开-添加/编辑目标设定
    targetOpen(obs) {
      if (!obs?.title && this.form?.targetConfigList?.length >= 5) {
        this.$message({ type: 'warning', message: '目标设定最多为5个目标' });
        return false;
      }
      this.targetObs = obs;
      this.targetShow = true;
    },
    // 关闭-添加/编辑目标设定
    targetClose(text) {
      // 有值，添加或者编辑
      if (!this.form?.targetConfigList?.length) {
        this.form.targetConfigList = [];
      }
      if (text) {
        const { title, ins } = this.targetObs;
        if (title) {
          this.form.targetConfigList[ins] = text;
        } else {
          let UIS = false;
          this.form.targetConfigList?.forEach(item => {
            if (item == text) {
              UIS = true;
            }
          });
          if (UIS) {
            this.$message({ type: 'warning', message: '目标名称已存在！' });
          } else this.form.targetConfigList.push(text);
        }
      }
      this.targetObs = {};
      this.targetShow = false;
    },
    // 删除-目标设定
    deleteTagget(ins) {
      this.$confirm('您确定删除当前选中的目标设定值吗？', '提示', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        this.form.targetConfigList.splice(ins, 1);
      });
    },
    // 下一步：提交添加/编辑
    submitApi() {
      this.form.pkRange = this.form?.pkRangeList?.toString();
      this.$refs['personalForm'].validate((valid) => {
        if (valid) {
          const desc = this.addType == 1 ? '个人' : this.addType == 3 ? '战区' : '团队';
          this.$confirm(`您确定提交${desc}PK的基础信息吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let formData = JSON.parse(JSON.stringify(this.form));
            if (formData?.pkVocationalVOList?.length) {
              formData.pkVocationalVOList.map(item => {
                item.score = item.score * 100;
              });
            }
            formData = this.getFormData(formData);
            // 添加编辑接口
            let url = 'addChildActivity';
            if (this.isEdit) {
              url = 'updateChildActivity';
            }
            this.$post(url, formData, { json: true }).then(res => {
              const { fail, body } = res;
              if (!fail) {
                this.$message({ message: '提交成功', type: 'success' });
                this.form.pkChildId = this.isEdit ? this.form.pkChildId : body;
                this.$emit('setActNus', {
                  bools: true,
                  pkChildId: this.isEdit ? this.form.pkChildId : body,
                  type: this.addType,
                  childActivityId: this.isEdit ? this.form.pkChildId : body
                });
                setTimeout(() => {
                  this.isEdit = true;
                }, 0);
              } else this.$emit('setActNus', { bools: false });
            });
          });
        } else this.$emit('setActNus', { bools: false });
      });
    },
    // 参数构造
    getFormData(formData) {
      formData.cjScore = formData.cjScore * 100;
      formData.gkScore = formData.gkScore * 100;
      formData.qrScore = formData.qrScore * 100;
      formData.zkScore = formData.zkScore * 100;
      formData.yjScore = formData.yjScore * 100;
      formData.hwScore = formData.hwScore * 100;
      formData.pkChildType = this.addType;
      return formData;
    }
  }
};
</script>

  <style lang="scss">

  </style>

