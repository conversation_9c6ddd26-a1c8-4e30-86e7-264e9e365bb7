<template>
  <div>
    <common-dialog
      is-full
      class="common-dialog"
      width="1100px"
      :title="optionsType"
      :visible.sync="show"
      @open="open"
      @close="close"
    >
      <el-form ref="form" :model="form" label-width="110px" class="yz-search-form">
        <el-form-item label="校区：">
          <el-select v-model="form.campusName" placeholder="请选择校区" prop="campusName" @change="schoolName">
            <el-option v-for="item in schoolInfo" :key="item.id" :label="item.campusName" :value="item.campusId" />
          </el-select>
        </el-form-item>
        <el-form-item label="部门:">
          <el-select v-model="form.dpName" :disabled="!form.campusName" placeholder="请选择部门">
            <el-option v-for="item in departmentInfo" :key="item.dpId" :label="item.dpName" :value="item.dpId" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间段:">
          <el-date-picker v-model="tiemValue" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="changeTiem" />
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" @click="query">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </el-form>
      <div style="float:right;margin:15px 25px 15px 0;">
        <el-button type="primary" @click="problemData()">问题数据</el-button>
        <el-button type="primary" @click="exportList()">导出Excel</el-button>
      </div>
      <el-table ref="multipleTable" :data="tableData" style="width: 100%; margin: 25px 0" border>
        <el-table-column prop="campusName" label="校区名称" align="center" />
        <el-table-column prop="dpName" label="部门" align="center" />
        <el-table-column prop="searchTotal" label="关键词搜索" align="center" />
        <el-table-column prop="clickTotal" label="点击量" align="center" />
        <el-table-column prop="collectTotal" label="收藏量" align="center" />
        <el-table-column prop="questionTotal" label="发布次数" align="center" />
        <el-table-column prop="usefulTotal" label="问题（有用）" align="center" />
        <el-table-column prop="uselessTotal" label="问题（没用）" align="center" />
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <div class="iconStyle">
              <div class="el-icon-view" @click="handleEdit(scope.$index, scope.row)"></div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pageClass">
        <el-pagination
          :current-page.sync="page.currentPage"
          :page-size="page.pageSize"
          :total="page.total"
          :page-sizes="[10, 20, 30, 40]"
          layout="total, prev, pager, next, sizes,jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </common-dialog>
    <personalData :rankingShow="rankingShow" :optionsType="personalTitle" :showList='showList' @dialogVisible='rankingShow=false' />
    <problemData :visible.sync="problemShow" :optionsType="problemTitle" @dialogVisible='problemShow=false' />
  </div>
</template>

<script>
import { ossUri, downUri } from '@/config/request';
import moment from 'moment';
import personalData from './personalData';
import problemData from './problemData';
export default {
  components: { problemData, personalData },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    optionsType: {
      type: String,
      default: ''
    },
    infoList: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      form: {
        dpName: '',
        startSearchTime: '',
        endSearchTime: '',
        campusName: ''
      },
      ruleForm: {},
      tableData: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 1
      },
      rankingShow: false,
      rules: {
        number: [
          { required: true, message: '请输入0~9999的数字', trigger: 'blur' }
        ]
      },
      schoolInfo: [],
      departmentInfo: [],
      showList: {},
      tiemValue: {},
      problemShow: false,
      problemTitle: '',
      personalTitle: ''
    };
  },
  watch: {
    visible(val) {
      this.show = val;
      if (val) {
        this.loadList();
        this.schoolList();
      }
    }
  },
  created() {},
  methods: {
    query() {
      this.loadList();
    },
    reset() {
      this.form = {};
      this.loadList();
    },
    loadList() {
      const params = {
        campusId: this.form.campusName || undefined,
        startSearchTime: this.form.startSearchTime ? moment(this.form.startSearchTime).format('YYYY-MM-DD') : undefined,
        endSearchTime: this.form.endSearchTime ? moment(this.form.endSearchTime).format('YYYY-MM-DD') : undefined,
        dpId: this.form.dpName || undefined,
        start: this.page.currentPage,
        length: this.page.pageSize,
        orderField: '',
        orderType: ''
      };
      this.$http.get('/question/report/getAllQuestionDeptReport.do', { params }).then(res => {
        if (res.ok) {
          this.tableData = res.body.data;
          this.page.total = res.body.recordsTotal;
        }
      });
    },
    open() {
      // this.$emit('dialogVisible', false);
    },
    close() {
      this.$emit('dialogData', false);
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      console.log(`每页 ${val} 条`);
      this.loadList();
    },
    handleCurrentChange(val) {
      this.page.currentPage = val;
      console.log(`当前页: ${val}`);
      this.loadList();
    },
    exportList() {
      var exportUrl = downUri + '/question/report/exportQuestionDept.do?campusId=' + this.form.campusName + '&startSearchTime=' + this.form.startSearchTime + '&endSearchTime=' + this.form.endSearchTime + '&dpId=' + this.form.dpName;
      // 导出文件
      window.location.href = exportUrl;
    },
    problemData() {
      this.problemShow = true;
      this.problemTitle = '数据报表';
    },
    handleClose() {
      this.rankingShow = false;
    },
    handleEdit(index, row) {
      this.showList = row;
      this.personalTitle = '数据报表（个人）';
      this.rankingShow = true;
    },
    ok(formName) {
      const params = {
        id: this.showList.id,
        number: this.ruleForm.number
      };
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$http.post('/question/log/updateManualSort.do', params).then(res => {
            if (res.ok) {
              this.$message.success('修改成功');
              this.loadList();
            } else {
              this.$message.error('修改失败');
            }
          });
          this.ruleForm = {};
          this.rankingShow = false;
        }
      });
    },
    // 下拉框校区数据
    schoolList() {
      const params = {};
      this.$http.post('/campus/selectAllList.do', params).then(res => {
        if (res.ok) {
          this.schoolInfo = res.body;
        }
      });
    },
    // 选择状态调用部门接口
    schoolName(value) {
      this.departmentList(value);
    },
    // 部门列表接口
    departmentList(value) {
      const params = {
        // campusName: this.form.campusName
        campusId: value
      };
      this.$http.post('/dep/selectAllList.do', params).then(res => {
        if (res.ok) {
          this.departmentInfo = res.body;
        }
      });
    },
    changeTiem(val) {
      this.form.startSearchTime = moment(this.tiemValue[0]).format('YYYY-MM-DD');
      this.form.endSearchTime = moment(this.tiemValue[1]).format('YYYY-MM-DD');
      console.log(this.form.endSearchTime, this.form.startSearchTime, 'val');
    }
  }
};
</script>

<style lang = "scss" scoped>
.center{
  i{
    color:red
  }
  .center_txt{
    float: right;
    height: 40px;
    line-height: 40px;
    /* text-align: right; */
  }
  .center_content{
    float: left;
    height: 40px;
    line-height: 40px;
    /* text-align:left ; */
  }
}
  .pageClass{
  float: right;
  margin-top: -15px;
}
</style>
