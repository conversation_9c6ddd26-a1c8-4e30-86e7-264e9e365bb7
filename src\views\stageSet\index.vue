<template>
  <div class="yz-base-container">
    <el-table
      ref="multipleTable"
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin: 50px 0 20px"
      border
      :span-method="objectSpanMethod"
      :row-style="{ height: '50px' }"
    >
      <el-table-column label="序号" prop="index" align="center" width="50">
        <template slot-scope="scope">{{ scope.row.index }}</template>
      </el-table-column>
      <el-table-column prop="recruitType" label="收费类型" align="center">
        <template slot-scope="scope">{{ scope.row.recruitType | recruitType }}</template>
      </el-table-column>
      <el-table-column prop="typeText" label="支付类型" align="center" />
      <el-table-column prop="isEnableOutLine" label="是否开启" align="center" />
      <el-table-column prop="downAmount" label="首付金额（元）" align="center" />
      <el-table-column prop="period" label="期数" align="center" />
      <el-table-column prop="isEnableInline" label="是否开启" align="center" />
      <el-table-column prop="interestRate" label="年利率" align="center" />
      <el-table-column prop="subsidyType" label="利息模式" align="center" />
      <el-table-column prop="subsidyProportion" label="商贴比例" align="center" />
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click="handleRecord(scope.row)">配置利息模式</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
      <el-pagination
        :current-page.sync="page.pageNum"
        layout="prev, pager, next, sizes,jumper"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <recordPop
      :showRecordPop="showRecordPop"
      :recordId="recordId"
      @showRecordPop="showRecordPop = false"
    />
  </div>
</template>

<script>
import recordPop from './recordPop';
import { getTextFromDict } from '@/utils';
import { getMulPrecision } from '@/utils/precision';

export default {
  components: {
    recordPop
  },
  filters: {
    recruitType(val) {
      if (!val) return;
      const name = getTextFromDict('recruitType', val);
      return name;
    }
  },
  data() {
    return {
      page: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      tableData: [],
      showRecordPop: false,
      loading: true,
      recordId: ''
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    getTableList() {
      this.loading = true;
      this.$post('instalmentList', {}, { json: true }).then((res) => {
        const { ok, body, code } = res;
        if (code == '00' && ok) {
          const processedData = [];
          body.data.forEach((item, index) => {
            const baseInfo = JSON.parse(JSON.stringify(item));
            baseInfo.index = index + 1;
            item.details.forEach((detail) => {
              delete baseInfo.details;
              delete baseInfo.isEnable;
              delete detail.id;
              baseInfo.isEnableOutLine = item.isEnable ? '是' : '否';
              detail.isEnableInline = detail.isEnable ? '是' : '否';
              detail.period = `${detail.period}期`;
              const obj = { 1: '客息', 2: '混合', 3: '贴息' };
              detail.subsidyType = detail.isEnable ? obj[detail.subsidyType] : '-';
              const interestRate = getMulPrecision(detail.interestRate, 100);
              const subsidyProportion = getMulPrecision(detail.subsidyProportion, 100);
              detail.interestRate = detail.isEnable ? `${interestRate}%` : '-';
              detail.subsidyProportion = detail.isEnable ? `${subsidyProportion}%` : '-';
              processedData.push({ ...baseInfo, ...detail });
            });
          });

          this.tableData = processedData;
          this.page.total = body.recordsTotal;
          this.loading = false;
        } else {
          this.loading = false;
        }
      });
    },
    handleRecord(row) {
      this.recordId = row.id;
      this.showRecordPop = true;
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.getIndexList();
    },
    handleCurrentChange(val) {
      this.page.pageNum = val;
      this.getIndexList();
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const columnIndexArr = [0, 1, 2, 3, 4, 10];
      if (columnIndexArr.includes(columnIndex)) {
        if (rowIndex % 2 === 0) {
          return {
            rowspan: 2,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.block {
  margin-top: 10px;
  text-align: right;
}
::v-deep.el-table th.el-table__cell {
  background-color: rgb(243, 243, 243);
}
::v-deep .el-table__row .el-table__cell {
  padding: 3px 0 !important;
}
</style>
