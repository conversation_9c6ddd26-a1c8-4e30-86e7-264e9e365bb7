<template>
  <common-dialog
    is-full
    width="1000px"
    title="毕业论文选题管理"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='100px'
        @submit.native.prevent='search'
      >
        <el-form-item label='年级' prop='grade'>
          <el-select
            v-model="form.grade"
            filterable
            clearable
            placeholder="请选择"
            @change="handleJointChange"
          >
            <el-option
              v-for="(item,index) in grade"
              :key="index"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='院校' prop='unvsId'>
          <infinite-selects
            v-model="form.unvsId"
            :props="{
              apiName: 'getUnvs',
              value: 'unvs_id',
              label: 'unvs_name',
              query: 'sName'
            }"
            @change="handleJointChange"
          />
        </el-form-item>

        <el-form-item label='专业层次' prop='pfsnLevel'>
          <el-select
            v-model="form.pfsnLevel"
            clearable
            placeholder="请选择"
            @change="handleJointChange"
          >
            <el-option
              v-for="(item,index) in schoolLevel"
              :key="index"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='专业' prop='pfsnId'>
          <el-select
            v-model="form.pfsnId"
            v-loadmore="loadSubject"
            :remote-method="querySubject"
            filterable
            remote
            clearable
            placeholder="请选择"
            @clear="clearSubject"
          >
            <el-option
              v-for="(item,index) in subject"
              :key="index"
              :label="item.label"
              :value="item.pfsnId"
            />
          </el-select>
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>

      <div class='yz-table-btnbox'>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-download"
          @click="utVisible = true"
        >批量导入论文题目</el-button>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="grade" label="年级" align="center">
          <template slot-scope="scope">
            {{ scope.row.grade }}级
          </template>
        </el-table-column>
        <el-table-column label="院校与专业">
          <template slot-scope="scope">
            <p>{{ scope.row.unvsName }}</p>
            <p>{{ scope.row.pfsnName }}</p>
          </template>
        </el-table-column>
        <el-table-column prop="pfsnLevel" label="层次" align="center">
          <template slot-scope="scope">
            {{ scope.row.pfsnLevel | pfsnLevel }}
          </template>
        </el-table-column>
        <el-table-column prop="paperTitle" label="论文题目" align="center" />
        <el-table-column prop="name" label="启停" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.enabled"
              active-color="#13ce66"
              @change="handelStartOrStop(scope.row, scope.row.enabled)"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <upload-topic :visible.sync="utVisible" type="1" />

  </common-dialog>
</template>

<script>
import uploadTopic from './import-essay-topic';
import { getTextFromDict } from '@/utils';
export default {
  components: {
    uploadTopic
  },
  filters: {
    pfsnLevel(val) {
      if (!val) return;
      const name = getTextFromDict('pfsnLevel', val);
      return name;
    }
  },
  provide() {
    return {
      parentVm: this
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      form: {
        grade: '',
        unvsId: '',
        pfsnLevel: '',
        pfsnId: ''
      },
      tableLoading: false,
      tableData: [],
      grade: [], // 年级
      schoolLevel: [], // 层次
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      utVisible: false,
      subject: [], // 专业
      subjectQuery: '',
      subjectPage: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    handleJointChange() {
      this.clearSubject();
    },
    open() {
      this.grade = this.$dictJson.grade;
      this.schoolLevel = this.$dictJson.pfsnLevel;
      this.getTableList();
      this.getSubject();
    },
    handelStartOrStop(row, status) {
      const params = {
        id: row.id,
        isEnabled: status ? 0 : 1
      };
      this.$post('startOrStopThesis', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
        });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    getTableList() {
      this.tableLoading = true;
      const params = {
        ...this.form,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('findAllThesisManagementList', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableLoading = false;
            body.data.forEach(item => {
              if (item.isEnabled === '0') {
                item.enabled = true;
              } else {
                item.enabled = false;
              }
            });
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
            console.log(body);
          }
        });
    },
    clearSubject() {
      this.subjectQuery = '';
      this.subject = [];
      this.subjectPage.page = 1;
      this.getSubject();
    },
    querySubject(query) {
      this.subjectQuery = query;
      this.subjectPage.page = 1;
      this.subject = [];
      setTimeout(() => {
        this.getSubject();
      }, 100);
    },
    getSubject() {
      const params = {
        sName: this.subjectQuery,
        sId: this.form.unvsId,
        page: this.subjectPage.page,
        rows: this.subjectPage.limit,
        ext1: this.form.pfsnLevel, // 专业层次
        ext2: this.form.grade // 年级
      };
      this.$http.get('/baseinfo/sPfsn.do', { params: params })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            body.data.forEach(item => {
              const levelName = getTextFromDict('pfsnLevel', item.pfsnLevel);
              item.label = `(${item.pfsnCode})${item.pfsnName}[${levelName}]`;
            });
            this.subject = this.subject.concat(body.data);
          }
        });
    },
    loadSubject() {
      if (this.subjectPage.total === this.subject.length) {
        return;
      }
      this.subjectPage.page += 1;
      this.getSubject();
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
