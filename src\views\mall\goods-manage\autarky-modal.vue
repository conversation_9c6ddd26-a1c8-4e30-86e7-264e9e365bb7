<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    :title="`${ isEdit ? '修改' : '新增'}` + '自营好物'"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="170px"
        size="small"
      >
        <el-form-item label="商品类型:">
          <span>自营好物</span>
        </el-form-item>
        <el-form-item label="商品名称:" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入商品名称" maxlength="25" show-word-limit />
        </el-form-item>

        <el-form-item label="副标题（简介）:" prop="productTitle">
          <el-input v-model="form.productTitle" placeholder="请输入副标题（简介）" maxlength="25" show-word-limit />
        </el-form-item>

        <el-form-item label="商品图片:" prop="urlList">
          <p>最多可支持上传5张，最少上传1张，按顺序依次展示, 建议图片尺寸宽高比1：1, 图片大小限制1M内</p>
          <upload-file
            :size="1"
            :max-limit="5"
            exts="jpg|png"
            :file-list="urlList"
            :is-drag="true"
            :all-file-list="form.urlList"
            @dragEnd="dragEnd"
            @remove="goodsImgRemove"
            @success="goodsImgSuccess"
          />
        </el-form-item>

        <el-form-item label="规格及价格配置:" required>
          <el-form-item prop="productSpecDetailList.length">
            <el-button type="primary" @click="createQuota">新增规格</el-button>
          </el-form-item>
          <el-table ref="tableRef" size="mini" :data="form.productSpecDetailList" border>
            <el-table-column label="规格名称" align="center">
              <template slot-scope="scope">
                <el-form-item :prop="`productSpecDetailList.${scope.$index}.specName`" :rules="rules.specName">
                  <el-input v-model="scope.row.specName" placeholder="请输入规格名称" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="成本价（元）" align="center">
              <template slot-scope="scope">
                <el-form-item :prop="`productSpecDetailList.${scope.$index}.retailPrice`" :rules="rules.retailPrice">
                  <el-input-number v-model="scope.row.retailPrice" :controls="false" :precision="2" :min="0" placeholder="请输入成本价" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="商城售价（元）" align="center">
              <template slot-scope="scope">
                <el-form-item :prop="`productSpecDetailList.${scope.$index}.marketPrice`" :rules="rules.marketPrice">
                  <el-input-number v-model="scope.row.marketPrice" :controls="false" :precision="2" :min="0" placeholder="请输入商城售价" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="可兑换数量（个）" align="center">
              <template slot-scope="scope">
                <el-form-item :prop="`productSpecDetailList.${scope.$index}.inventoryAmount`" :rules="rules.inventoryAmount">
                  <el-input-number v-model="scope.row.inventoryAmount" :controls="false" :precision="0" placeholder="请输入可兑换数量" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="primary" @click="deleteGoodsQuota(scope.$index)">删除</el-button>
              </template>

            </el-table-column>
          </el-table>
        </el-form-item>

        <el-form-item label="详细介绍:" prop="productDesc">
          <wang-editor ref="productDesc" v-model="form.productDesc" :height="600" :content.sync="form.productDesc" />
        </el-form-item>

        <el-form-item label="是否限购:" required>
          <div style="display: flex;">
            <el-form-item prop="limitedAmountRadio">
              <el-radio-group v-model="form.limitedAmountRadio">
                <el-radio :label="0">不限购</el-radio>
                <el-radio :label="1">每人限购</el-radio>
              </el-radio-group>
            </el-form-item>
            <template v-if="form.limitedAmountRadio == 1">
              <el-form-item prop="limitedAmount" class="ml10">
                <el-input-number v-model="form.limitedAmount" :controls="false" :precision="0" :min="1" placeholder="请输入" />
              </el-form-item>
              <span class="ml10">件</span>
            </template>
          </div>
        </el-form-item>

        <el-form-item label="是否包邮:" required>
          <div style="display: flex;">
            <el-form-item prop="freightAmountRadio">
              <el-radio-group v-model="form.freightAmountRadio">
                <el-radio :label="0">包邮</el-radio>
                <el-radio :label="1">运费</el-radio>
              </el-radio-group>
            </el-form-item>
            <template v-if="form.freightAmountRadio == 1">
              <el-form-item prop="freightAmount" class="ml10">
                <el-input-number v-model="form.freightAmount" :controls="false" :precision="0" :min="0" placeholder="请输入" />
              </el-form-item>
              <span class="ml10">元</span>
            </template>
          </div>
        </el-form-item>

        <el-form-item label="售卖开始时间:" prop="sellingStartTime">
          <el-date-picker
            v-model="form.sellingStartTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="选择日期时间"
            align="left"
            style="width: 200px"
            @change="changeStartTime"
          />
        </el-form-item>

        <el-form-item label="售卖有效期:" required>
          <div style="display: flex;">
            <el-form-item prop="showTimeStatus">
              <el-radio-group v-model="form.showTimeStatus">
                <el-radio label="PERMANENT_EFFECTIVE">永久有效</el-radio>
                <el-radio label="TIME_EFFECTIVE">结束时间</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="form.showTimeStatus == 'TIME_EFFECTIVE'" class="ml10" prop="sellingEndTime">
              <el-date-picker
                v-model="form.sellingEndTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="pickerOptions"
                type="datetime"
                placeholder="选择日期时间"
                align="left"
                style="width: 200px"
              />
            </el-form-item>
          </div>

        </el-form-item>

        <el-form-item label="结束后是否自动下架:" prop="autoUnshelve">
          <el-radio-group v-model="form.autoUnshelve">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="状态:" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">上架</el-radio>
            <el-radio :label="0">下架</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="虚拟销量:" prop="virtualSellingAmount">
          <div style="display: flex;">
            <el-input-number v-model="form.virtualSellingAmount" :controls="false" :min="0" :max="999" placeholder="请输入虚拟销量" style="width: 200px" />
            <span>（可输入0~999）</span>
          </div>
        </el-form-item>

        <el-form-item label="商品权重:" prop="weight">
          <div style="display: flex;">
            <el-input-number v-model="form.weight" :controls="false" :min="0" :max="9999" placeholder="请输入商品权重" style="width: 200px" />
            <span>（可输入0~9999，数值越大排序越靠前）</span>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>

</template>

<script>
import moment from 'moment';
import { ossUri } from '@/config/request';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      confirmLoading: false,
      show: false,
      isEdit: false, // 是否编辑
      rules: {
        productName: [
          { required: true, message: '请输入商品名称', trigger: 'blur' }
        ],
        productTitle: [
          { required: true, message: '请输入商品名称', trigger: 'blur' }
        ],
        urlList: [
          { required: true, message: '请上传商品图片', trigger: 'change' }
        ],
        'productSpecDetailList.length': [
          { type: 'number', min: 1, message: '请输入规格及价格配置', trigger: 'change' }
        ],
        specName: [
          { required: true, message: '请输入规格名称', trigger: 'blur' },
          { max: 10, message: '最多只能10字符', trigger: 'blur' }
        ],
        retailPrice: [
          { required: true, message: '请输入成本价（元）', trigger: 'blur' }
        ],
        marketPrice: [
          { required: true, message: '请输入商城售价（元））', trigger: 'blur' }
        ],
        inventoryAmount: [
          { required: true, message: '请输入可兑换数量（个）', trigger: 'blur' }
        ],
        productDesc: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ],
        limitedAmountRadio: [
          { required: true, message: '请选择是否限购', trigger: 'change' }
        ],
        limitedAmount: [
          { required: true, message: '请输入限购数量', trigger: 'blur' }
        ],
        freightAmountRadio: [
          { required: true, message: '请选择是否包邮', trigger: 'change' }
        ],
        freightAmount: [
          { required: true, message: '请输入运费', trigger: 'blur' }
        ],
        sellingStartTime: [
          { required: true, message: '请选择售卖开始时间', trigger: 'blur' }
        ],
        showTimeStatus: [
          { required: true, message: '请选择售卖开始时间', trigger: 'blur' }
        ],
        sellingEndTime: [
          { required: true, message: '请选择售卖有效期', trigger: 'blur' }
        ],
        autoUnshelve: [
          { required: true, message: '请选择结束后是否自动下架', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        virtualSellingAmount: [
          { required: true, message: '请输入虚拟销量', trigger: 'blur' }
        ],
        weight: [
          { required: true, message: '请输入商品权重', trigger: 'blur' }
        ]
      },
      form: {
        urlList: [],
        productSpecDetailList: []
      },
      urlList: [] // 商品图片
    };
  },
  computed: {
    pickerOptions() {
      if (!this.form.sellingStartTime) return;
      const startDateTime = this.form.sellingStartTime;
      const timePart = startDateTime.split(' ')[1];
      const date = moment(startDateTime, 'YYYY-MM-DD HH:mm:ss');
      const startDay = date.startOf('day').format('YYYY-MM-DD HH:mm:ss');
      return {
        disabledDate: (time) => {
          return time.getTime() < new Date(startDay).getTime();
        },
        selectableRange: `${timePart} - 23:59:59`
      };
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 开始时间改变
    changeStartTime(e) {
      const startTime = new Date(e).getTime();
      const endTime = new Date(this.form.sellingEndTime).getTime();
      if (!e || startTime > endTime) {
        this.form.sellingEndTime = null;
      }
    },
    // 图片拖拽结束
    dragEnd(arr) {
      this.form.urlList = arr;
    },
    // 商品图片删除
    goodsImgRemove({ file, fileList }) {
      let index = 0;
      for (let i = 0; i < this.form.urlList.length; i++) {
        const item = this.form.urlList[i];
        if (item.fileUrl === file.response) {
          index = i;
          break;
        }
      }

      this.form.urlList.splice(index, 1);
    },
    // 商品图片上传成功
    goodsImgSuccess({ response, file, fileList }) {
      this.form.urlList.push({ fileUrl: file.response });
    },
    // 打开弹框
    open() {
      if (this.currentId) {
        this.isEdit = true;
        this.$http.get(`/product/getById/${this.currentId}`).then(res => {
          const { fail, body } = res;
          if (!fail) {
            const form = body;

            this.$refs.productDesc.setContent(form.productDesc);

            const urlList = form.urlList;
            form.urlList = [];
            urlList.forEach(item => {
              this.urlList.push({ url: ossUri + item });
              form.urlList.push({ fileUrl: item });
            });

            // 是否限购
            if (!form.limitedAmount) {
              form.limitedAmountRadio = 0;
              form.limitedAmount = undefined;
            } else {
              form.limitedAmountRadio = 1;
            }

            // 是否包邮
            if (form.freightAmount == 0) {
              form.freightAmountRadio = 0;
              form.freightAmount = undefined;
            } else {
              form.freightAmountRadio = 1;
            }

            this.form = form;
          }
        });
      }
    },
    resetData() {
      this.confirmLoading = false;
      this.isEdit = false;
      this.form = {
        urlList: [],
        productSpecDetailList: []
      };
      this.urlList = [];
    },
    // 关闭弹框
    close() {
      this.resetData();
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 删除规格
    deleteGoodsQuota(index) {
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.productSpecDetailList.splice(index, 1);
      }).catch(() => {});
    },
    // 新增规格
    createQuota() {
      this.$refs.formRef.clearValidate('productSpecDetailList.length');
      if (this.form.productSpecDetailList.length >= 10) {
        return this.$message.error('最多只支持配置10个规格！');
      }
      const obj = {
        specName: undefined,
        retailPrice: undefined,
        marketPrice: undefined,
        inventoryAmount: undefined
      };
      this.form.productSpecDetailList.push(obj);
    },
    // 提交
    submit() {
      this.$refs.formRef.validate((valid) => {
        if (!valid) return false;

        this.confirmLoading = true;

        const form = JSON.parse(JSON.stringify(this.form));
        form.productType = 'SELF_ENTITY_PRODUCT'; // 自营好物
        // 商品图片
        form.urlList = form.urlList.map(item => item.fileUrl);

        // 是否限购
        if (form.limitedAmountRadio == 0) {
          form.limitedAmount = null;
        }
        delete form.limitedAmountRadio;

        // 是否包邮
        if (form.freightAmountRadio == 0) {
          form.freightAmount = 0;
        }
        delete form.freightAmountRadio;

        // 售卖有效期
        if (form.showTimeStatus == 'PERMANENT_EFFECTIVE') {
          delete form.sellingEndTime;
        }

        let apiKey = 'addZMProduct';
        if (this.isEdit) {
          apiKey = 'updateZMProduct';
        }
        this.$post(apiKey, form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>

<style lang='scss' scoped>
.common-dialog {
  ::v-deep .yz-common-dialog {
    margin-top: 2vh !important;
  }
  ::v-deep .yz-common-dialog__content {
    max-height: 76vh;
  }
}
</style>
