<!-- 转介绍配置页面 -->
<template>
  <div class="messageAggregation">
    <el-form class="messageAggregation-forms" :model="querys" :size="'mini'" label-width="210px" @submit.native.prevent="searchBtn(1)">
      <el-col :span="20">
        <el-form-item label="任务id：">
          <el-input v-model.trim="querys.id" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="任务类型：">
          <el-select v-model.trim="querys.type" filterable clearable placeholder="请选择">
            <el-option label="注册" value="1" />
            <el-option label="测评" value="2" />
            <el-option label="报名活动" value="3" />
            <el-option label="首次发帖" value="4" />
            <el-option label="添加老师" value="5" />
            <el-option label="报读" value="6" />
            <el-option label="缴费" value="7" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务标题：">
          <el-input v-model.trim="querys.title" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="启用状态：">
          <el-select v-model="querys.isAllow" filterable clearable placeholder="请选择">
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="对未缴费的邀约人是否生效：">
          <el-select v-model="querys.isSupportUnpaid" filterable clearable placeholder="请选择">
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <div class="forms-btn">
        <el-button type="primary" icon="el-icon-search" native-type="submit" :size="'mini'" v-text="'搜索'" />
        <el-button icon="el-icon-refresh" :size="'mini'" @click="searchBtn(0)" />
      </div>
    </el-form>
    <div style="float: right; margin: 15px 25px 15px 0">
      <el-button type="primary" icon="el-icon-setting" :size="'mini'" @click="showLimitSet=true">
        邀约上限设置
        <mainLimitSet :visible.sync="showLimitSet" @on-close="showLimitSet=false" />
      </el-button>
      <el-button style="margin: 0 15px" type="warning" icon="el-icon-edit" :size="'mini'" @click="showFormConfig=true">
        邀约有礼页配置
        <mainFormConfig :visible.sync="showFormConfig" @on-close="showFormConfig=false" />
      </el-button>
      <el-button type="success" icon="el-icon-plus" :size="'mini'" @click="openTaskeBtn(null)">
        新增任务
        <mainTaske :visible.sync="showTaske" :edits="taskeEdit" @on-close="closeTaskeBtn" />
      </el-button>
    </div>
    <el-table v-loading="tableLoading" border :size="'small'" :data="tableData">
      <el-table-column prop="id" label="任务id" align="center" />
      <el-table-column prop="typeText" label="任务类型" align="center" />
      <el-table-column prop="title" label="任务标题" align="center" />
      <el-table-column prop="detail" label="任务明细" align="center" />
      <el-table-column prop="isSupportUnpaid" label="对未缴费的邀约人是否生效" align="center">
        <template slot-scope="scope">
          {{ scope.row.isSupportUnpaid==0?'否':'是' }}
        </template>
       </el-table-column>
      <el-table-column prop="giveZhimi" label="赠送智米数" align="center" />
      <el-table-column prop="allowText" label="启用状态" align="center" />
      <el-table-column label="操作" align="center" width="250">
        <template slot-scope="scope">
          <el-button :size="'mini'" style="margin-bottom: 6px;" type="warning" @click="openTaskeBtn(scope.row)">
            编辑
          </el-button>
          <el-button :size="'mini'" style="margin-bottom: 6px;" type="primary" @click="openLookRecord(scope.row)">
            操作记录
          </el-button>
          <el-button v-if="scope.row.type!=7" :size="'mini'" :type="scope.row.isAllow==1?'info':'success'" @click="onEnableDisable(scope.row)">
            {{ scope.row.isAllow==1?'禁用':'启用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="yz-table-pagination">
      <pagination :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.rows" @pagination="getTableList" />
    </div>
    <!-- 弹窗-查看操作记录 -->
    <el-dialog :visible.sync="showRecords" append-to-body title="查看操作记录">
      <el-table v-loading="recordLoading" border size="small" :data="recordData" header-cell-class-name="table-cell-header">
        <el-table-column prop="createTime" label="操作时间" align="center" />
        <el-table-column prop="createEmpName" label="操作人" align="center" />
        <el-table-column prop="remark" label="变更信息" align="center">
          <template slot-scope="scope">
            <span v-html="scope.row.remark"></span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 弹窗-启用禁用 -->
    <el-dialog :visible.sync="showEnableDisable" append-to-body width="20%" :center="true" :title="`${typeEnableDisable==1?'禁用':'启用'}确认弹窗`">
      <div v-if="typeEnableDisable==1" class="ed">任务禁用后，新注册用户将无法参与<br />（任务启用期间注册的用户不受影响）</div>
      <div v-else class="ed">任务启用后，新注册用户可参与任务<br />（任务禁用期间注册的用户无法参与）</div>
      <span slot="footer" class="dialog-footer">
        <el-button :size="'mini'" @click="showEnableDisable=false">取 消</el-button>
        <el-button :type="typeEnableDisable==1?'info':'success'" :size="'mini'" @click="closeEnableDisable">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mainLimitSet from './mainLimitSet';
import mainFormConfig from './mainFormConfig';
import mainTaske from './mainTaske';
import { getCutDay } from '@/utils';

export default {
  // import引入的组件需要注入到对象中才能使用
  components: { mainLimitSet, mainFormConfig, mainTaske },
  data() {
    // 这里存放数据
    return {
      querys: {},
      pagination: {
        total: 0,
        page: 1,
        rows: 10
      },
      tableLoading: false,
      tableData: [],
      showLimitSet: false,
      showFormConfig: false,
      showTaske: false,
      taskeEdit: null,
      showRecords: false,
      recordLoading: false,
      recordData: [],
      showEnableDisable: false,
      typeEnableDisable: null,
      objEnableDisable: {}
    };
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.getTableList();
  },
  // 方法集合
  methods: {
    // 查询-重置
    searchBtn(type) {
      if (type === 1) this.pagination.page = 1;
      else this.querys = {};
      this.getTableList();
    },
    // 打开-编辑任务表单弹窗
    openTaskeBtn(row) {
      console.log('打开-编辑：任务表单弹窗', row);
      this.taskeEdit = row;
      this.showTaske = true;
    },
    // 关闭当前任务表单弹窗
    closeTaskeBtn(type) {
      this.showTaske = false;
      if (type) this.getTableList();
    },
    // 查看操作记录
    openLookRecord({ id }) {
      this.recordLoading = true;
      this.$http
        .post('/inviteTaskConfig/getTaskConfigLog.do', { id })
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            body?.map(item => {
              item.createTime = item.createTime && getCutDay(item.createTime);
            });
            this.recordData = body;
          }
          this.recordLoading = false;
          this.showRecords = true;
        })
        .catch(() => {
          this.recordLoading = false;
        });
    },
    // 打开启用禁用
    onEnableDisable(rows) {
      this.typeEnableDisable = Number(rows?.isAllow || 0);
      this.objEnableDisable = rows;
      console.log('打开启用禁用', rows);
      this.showEnableDisable = true;
    },
    // 【确定】按钮关闭启用禁用
    closeEnableDisable() {
      const { id, type, isAllow } = this.objEnableDisable;
      const param = JSON.parse(this.objEnableDisable?.param);
      if (type == 2) {
        delete param.actId;
        param.evalId = Number(param.evalId);
      }
      if (type == 3) {
        delete param.evalId;
        param.actId = Number(param.actId);
      }
      console.log('param', param);
      this.$http
        .post('/inviteTaskConfig/updateIsAllow.do', {
          id,
          type,
          isAllow: isAllow == 1 ? 0 : 1,
          param: JSON.stringify(param)
        })
        .then((res) => {
          console.log('关闭启用禁用-res', res);
          const { code } = res;
          if (code === '00') {
            this.$message({
              message: `${isAllow == 1 ? '禁用' : '启用'}成功！`,
              type: 'success'
            });
            this.getTableList();
          }
          this.showEnableDisable = false;
        })
        .catch(() => {
          this.$message({
            message: `${isAllow == 1 ? '禁用' : '启用'}失败！`,
            type: 'error'
          });
          this.showEnableDisable = false;
        });
    },
    // 获取任务列表数据
    getTableList() {
      this.tableLoading = true;
      const params = JSON.parse(JSON.stringify({ ...this.pagination, ...this.querys }));
      for (const key in params) {
        if (params[key] == '' || params[key] == null) delete params[key];
      }
      params['orderType'] = 'create_time';
      params['orderSort'] = 'DESC';
      this.$http
        .post('/inviteTaskConfig/getList.do', params)
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            const data = body?.data || [];
            data.map((item) => {
              item.typeText = '';
              switch (Number(item.type)) {
                case 1:
                  item.typeText = '注册';
                  break;
                case 2:
                  item.typeText = '测评';
                  break;
                case 3:
                  item.typeText = '报名活动';
                  break;
                case 4:
                  item.typeText = '首次发帖';
                  break;
                case 5:
                  item.typeText = '添加老师';
                  break;
                case 6:
                  item.typeText = '报读';
                  break;
                case 7:
                  item.typeText = '缴费';
                  break;
                default:
                  break;
              }
              item.allowText = Number(item.isAllow) == 1 ? '启用' : '禁用';
            });
            this.tableData = data;
            this.pagination.total = body?.recordsTotal || 0;
          }
          this.tableLoading = false;
        })
        .catch((err) => {
          this.tableLoading = false;
          console.log('表格数据-err', err);
        });
    }
  }
};
</script>

<style lang="scss" scoped>
// @import url(); 引入公共css类
.messageAggregation {
  padding: 40px 30px;

  &-forms {
    margin-bottom: 30px;

    .el-form-item {
      width: 32%;
      display: inline-block;
    }

    .forms-btn {
      padding-top: 40px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .popclass {
    height: 200px;
    overflow: hidden;
    overflow-y: scroll;
  }
}
.ed {
  text-align: center;
  line-height: 24px;
}
</style>

