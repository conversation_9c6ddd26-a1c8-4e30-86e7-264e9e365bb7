<template>
  <common-dialog
    is-full
    :title="steps.title"
    width="95%"
    :visible.sync='show'
    @open="init"
    @close='close'
  >
    <div class="dialogs-main">
      <!-- 步骤器 -->
      <el-steps class="main-steps" :active="activeInx" align-center>
        <el-step :title="steps.oneText" />
        <el-step :title="steps.twoText" />
        <el-step :title="steps.threeText" />
      </el-steps>
      <!-- 第一步：添加/编辑表单 -->
      <formStep v-show="activeInx == 1" ref="firstStepRef" :addType="addType" @setActNus="setActNus" />
      <!-- 第二步：添加/编辑表格 -->
      <secondPersonal ref="preSecondRef" :activeInx="activeInx" :addType="addType" :addParams="importParams" />
      <secondDepartment ref="depSecondRef" :activeInx="activeInx" :addType="addType" :addParams="importParams" />
      <secondWars ref="warSecondRef" :activeInx="activeInx" :addType="addType" :addParams="importParams" />
      <!-- 第三步：查看表格详情 -->
      <thirdPersonal ref="preThirdRef" :activeInx="activeInx" :addType="addType" :addParams="importParams" />
      <thirdDepartment ref="depThirdRef" :activeInx="activeInx" :addType="addType" :addParams="importParams" />
      <thirdWars ref="warThirdRef" :activeInx="activeInx" :addType="addType" :addParams="importParams" />
      <!-- 操作区域 -->
      <el-row class="main-rows">
        <el-button v-show="activeInx != 1" type="" @click="preBtn">上一步</el-button>
        <el-button v-show="activeInx != 3" type="primary" @click="nextBtn">下一步</el-button>
        <el-button v-show="activeInx == 3" type="primary" @click="submitBtn">完成</el-button>
      </el-row>
    </div>
  </common-dialog>
</template>

<script>
import formStep from './formStep';
import secondPersonal from '../secondStep/personal';
import secondDepartment from '../secondStep/department';
import secondWars from '../secondStep/wars';
import thirdPersonal from '../thirdStep/personal';
import thirdDepartment from '../thirdStep/department';
import thirdWars from '../thirdStep/wars';

export default {
  components: { formStep, secondPersonal, secondDepartment, secondWars, thirdPersonal, thirdDepartment, thirdWars },
  props: {
    addType: { type: [String, Number], default: '1' }, // 1:个人, 2:部门, 3:战队
    title: { type: String, default: '新增' },
    visible: { type: Boolean, default: false }
  },
  data() {
    return {
      show: false,
      activeInx: 1,
      importParams: {
        type: this.addType,
        childActivityId: ''
      }
    };
  },
  inject: ['newRow'],
  computed: {
    steps() { // 进步数文案
      const { isEdit } = this.newRow();
      const text = isEdit ? '编辑' : '添加';
      return {
        oneText: `${text}基础信息 `,
        title: this.addType == 1 ? `${text}个人天梯赛` : this.addType == 2 ? `${text}团队天梯赛` : `${text}战队对抗赛`,
        twoText: this.addType == 1 ? `${text}PK人员` : this.addType == 2 ? `${text}团队人员` : `${text}战队人员`,
        threeText: this.addType == 1 ? '确认PK情况' : this.addType == 2 ? '确认团队情况' : '确认战队情况'
      };
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 基础初始化：第一步
    init() {
      const that = this;
      this.$nextTick(() => {
        that.$refs?.firstStepRef?.firstInit();
      });
    },
    // 上一步：返回
    preBtn() {
      this.activeInx--;
    },
    // 设置第二步
    setActNus(obs) {
      // 第一步反馈第二步所需参数
      if (obs?.bools && this.activeInx == 1) {
        this.importParams = obs;
        setTimeout(() => {
          this.activeInx = 2;
          // 第二步，调用子级添加
          const key = `${this.addType == 1 ? 'pre' : this.addType == 2 ? 'dep' : 'war'}SecondRef`;
          const { secondInit } = this.$refs[key] || {};
          secondInit && secondInit();
        }, 0);
      }
    },
    // 下一步
    nextBtn() {
      // 第一步：调用子级表单提交
      if (this.activeInx == 1) {
        this.$refs?.firstStepRef?.submitApi();
      }
      // 已经到了第三步
      if (this.activeInx == 2) {
        const preValid = this.addType == 1 && !this.$refs.preSecondRef.tableData.length;
        const depValid = this.addType == 2 && !this.$refs.depSecondRef.tableData.length;
        const warValid = this.addType == 3 && !this.$refs.warSecondRef.tableData.length;
        // 请添加参与PK人员名单
        if (preValid || depValid || warValid) {
          this.$message.warning('请添加参与PK人员名单');
          return;
        }
        const key = `${this.addType == 1 ? 'pre' : this.addType == 2 ? 'dep' : 'war'}ThirdRef`;
        const { thirdInit } = this.$refs[key] || {};
        thirdInit && thirdInit();
        this.activeInx = 3;
      }
    },
    // 确认结果：流程结束
    submitBtn() {
      this.$message.success('提交成功!');
      this.close();
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
      this.$emit('showParent', false);
      this.$emit('refreshParent', true);
    }
  }
};
</script>

<style lang="scss">
.dialogs-main {
  margin: 50px 80px;
  overflow: hidden;
  .main-steps {
    width: 80%;
    margin: 50px auto;
  }
  .main-forms {
    width: 50%;
    margin: auto;
    .el-input-number--mini {
      width: 100%;
    }
    .el-input__inner {
      text-align: left;
    }
    .main-days {
      position: relative;
      &:after {
        content: '天后';
        position: absolute;
        right: 8px;
        top: 0;
        font-size: 12px;
        color: #C0C4CC;
      }
    }
  }
  .main-text {
    margin-right: 10px;
  }
  .main-desc {
    color: #F56C6C;
  }
  .main-rows {
    margin-top: 50px;
    text-align: center;
  }
  .add_tasget {
    .el-form-item__content {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }
    &-btn {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .btn-li {
        padding: 0 12px;
        margin: 8px 14px 8px 0;
        color: #ffffff;
        font-size: 12px;
        border-radius: 14px 14px 14px 14px;
        background-color: #62affd;
        position: relative;
      }
      .btn-icon {
        position: absolute;
        top: -5px;
        right: -5px;
        color: #fd5050;
        font-size: 14px;
        cursor: pointer;
      }
    }
    &-desc {
      width: 100%;
      margin-top: 8px;
      color: #fd5050;
    }
  }
}
</style>
