<template>
  <div class="yz-base-container">
    <el-form ref="ruleForm" :inline="true" :model="formInline" class="demo-form-inline" label-width="100px">
      <!-- <el-form-item label="账号：">
        <el-input v-model="formInline.user" placeholder="请输入账号" />
      </el-form-item>
      <el-form-item label="昵称：">
        <el-input v-model="formInline.user" placeholder="请输入昵称" />
      </el-form-item> -->
      <el-form-item label="远智编号：" prop="yzCode">
        <el-input v-model="formInline.yzCode" placeholder="请输入远智编号" />
      </el-form-item>
      <el-form-item label="话题：" prop="topicId">
        <el-select v-model="formInline.topicId" filterable placeholder="请选择话题">
          <el-option v-for="item in talk" :key="item.id" :label="item.topicName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item style="float:right">
        <el-button type="primary" @click="onSubmit">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <div style="float:right;margin-bottom:15px">
      <el-button type="primary" @click="addEdit('','add')">新增</el-button>
      <!-- <el-button class="el-icon-plus" type="primary" @click="jumpConfiguration">学堂入口跳转配置</el-button> -->
    </div>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column prop="yzCode" label="远智编号" align="center" />
      <!-- <el-table-column prop="date" label="账号（手机号）" align="center" /> -->
      <el-table-column prop="empName" label="教师" align="center" />
      <el-table-column prop="topicName" label="绑定答疑专区话题" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" style="margin-right: 10px;" @click="addEdit(scope.row,'edit')">编辑</el-button>
          <el-popconfirm title="确定是否删除吗？" @confirm='handleDelete(scope.$index, scope.row)'>
            <el-button slot="reference" type="danger" size="mini">删除</el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <div class="page">
      <el-pagination layout="total, sizes, prev, pager, next, jumper" :total="page.total" :page-sizes="[10, 20, 30, 40]" :page-size="page.pageSize" :current-page="currentPage4" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
    <Add :addVisible='addVisible' :type='type' :editId="editId" @addVisible='addVisible=false' @editId="editId=null" />
    <Configuration :configurationVisible='configurationVisible' @configurationVisible='configurationVisible=false' />
  </div>
</template>

<script>
import Add from './add.vue';
import Configuration from './configuration.vue';
export default {
  components: { Add, Configuration },
  data() {
    return {
      tableData: [],
      currentPage4: 1,
      formInline: {},
      configurationVisible: false,
      addVisible: false,
      type: 'add',
      page: {
        total: 1,
        pageNum: 1,
        pageSize: 10
      },
      editId: null,
      talk: []
    };
  },
  created() {
    this.list();
    this.talkList();
  },
  methods: {
    list() {
      const params = {
        yzCode: this.formInline.yzCode,
        topicId: this.formInline.topicId,
        pageNum: this.page.pageNum,
        pageSize: this.page.pageSize
      };
      this.$http.post('/qaConfig/list', params, { json: true }).then(res => {
        if (res.ok) {
          console.log(res, '000');
          this.tableData = res.body.data;
          this.page.total = res.body.recordsTotal;
        }
      });
    },
    talkList() {
      const params = {
        topicType: 3
      };
      this.$http.post(`/circleDynamic/selTopic`, params).then(res => {
        console.log(res, '000');
        this.talk = res.body;
      });
    },
    onSubmit() {
      this.list();
    },
    reset() {
      this.$refs.ruleForm.resetFields();
      this.list();
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.list();
    },
    handleCurrentChange(val) {
      this.page.pageNum = val;
      this.list();
    },
    handleDelete(index, row) {
      console.log(index, row);
      this.$http.get(`/qaConfig/delete/${row.id}`).then(res => {
        if (res.ok) {
          this.$message({ message: '删除成功', type: 'success' });
          this.list();
        }
      });
    },
    jumpConfiguration() {
      this.configurationVisible = true;
    },
    addEdit(row, type) {
      this.addVisible = true;
      if (type == 'add') {
        this.type = '新增';
      } else {
        this.type = '编辑';
        this.editId = row.id;
      }
    }
  }
};
</script>

<style lang = "scss" scoped>
.el-input {
    width: 120%;
}
.page{
  display: flex;
  justify-content: center;
  margin-top: 15px;
}
</style>
