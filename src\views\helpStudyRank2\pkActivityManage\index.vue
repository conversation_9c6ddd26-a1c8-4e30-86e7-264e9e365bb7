<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >

      <el-form-item label='活动名称' prop='pkName'>
        <el-input v-model="form.pkName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='创建人' prop='createUser'>
        <el-input v-model="form.createUser" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='活动状态' prop='status'>
        <el-select
          v-model="form.status"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option label="全部" value="1" />
          <el-option label="未开始" value="2" />
          <el-option label="进行中" value="3" />
          <el-option label="已结束" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label='活动起止时间' prop='activityTime'>
        <el-date-picker
          v-model="form.activityTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>
    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="success" size="small" icon="el-icon-plus" plain @click="addAactivity">活动</el-button>
      <el-button type="primary" size="small" @click="exportData">导出活动数据</el-button>
      <el-button type="danger" size="small" plain @click="reduceAchieve(1)">学历教育业绩调整</el-button>
      <el-button type="danger" size="small" plain @click="reduceAchieve(0)">职业教育业绩调整</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      v-sticky-scroller
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="选择" class="radioBox" width="55">
        <template slot-scope="scope">
          <el-radio
            v-model="checkedAct"
            :label="scope.row.pkId"
            style="color: #fff;padding-left: 10px; margin-right: -15px;"
            @change.native="getCurrentRow(scope.row)"
          >{{ '' }}</el-radio>
        </template>
      </el-table-column>
      <el-table-column
        prop="pkNumber"
        label="活动编码"
        width="120px"
        align="center"
      />
      <el-table-column prop="pkName" label="活动名称" width="150px" align="center" />
      <el-table-column
        prop="activityTime"
        label="活动起止时间"
        align="center"
        width="265px"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.startTime | transformTimeStamp }} 至 {{ scope.row.endTime | transformTimeStamp }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="listChildActivity"
        label="PK内容"
        width="250px"
      >
        <template slot-scope="scope">
          <p v-for="(item,index) in scope.row.listChildActivity" :key="index">
            <el-link class="font-12" type="primary" @click.prevent="showPkRank(item)">{{ item.pkChildName }}</el-link>
          </p>
        </template>
      </el-table-column>
      <el-table-column width="100px" label="活动文件" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.pkAttr">
            <el-button type="text" size="mini" @click.prevent="previewClick(scope.row.pkAttr)">预览</el-button>
            <el-button type="text" size="mini" @click.prevent="actDownload(scope.row.pkAttr)">下载</el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="活动状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.status | status }}
        </template>
      </el-table-column>
      <el-table-column prop="enable" label="是否启用" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.enable==1" type="success">已启用</el-tag>
          <el-tag v-else type="danger">已禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createUser" label="创建人" align="center" />
      <el-table-column prop="createTime" width="150px" label="创建时间" align="center" />
      <el-table-column prop="updateUser" label="最后修改人" width="100px" align="center" />
      <el-table-column
        prop="updateTime"
        label="最后修改时间"
        width="150px"
        align="center"
      />
      <el-table-column prop="date" label="操作" fixed="right" align="center" width="150px">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="addPK(scope.row)">添加PK</el-button>
            <el-button type="text" @click="editStatus(scope.row)">{{ scope.row.enable==1?'禁用':"启用" }}</el-button>
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 弹窗 - 新增编辑 -->
    <update-dialog :id='currentEditId' :title="updateDialogTitle" :visible.sync="udVisible" @refresh-list="getTableList" />
    <!-- 弹窗 - 业绩调整 -->
    <import-achieve-dialog :id='currentEditId' :type="reduceType" :params="reduceParmas" :title="reduceDialogTitle" :visible.sync="raVisible" />
    <!-- 弹窗 - 添加PK -->
    <addpk-dialog :visible.sync="addTypeVisible" :title="addTypeDialogTitle" @refresh-list="getTableList" />
    <!-- 弹窗 - 查看个人PK榜 -->
    <personal-rank-dialog :visible.sync="personalRankVisible" :title="personalRankTitle" @refresh-list="getTableList" />
    <!-- 弹窗 - 查看战区PK榜 -->
    <war-rank-dialog :id='currentEditId' :visible.sync="warRankVisible" :title="warRankTitle" @refresh-list="getTableList" />
    <!-- 弹窗 - 查看团队pk榜 -->
    <depart-rank-dialog :id='currentEditId' :visible.sync="departRankVisible" :title="departRankTitle" @refresh-list="getTableList" />
  </div>
</template>

<script>
import { handleDateControl, formatTimeStamp } from '@/utils';
import updateDialog from './update-dialog';
import importAchieveDialog from './import-achieve-dialog';
import addpkDialog from './addPK/addpk-dialog.vue';
import personalRankDialog from './pkRank/personal-rank-dialog';
import warRankDialog from './pkRank/war-rank-dialog.vue';
import departRankDialog from './pkRank/depart-rank-dialog.vue';
import { ossUri, bmsURL } from '@/config/request';
import buildZipDownload from '@/utils/zip';
import downFile from '@/utils/downFile';
import { StickyScroller } from '@cell-x/el-table-sticky';
export default {
  components: { updateDialog, importAchieveDialog, addpkDialog, personalRankDialog, warRankDialog, departRankDialog },
  directives: {
    StickyScroller: new StickyScroller({ offsetBottom: '15px' }).init()
  },
  filters: {
    status(value) {
      if (!value) return;
      const data = {
        '1': '全部',
        '2': '未开始',
        '3': '进行中',
        '4': '已结束'
      };
      return data[value];
    }
  },
  data() {
    return {
      checkedAct: '',
      form: {
        pkName: '',
        status: '',
        createUser: '',
        activityStatus: '',
        activityTime: ''
      },
      exportRow: null,
      statusObj: {
        '1': '全部',
        '2': '未开始',
        '3': '进行中',
        '4': '已结束'
      },
      tableData: [],
      tableLoading: true,
      udVisible: false,
      previewShow: false, // 预览
      raVisible: false, // 调整业绩
      addTypeVisible: false, // 添加PK
      personalRankVisible: false, // 个人pk榜show
      pkChildId: '', // 子活动id
      warRankVisible: false, // 战区pk榜show
      departRankVisible: false, // 团队pk榜show
      currentEditId: null,
      pkActId: '',
      pkChildType: '',
      updateDialogTitle: '',
      reduceDialogTitle: '', // 调整业绩标题
      addTypeDialogTitle: '',
      personalRankTitle: '', // 个人pk榜
      warRankTitle: '', // 战区pk榜
      departRankTitle: '', // 团队pk榜
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      currentRow: null,
      reduceParmas: {},
      reduceType: '4'
    };
  },
  provide() {
    return {
      newRow: () => {
        return this.currentRow;
      },
      addPkActId: () => {
        return this.pkActId;
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    handleSelectionChange(selects) {
      this.selects = selects;
    },
    // 获取列表数据
    getTableList() {
      const data = this.handleQueryParams();
      this.$http.post('/pkActivity/pkActivityList', data).then(res => {
        const { fail, body } = res;
        const data = body?.data || [];
        data.map(item => {
          item['activityTime'] = this.formatDate(item.startTime) + '~' + this.formatDate(item.endTime);
          item['updateTime'] = formatTimeStamp(item.updateTime, 'YYYY-MM-DD HH:MM:SS');
          item['createTime'] = formatTimeStamp(item.createTime, 'YYYY-MM-DD HH:MM:SS');
        });
        this.tableData = data;
        this.pagination.total = body.recordsTotal;
        this.tableLoading = false;
      });
    },
    // data参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      if (formData.status === '') formData.status = 1;
      const date = handleDateControl(formData.activityTime);
      formData.startTimeStr = date[0];
      formData.endTimeStr = date[1];

      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 启用禁用
    editStatus(row) {
      row.enable = row.enable + '';
      const data = {
        enable: row.enable === '1' ? '2' : '1',
        pkActId: row.pkId
      };
      this.$post('updateActivityEnable', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
        this.getTableList();
      });
    },
    // 添加活动
    addAactivity() {
      this.currentEditId = '';
      this.updateDialogTitle = '添加活动';
      this.udVisible = true;
    },
    // 编辑
    handleEdit(row) {
      this.currentEditId = row.pkId;
      this.updateDialogTitle = '编辑';
      this.udVisible = true;
    },
    // 导出活动数据
    exportData() {
      if (!this.exportRow) {
        this.$message.error('请先选择需要导出的活动');
        return;
      }
      if (Array.isArray(this.exportRow.listChildActivity)) {
        const zipName = this.exportRow.pkName;
        const files = []; // 存放要打包的文件

        this.exportRow.listChildActivity.forEach(item => {
          files.push({
            fileUrl: bmsURL + '/pkChildActivity/pkChildActivityPerformanceExportV2?pkChildActivityId=' + item.pkChildId,
            fileName: item.pkChildName + '.xlsx'
          });
        });

        buildZipDownload(files, zipName);
      } else {
        this.$message({
          message: '该活动没有数据可导出',
          type: 'warning'
        });
      }
    },
    // 时间戳转换方法    date:时间戳数字
    formatDate(item) {
      var date = new Date(item);
      var YY = date.getFullYear();
      var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);
      var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
      var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours());
      var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes());
      var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
      return YY + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss;
    },
    // 调整业绩
    reduceAchieve(typs = 0) {
      if (this.checkedAct === '' || this.checkedAct === null) {
        this.$message.error('请先选择需要调整业绩的活动');
        return;
      }
      // 存在 “个人招生系数显示”（coefficientSwitch） 为 1 的子活动，则不能加调整业绩
      if (this.exportRow.listChildActivity.some(v => v.coefficientSwitch === 1)) {
        this.$message.warning('影响个人招生系数展示，此活动不能加调整业绩');
        return;
      }
      this.reduceType = typs ? '4' : '5';
      this.reduceDialogTitle = typs ? '导入学历教育业绩调整' : '导入职业教育业绩调整';
      this.reduceParmas = {
        pkActId: this.checkedAct
      };
      // 职业教育
      if (!typs) {
        this.reduceParmas = {
          ... this.handleQueryParams(),
          pkActId: this.checkedAct
        };
      }
      this.raVisible = true;
    },
    addPK(row) {
      this.addTypeDialogTitle = '添加PK';
      this.currentRow = {
        isEdit: false,
        ...row
      };
      this.pkActId = row.pkId;
      this.addTypeVisible = true;
    },
    showPkRank(row) {
      this.currentRow = {
        isEdit: true,
        ...row
      };
      if (row.pkChildType === '1') {
        this.personalRankTitle = '个人pk榜';
        this.personalRankVisible = true;
        return;
      }
      if (row.pkChildType === '2') {
        this.departRankTitle = '团队pk榜';
        this.departRankVisible = true;
        return;
      }

      if (row.pkChildType === '3') {
        this.warRankTitle = '战区pk榜';
        this.warRankVisible = true;
      }
    },
    getCurrentRow(row) {
      this.exportRow = row;
    },
    previewClick(file) {
      window.open(ossUri + file);
    },
    actDownload(file) {
      downFile(ossUri + file, file);
    },
    closePreview() {
      this.previewShow = false;
    }

  }
};
</script>

<style lang = "scss" scoped>
.font-12 {
  font-size: 12px;
}

::v-deep .text-top {
  padding: 0;
  vertical-align: top;
}

</style>

