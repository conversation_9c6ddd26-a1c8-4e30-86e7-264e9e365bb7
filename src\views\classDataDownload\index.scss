@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 #f26662;
  }
  100% {
    box-shadow: 0 0 0 12px transparent;
  }
}
.page-box {
  height: calc(100vh - 30px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.yz-base-container {
  background: #ffffff;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  user-select: none;
  flex: 1;
  overflow: hidden;
  .con-grid {
    width: 1020px;
    height: 100%;
    margin: 0 auto;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1;
    .change-grid {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
  }
  .header-container {
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &.bg {
      padding: 0 30px;
      background: #f6f6f6;
    }
  }
  .btn-tabs {
    .ac {
      color: #fff;
      background: linear-gradient(
        135deg,
        #f09190 0%,
        #f07877 66%,
        #f06e6c 100%
      );
    }
    .btn {
      padding: 8px 13px;
      font-size: 14px;
      display: inline-block;
      border-radius: 8px;
      border: 1px solid #e8e8e8;
      margin-right: 12px;
      cursor: pointer;
      transition: transform 0.35s;
      &:hover {
        transform: translateY(-2px);
      }
    }
  }
  .crumb {
    margin: 20px 0;
    color: rgba(24, 7, 7, 0.5);
    font-size: 16px;
    .cru-ac {
      color: #180707;
      font-weight: bold;
    }
    .cru-ac2 {
      cursor: pointer;
    }
  }
  .grid-down {
    border-radius: 4px;
    height: 100%;
    flex: 1;
    overflow: hidden;

    .top-b {
      width: 100%;
      height: 50px;
      background: #f6f6f6;
      border-radius: 4px 4px 0px 0px;
      display: flex;
      align-items: center;
      padding-left: 15px;
      img {
        width: 32px;
        height: 32px;
        cursor: pointer;
      }
      .all-s {
        font-size: 16px;
        font-weight: bold;
        color: #170606;
        flex: 1;
      }
      .r-b {
        padding: 7px 18px;
        background: #f26662;
        border-radius: 4px;
        border: 1px solid #f26662;
        font-size: 14px;
        color: #fff;
        cursor: pointer;
        margin-right: 15px;
        text-align: center;
        &:hover {
          animation: pulse 1s infinite;
        }
      }
    }
    .t-c-pd {
      padding: 14px 20px;
    }
    .d-b {
      height: 69vh;
      overflow-y: auto;

      .dl-box {
        display: flex;
        flex-flow: wrap;
        user-select: none;
        padding-bottom: 120px;
        dl {
          width: 200px;
          height: 180px;
          border-radius: 4px;
          position: relative;
          box-sizing: border-box;
          cursor: pointer;
          margin-right: 20px;
          transition: transform 0.35s;
          &:hover {
            transform: translateY(-5px);
            background: rgba(51, 51, 51, 0.03);
          }
          dt {
            position: absolute;
            right: 10px;
            top: 10px;
            img {
              width: 32px;
              height: 32px;
            }
            .icon-d-b {
              color: #f26662;
              display: flex;
              align-items: center;
              img {
                width: 24px;
                height: 24px;
              }
            }
          }
          dd {
            text-align: center;
            margin: 0;
            box-sizing: border-box;
            img {
              width: 111px;
              height: 111px;
              padding: 0;
            }
          }
          .dd-m {
            margin-top: 14px;
            display: inline-block;
            width: 100%;
            text-align: center;
          }
          .txt {
            margin-top: 9px;
            color: #170606;
            font-size: 16px;
            padding: 0 12px;
          }
          .txt-elp {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
            display: inline-block;
          }
        }
      }
    }
  }

  ::v-deep .el-tabs__item {
    font-size: 20px;
  }
  ::v-deep .el-tabs__item:hover {
    color: #f26662;
  }
  ::v-deep .is-active {
    color: #f26662;
    font-weight: 600;
  }
  ::v-deep .el-tabs__active-bar {
    background-color: #f26662;
  }
}
.file-l-b {
  flex: 1;
  height: 100%;
  overflow-y: auto;
}
.no-data-b {
  width: 100%;
  height: 600px;
  text-align: center;
  img {
    width: 200px;
    height: 200px;
    margin-top: 180px;
  }
  p {
    margin-top: 10px;
  }
}
.select-grid {
  text-align: right;
  border-radius: 4px 4px 0px 0px;
}
.drawer-c {
  height: calc(100vh - 100px);
  padding: 0 10px;
  overflow-y: auto;
  li {
    border-bottom: 1px solid #fbfbfb;
    padding: 6px 0 1px;
  }
}

.mr-10 {
  margin-right: 10px;
}

.flag-new {
  position: absolute;
  top: 0;
  right: 5px;
  width: 20px;
  height: 20px;
}

::v-deep .btn-batch {
  &:hover {
    animation: pulse 1s infinite;
  }
}

.drawer-c,
.d-b,
.file-l-b,
.el-table,
::v-deep .el-table__body-wrapper,
::v-deep .el-scrollbar__wrap {
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }
  &::-webkit-scrollbar-thumb {
    background: rgb(194, 193, 193);
  }
  &::-webkit-scrollbar-track {
    width: 5px;
    height: 5px;
    background: #f6f6f6;
  }
}

::v-deep .table-header {
  background-color: #f6f6f6 !important;
  color: #000;

  & .cell {
    overflow: visible;
  }
}

::v-deep .el-table {
  &.el-table::before {
    display: none;
  }

  td.el-table__cell {
    border-bottom: none;
  }
}
