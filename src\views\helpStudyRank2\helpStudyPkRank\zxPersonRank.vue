<template>
  <div class="yz-base-container">
    1
    6
    <div v-loading="tableLoading" element-loading-background="rgba(0, 0, 0, 0.8)" class="connect">
      <div v-if="empTeam" class="myRank">
        <div class="personHeadImg">
          <img src="@/assets/imgs/helpStudyPkRank/myRank.png" alt="" />
        </div>
        <img class="depart-head-bg" src="@/assets/imgs/helpStudyPkRank/depart-head-bg.png" alt="" />
        <div class="l">
          <div class="imgBox">
            <el-avatar class="avatar-box" :size="76" :src="teamAvatar | formatOssImgUrl">
              <img src="@/assets/imgs/helpStudyPkRank/defaultPK1.png" />
            </el-avatar>
            <div class="myRankIcon">
              <img v-if="empTeam.pkRanking === 1" src="@/assets/imgs/helpStudyPkRank/<EMAIL>" alt="" />
              <img v-else-if="empTeam.pkRanking === 2" src="@/assets/imgs/helpStudyPkRank/<EMAIL>" alt="" />
              <img v-else-if="empTeam.pkRanking === 3" src="@/assets/imgs/helpStudyPkRank/<EMAIL>" alt="" />
            </div>
          </div>
          <div class="myInfo">
            <img class="no" src="@/assets/imgs/helpStudyPkRank/NO..png" alt="" />
            <span class="rankNum">{{ empTeam.pkRanking }}</span>
            <div>
              {{ empTeam.empName }} <span>{{ empTeam.jobTitle }}</span>
            </div>
          </div>
        </div>

        <div class="total">
          <div class="total-l">
            <div>
              <p>活动合计：</p>
              <p>{{ empTeam.totalOrders || 0 }}</p>
            </div>
            <div>
              <p>{{ pkTypeToText }}</p>
              <p>{{ empTeam.totalScore || 0 }}</p>
            </div>
            <div>
              <p>今日合计：</p>
              <p>{{ empTeam.todayOrders || 0 }}</p>
            </div>
          </div>
          <div class="total-r">
            <el-carousel height="110px" :autoplay="false" arrow="never">
              <el-carousel-item>
                <div class="total-r-carousel-item">
                  <p>
                    <span>今日成教：<i>{{ empTeam.adultEduToday || 0 }}</i></span>
                    <span>今日国开：<i>{{
                      empTeam.nationalOpenToday || 0
                    }}</i></span>
                    <span>今日全日制：<i>{{ empTeam.fullTimeToday || 0 }}</i></span>
                    <span>今日自考：<i>{{ empTeam.selfStudyToday || 0 }}</i></span>
                  </p>
                  <p>
                    <span>活动成教：<i>{{ empTeam.adultEduTotal || 0 }}</i></span>
                    <span>活动国开：<i>{{
                      empTeam.nationalOpenTotal || 0
                    }}</i></span>
                    <span>活动全日制：<i>{{ empTeam.fullTimeTotal || 0 }}</i></span>
                    <span>活动自考：<i>{{ empTeam.selfStudyTotal || 0 }}</i></span>
                  </p>
                </div>
              </el-carousel-item>
              <el-carousel-item>
                <div class="total-r-carousel-item">
                  <p>
                    <span>今日研究生：<i>{{
                      empTeam.postgraduateToday || 0
                    }}</i></span>
                    <span>今日职业教育：<i>{{
                      empTeam.vocationalEduToday || 0
                    }}</i></span>
                    <span>今日海外教育：<i>{{
                      empTeam.overseasEduToday || 0
                    }}</i></span>
                  </p>
                  <p>
                    <span>活动研究生：<i>{{
                      empTeam.postgraduateTotal || 0
                    }}</i></span>
                    <span>活动职业教育：<i>{{
                      empTeam.vocationalEduTotal || 0
                    }}</i></span>
                    <span>活动海外教育：<i>{{
                      empTeam.overseasEduTotal || 0
                    }}</i></span>
                  </p>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </div>
      <div class="personImg">
        <img src="@/assets/imgs/helpStudyPkRank/personRank.png" alt="" />
        <el-tooltip v-if="coefficientSwitch" v-model="showCoefficientTip" effect="dark" placement="left" :manual="true">
          <div slot="content">
            预估个人招生系数：系数仅供参考，年终核算以最终核算规则为准。如成交
            <br />缴费时，初始邀约人（在职）和招生老师不一致，分成比例为3:7……等等。
          </div>
          <i class="el-icon-s-order" @click="handleCoefficientTip"></i>
        </el-tooltip>
      </div>
      <!-- 表格 -->
      <div class="main-table-wrap">
        <el-table
          ref="mainTable"
          v-sticky-header
          border
          size="small"
          style="width: 100%"
          header-cell-class-name="table-cell-header"
          :data="tableData"
          class="main-table"
          :header-row-class-name="headerStyle"
        >
          <el-table-column width="100" prop="readPlanName" type="index" label="排名" align="center" fixed>
            <template v-slot="scope">
              <div v-if="scope.$index === 0">
                <img class="crown-emblem" src="@/assets/imgs/helpStudyPkRank/NO1.png" alt="" />
              </div>
              <div v-else-if="scope.$index === 1">
                <img class="crown-emblem" src="@/assets/imgs/helpStudyPkRank/NO2.png" alt="" />
              </div>
              <div v-else-if="scope.$index === 2">
                <img class="crown-emblem" src="@/assets/imgs/helpStudyPkRank/NO3.png" alt="" />
              </div>
              <div v-else>{{ scope.$index + 1 }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="dpName" width="160" label="部门" align="center" fixed />
          <el-table-column prop="teamName" label="姓名" align="center" fixed width="90" />
          <el-table-column prop="jobTitle" label="职位" align="center" fixed width="90" />

          <template v-if="pkRange.includes('1')">
            <el-table-column
              v-if="isShowTableMoreCol"
              key="adultEduToday"
              prop="adultEduToday"
              label="今日成教"
              align="center"
            />
            <el-table-column key="adultEduTotal" prop="adultEduTotal" label="活动成教" align="center" />
          </template>
          <template v-if="pkRange.includes('2')">
            <el-table-column
              v-if="isShowTableMoreCol"
              key="nationalOpenToday"
              prop="nationalOpenToday"
              label="今日国开"
              align="center"
            />
            <el-table-column key="nationalOpenTotal" prop="nationalOpenTotal" label="活动国开" align="center" />
          </template>
          <template v-if="pkRange.includes('3')">
            <el-table-column
              v-if="isShowTableMoreCol"
              key="fullTimeToday"
              prop="fullTimeToday"
              label="今日全日制"
              align="center"
            />
            <el-table-column key="fullTimeTotal" prop="fullTimeTotal" label="活动全日制" align="center" />
          </template>
          <template v-if="pkRange.includes('4')">
            <el-table-column
              v-if="isShowTableMoreCol"
              key="selfStudyToday"
              prop="selfStudyToday"
              label="今日自考"
              align="center"
            />
            <el-table-column key="selfStudyTotal" prop="selfStudyTotal" label="活动自考" align="center" />
          </template>
          <template v-if="pkRange.includes('5')">
            <el-table-column
              v-if="isShowTableMoreCol"
              key="postgraduateToday"
              prop="postgraduateToday"
              label="今日研究生"
              align="center"
            />
            <el-table-column key="postgraduateTotal" prop="postgraduateTotal" label="活动研究生" align="center" />
          </template>
          <template v-if="pkRange.includes('6')">
            <el-table-column
              v-if="isShowTableMoreCol"
              key="vocationalEduToday"
              prop="vocationalEduToday"
              label="今日职业教育"
              align="center"
              width="92"
            />
            <el-table-column
              key="vocationalEduTotal"
              prop="vocationalEduTotal"
              label="活动职业教育"
              align="center"
              width="92"
            />
          </template>
          <template v-if="pkRange.includes('7')">
            <el-table-column
              v-if="isShowTableMoreCol"
              key="overseasEduToday"
              prop="overseasEduToday"
              label="今日海外教育"
              align="center"
              width="92"
            />
            <el-table-column key="overseasEduTotal" prop="overseasEduTotal" label="活动海外教育" align="center" width="92" />
          </template>

          <el-table-column prop="performanceDeduction" label="调整业绩" align="center">
            <template v-slot="scope">
              <el-link class="link" @click="openDetailsDialog('lrDialogShow', scope.row)">{{
                scope.row.performanceDeduction }}</el-link>
              <i class="icon-arrow-right"></i>
            </template>
          </el-table-column>
          <el-table-column prop="totalOrders" label="活动合计" align="center">
            <template v-slot="scope">
              <el-link class="link" @click="openDetailsDialog('resultsDialogShow', scope.row)">{{ scope.row.totalOrders
              }}</el-link>
              <i class="icon-arrow-right"></i>
            </template>
          </el-table-column>

          <el-table-column v-if="coefficientSwitch" prop="coefficient" label="预估个人招生系数" align="center" width="120" />

          <!-- 普通目标 普通目标可以存在多个-->
          <template v-if="targetType === 1 && targetConfigList.length">
            <el-table-column
              v-for="(item, index) in targetConfigList"
              :key="index"
              :label="item"
              align="center"
              width="150"
            >
              <template slot-scope="scope">
                <target-progress
                  :target="scope.row.totalScore"
                  :total="scope.row.targetConfigList &&
                    scope.row.targetConfigList[index]
                  "
                />
              </template>
            </el-table-column>
          </template>

          <!-- 奖励目标 奖励目标只存在1个-->
          <template v-if="targetType === 2">
            <el-table-column label="达成奖励目标" align="center" width="150">
              <template slot-scope="scope">
                <target-progress :target="scope.row.totalScore" :total="rewardTarget" />
              </template>
            </el-table-column>
            <el-table-column prop="achievementTime" label="达成奖励时间" align="center" width="135">
              <template slot-scope="scope">
                <span>{{
                  scope.row.achievementTime | transformTimeStamp
                }}</span>
              </template>
            </el-table-column>
          </template>

          <!-- 助学积分、助学人数、活动人均 -->
          <el-table-column prop="totalScore" align="center">
            <template slot="header">
              <span class="score-column-color">{{ pkTypeToText }}</span>
            </template>

            <template slot-scope="scope">
              <span class="score-column-color">{{ scope.row.totalScore }}</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 表格收起/展开按钮 -->
        <div v-show="tableData.length && !tableLoading" class="toggle-button-box">
          <img class="toggle-button" :src="toggleIcon" @click="toggleTableCol" />
        </div>
      </div>
    </div>

    <!-- 回到顶部按钮 -->
    <div v-show="showBackToTop" class="back-to-top" @click="scrollToTop">
      <i class="el-icon-arrow-up"></i>
    </div>

    <!-- 活动合计详情弹窗 -->
    <results-dialog :visible.sync="resultsDialogShow" :query="currentLookTeam" />

    <!-- 减业绩详情弹窗 -->
    <loss-results-dialog :visible.sync="lrDialogShow" :query="currentLookTeam" />
  </div>
</template>

<script>
import ResultsDialog from '../unionDetailed/results-dialog';
import LossResultsDialog from '../unionDetailed/loss-results-dialog';
import targetProgress from './components/targetProgress.vue';
import { StickyHeader } from '@cell-x/el-table-sticky';

export default {
  directives: {
    // 表格吸顶
    StickyHeader: new StickyHeader({
      offsetTop: 0,
      offsetBottom: '30px'
    }).init()
  },
  components: { ResultsDialog, LossResultsDialog, targetProgress },
  props: {
    row: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      tableLoading: false,
      lrDialogShow: false, // 减业绩详情弹窗
      resultsDialogShow: false, // 活动合计详情弹窗
      currentLookTeam: {}, // 当前查看的团队信息
      teamAvatar: '', // 头像
      lengths: 1, // 跑马灯页数
      isShowTableMoreCol: true, // 是否显示更多列
      showCoefficientTip: false, // 是否显示系数提示框
      showBackToTop: false, // 是否显示回到顶部按钮

      endTime: 0, // 结束时间戳
      pkType: 0, // 1：助学积分 2：助学人数 3：活动人均
      pkRange: [], // pk范围，1：成教 2：国开 3：全日制 4：自考 5：研究生 6：职业教育 7：海外教育
      targetType: 0, // 1: 普通目标, 2: 奖励目标
      targetConfigList: [], // 普通目标的配置
      rewardTarget: 0, // 奖励目标的总数
      coefficientSwitch: false, // 个人系数开关

      empTeam: null, // 本人数据
      tableData: []
    };
  },
  computed: {
    toggleIcon() {
      return !this.isShowTableMoreCol
        ? require('@/assets/imgs/helpStudyPkRank/table-retract.png')
        : require('@/assets/imgs/helpStudyPkRank/table-expand.png');
    },
    pkTypeToText() {
      const pkTypeEnum = {
        1: '助学积分',
        2: '助学人数',
        3: '活动人均'
      };
      return pkTypeEnum[this.pkType] || '/';
    }
  },
  watch: {
    pkType() {
      this.doLayout();
    }
  },
  mounted() {
    // 添加滚动监听
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    // 移除滚动监听
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 处理滚动事件
    handleScroll() {
      this.showBackToTop = window.pageYOffset > 500;
    },
    // 滚动到顶部
    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    },
    // 根据接口和storage判断是否显示个人招生系数提示
    getLocalStorageToShowTip() {
      if (
        this.coefficientSwitch &&
        localStorage.getItem('showCoefficientTip') === null
      ) {
        setTimeout(() => {
          this.showCoefficientTip = true;
        }, 1000);
      }
    },
    init() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.getChildActivityInfo(this.row.pkChildId);
      this.getMyPkStream(this.row.pkChildId);
      this.getPkStreamV2(this.row.pkChildId);
    },
    // 获取活动信息
    getChildActivityInfo(pkChildId) {
      this.$post('getChildActivityInfo', { pkChildId }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.pkType = body.pkType;
          this.endTime = body.endTime;
          this.$emit('endTime', body.endTime);
          this.pkRange = body.pkRange.split(',');
          this.targetType = body.targetType;
          this.targetConfigList = body.targetConfigList;
          this.rewardTarget = body.rewardTarget;
          this.coefficientSwitch = Boolean(body.coefficientSwitch);
          this.getLocalStorageToShowTip();
          this.getEmpAvatarv(body.empId);
        }
      });
    },
    // 获取我的pk流水
    getMyPkStream(pkChildId) {
      this.$post('getMyPkStream', { pkChildId }).then(async(res) => {
        const { fail, body } = res;
        if (!fail) {
          this.empTeam = body;
        }
      });
    },
    // 获取pk流水（助学榜单用）
    getPkStreamV2(pkChildId) {
      this.tableLoading = true;
      this.$post('getPkStreamV2', { pkChildId })
        .then(async(res) => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body;
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 获取团队/个人头像
    getEmpAvatarv(empId) {
      this.$post('getEmpAvatarv', { empId }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.teamAvatar = body?.headShot || '';
        }
      });
    },
    // 显示招生系数提示框
    handleCoefficientTip() {
      this.showCoefficientTip = !this.showCoefficientTip;
      if (localStorage.getItem('showCoefficientTip') === null) {
        localStorage.setItem('showCoefficientTip', '1');
      }
      this.doLayout();
    },
    doLayout() {
      this.$nextTick(() => {
        this.$refs.mainTable.doLayout();
      });
    },
    // 表格收起/展开更多列
    toggleTableCol() {
      this.isShowTableMoreCol = !this.isShowTableMoreCol;
      this.doLayout();
    },
    // 打开详情弹窗
    openDetailsDialog(field, item) {
      this.currentLookTeam = {
        pkActId: this.row.pkActId,
        pkChildId: item.pkChildId,
        pkType: this.pkType,
        pkRange: this.pkRange,
        isPerson: 1,
        requestUrl: field === 'lrDialogShow' ? 1 : 0,
        dialogTitle: field === 'lrDialogShow' ? item?.empName : item?.teamName,
        newTeamId: item.teamId
      };
      this[field] = true;
    },
    // 刷新
    refresh() {
      if (this.row.pkChildId) {
        this.init();
      }
    },
    headerStyle() {
      return 'tableStyle';
    }
  }
};
</script>

<style lang="scss" scoped>
/* 定义变量 */
$table-header-border-bottom: 5px; // 表头底部间距
$table-hover-bg-color: linear-gradient(270deg,
    #341f7b 0%,
    #452f9e 100%); // 表格鼠标悬停背景颜色

/* 基础容器样式 */
.yz-base-container {
  background-color: #180e36;
  color: #fff;
  overflow: unset !important;
  min-height: unset !important;
}

/* 排名图标样式 */
.crown-emblem {
  width: 70px;
  height: 60px;
}

/* 选项卡样式 */
.item {
  display: inline-block;
  width: 90px;
  height: 50px;
  line-height: 50px;
  transform: skewX(-30deg);
  margin-left: 20px;
  margin-top: 20px;
  background-color: #201e53;
  cursor: pointer;
  color: #fff;
  box-shadow: 0px 0px 10px 0px rgba(95, 153, 254, 0.6);
  border-radius: 2px;
  border: 1px solid #5f99fe66;

  >div {
    text-align: center;
    transform: skewX(30deg);
  }
}

/* .item:hover{
  background-image: radial-gradient(rgba(95, 153, 254, 0.4) 0%, rgba(73, 119, 254, 0.8) 100%);
} */

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;

  .l,
  .r {
    flex-grow: 1;
  }

  .center {
    flex-grow: 2;

    >h1 {
      background: linear-gradient(180deg, #faeee3 0%, #d9b271 100%);
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
    }
  }

  .r {
    position: relative;

    p {
      margin-top: 60px;
      font-size: 14px;
      text-align: right;
    }
  }

  .refresh {
    position: absolute;
    right: 0;
    top: 20px;
    width: 60px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
    background-image: radial-gradient(rgba(124, 203, 0, 0.3) 0%,
        rgba(124, 203, 0, 0.5) 100%);
    border-radius: 2px;
    border: 1px solid rgba(124, 203, 0, 0.6);

    div {
      font-size: 12px;
    }
  }
}

.itemStyle {
  background-image: radial-gradient(rgba(95, 153, 254, 0.4) 0%,
      rgba(73, 119, 254, 0.8) 100%);
}

/* 主容器样式 */
.connect {
  width: 100%;
  border: 1px solid #58516e;
  padding: 40px 20px;
  overflow: unset;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 5px;
}

/* 个人排名区域样式 */
.myRank {
  width: 100%;
  height: 138px;
  background: linear-gradient(90deg,
      rgba(234, 105, 69, 0.7) 0%,
      rgba(255, 149, 69, 0.05) 100%);
  border-radius: 2px;
  margin-bottom: 20px;
  padding-left: 20px;
  box-sizing: border-box;
  position: relative;
  border: 1px solid #ddd;
  border-image: linear-gradient(45deg, #b47a72 30%, #2e2043) 50 50;
  display: flex;
  align-items: center;
  justify-content: space-between;

  /* 个人排名头部图片 */
  .personHeadImg {
    position: absolute;
    top: 0;
    left: -1px;
    transform: translateY(-50%);
    width: 230px;
    height: 36px;
  }

  .depart-head-bg {
    position: absolute;
    width: 170px;
    height: 138px;
    top: 0%;
    left: 18%;
  }

  /* 左侧区域 */
  .l {
    display: flex;
    align-items: center;
    position: relative;

    /* 头像区域 */
    .imgBox {
      width: 76px;
      height: 76px;
      border-radius: 50%;
      position: relative;

      .el-avatar {
        width: 90px;
        height: 90px;
        background: #fff;

        img {
          width: 100%;
        }
      }

      .myRankIcon {
        position: absolute;
        left: -23px;
        top: -23px;

        img {
          width: 50px;
          height: 50px;
        }
      }
    }

    /* 个人信息区域 */
    .myInfo {
      margin-left: 20px;

      p {
        font-size: 24px;
        color: rgba(255, 255, 255, 0.2);

        span {
          margin-left: 5px;
          font-size: 32px;
          font-weight: 600;
          color: #ffd387;
        }
      }

      .rankNum {
        margin-left: 5px;
        font-size: 32px;
        font-weight: 600;
        color: #ffd387;
      }

      .no {
        width: 66px;
      }

      div {
        margin-top: 10px;
        font-size: 18px;
        display: flex;
        align-items: center;
        white-space: nowrap;

        span {
          display: inline-block;
          width: 104px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          background: linear-gradient(90deg, #ea6945 0%, #ff9545 100%);
          box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
          border-radius: 12px;
          font-size: 14px;
          margin-left: 4px;
        }
      }
    }
  }

  /* 总分区域 */
  .total {
    flex: 1;
    display: flex;
    align-items: center;
    margin-left: 5%;

    @media screen and (max-width: 1200px) {
      margin-left: 10px;
    }

    /* 左侧总分 */
    .total-l {
      display: flex;

      >div {
        position: relative;
        display: inline-block;
        width: 100px;
        height: 80px;
        text-align: center;
        background: rgba(255, 255, 255, 0.05);
        margin-right: 10px;
        border-radius: 5px;
        padding-top: 20px;
        box-sizing: border-box;

        p {
          margin-bottom: 5px;
        }

        p:nth-child(2) {
          font-size: 24px;
          font-weight: 600;
          color: #ffd387;
        }
      }

      div::after {
        content: '';
        position: absolute;
        top: 25%;
        left: 0%;
        width: 6px;
        height: 14px;
        background: linear-gradient(270deg, #ea6945ff 0%, #ff9545ff 100%);
      }
    }

    /* 右侧轮播 */
    .total-r {
      flex: 1;
      margin-right: 20px;
      margin-bottom: -30px;

      .total-r-carousel-item {
        overflow-y: hidden;

        &::-webkit-scrollbar {
          display: none;
        }

        &:hover {
          &::-webkit-scrollbar {
            display: block;
            height: 4px;
            margin-bottom: -80px;
            z-index: 99;
          }

          &::-webkit-scrollbar-thumb {
            background-color: rgba(247, 243, 243, 0.7);
            border-radius: 6px;
          }
        }
      }

      p {
        width: 100%;
        min-width: fit-content;
        height: 38px;
        line-height: 38px;
        margin-bottom: 5px;
        padding-left: 20px;
        background: rgba(255, 255, 255, 0.05);
        display: flex;
        border-radius: 5px;

        &:last-child {
          margin-bottom: 0;
        }

        span {
          min-width: 130px;
          width: 25%;
          font-size: 15px;
          display: inline-block;

          i {
            color: #ffd387;
            font-style: normal;
          }
        }
      }
    }
  }
}

/* 表格样式 */
.main-table-wrap {
  position: relative;

  /* 表格样式 */
  .main-table {

    /* 滚动条样式 - @cell-x/el-table-sticky插件 */
    ::v-deep .gm-scrollbar {
      background-color: rgba(255, 255, 255, 0.1);

      .thumb {
        background-color: rgba(255, 255, 255, 0.3);

        &:active,
        &:hover {
          background-color: rgba(255, 255, 255, 0.3) !important;
        }
      }
    }

    .icon-arrow-right {
      width: 3px;
      height: 6px;
      background: url('~@/assets/imgs/helpStudyPkRank/icon-arrow-right.png') no-repeat;
      background-size: 100% 100%;
      display: inline-block;
      margin-left: 6px;
    }

    .link {
      color: #fff;

      &:hover {
        color: #fff;
      }

      &:hover:after {
        position: absolute;
        left: 0;
        right: 0;
        height: 0;
        bottom: 0;
        border-bottom: 1px solid #fff;
      }
    }

    /* 表格样式覆盖 */
    ::v-deep .el-table__header-wrapper {
      border-bottom: $table-header-border-bottom solid transparent;
      border-top: none !important; // 处理粘性插件的边框
    }

    ::v-deep .el-table__row {
      background-color: #2b1e54;
      color: #fff;
    }

    /* 前三名特殊样式 */
    ::v-deep .el-table__row:nth-child(-n + 3) {
      background: linear-gradient(90deg, #946643 0%, #37253f 44%, #38253f 100%);
      box-shadow: inset 0 0 20px 0 rgba(16, 0, 0, 0.22),
        inset 0 0 2px 0 rgba(255, 255, 255, 0.75);

      .el-table__cell {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding: 12px 0;
      }

      .cell {
        line-height: normal;
      }
    }

    ::v-deep .el-table__row:nth-child(3) {
      .el-table__cell {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }
    }

    /* 表格行悬停效果 */
    ::v-deep .el-table__row:hover td {
      background: transparent !important;
      background-color: transparent !important;
    }

    ::v-deep .el-table__row:hover {
      background: $table-hover-bg-color;
    }

    /* 表格固定列样式修复 */
    /* fixed固定时会产生一个灰色的背景色，不兼容 */
    ::v-deep .el-table__body tr.hover-row>td.el-table__cell {
      background: transparent !important;
      color: #fff;
    }

    /* 表格行悬停效果统一 */
    /* 固定表格和底部表格其中一个hover的时候，同步hover的颜色 */
    ::v-deep .el-table__body tr.hover-row,
    ::v-deep .el-table__row:hover {
      background: $table-hover-bg-color;
    }

    /* 修复固定列表格的高度问题 */
    ::v-deep .el-table__fixed {
      height: auto !important;
      bottom: 0;

      &::before {
        height: 0;
        /* 修复固定表格底部白边 */
      }
    }

    /* 表格背景和边框样式 */
    ::v-deep &,
    ::v-deep .el-table__expanded-cell {
      background-color: #180e36 !important;
    }

    ::v-deep td,
    ::v-deep th.is-leaf {
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    ::v-deep td {
      border-right: none;
    }

    ::v-deep tr {
      background-color: transparent;
    }

    /* 表格表头单元格样式 */
    ::v-deep .table-cell-header {
      background-color: #4731a6 !important; // 要不然左右滚动颜色就会穿透
      color: #fff !important;
    }

    /* 表格边框样式覆盖 */
    &.el-table--border ::v-deep td,
    &.el-table--border ::v-deep th,
    ::v-deep .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
      border-right: none;
    }

    ::v-deep .el-table__body-wrapper {
      min-height: 265px;
    }

    /* 表格助学积分、助学人数、活动人均列特殊颜色 */
    .score-column-color {
      color: #ffd387ff;
    }

    ::v-deep td.el-table__cell,
    ::v-deep th.el-table__cell.is-leaf {
      border-bottom: 0px;
    }

    /* 移除表格默认边框 */
    ::v-deep &.el-table--border,
    ::v-deep .el-table--group {
      border: none;
    }

    ::v-deep &.el-table--border::after,
    ::v-deep .el-table--group::after,
    ::v-deep &::before {
      background-color: transparent;
    }

    ::v-deep &.el-table--border th,
    ::v-deep .el-table__fixed-right-patch {
      border: none;
    }
  }

  /* 表格收起/展开按钮 */
  .toggle-button-box {
    position: absolute;
    right: -14px;
    top: 128px;
    width: 14px;
    height: 99%; // 父元素的高度不能低于sticky元素的高度 (bug: 100%高度会导致父元素高度多出一点)

    .toggle-button {
      width: 100%;
      height: 100px;
      cursor: pointer;
      position: sticky;
      top: 45%;
    }
  }
}

/* 其他元素样式 */
.personImg {
  width: 100%;
  height: 55px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 头像框样式 */
.avatar-box {
  border: 2px solid #fff;

  ::v-deep img {
    width: 100%;
  }
}

/* 回到顶部按钮样式 */
.back-to-top {
  position: fixed;
  right: 5px;
  bottom: 60px;
  width: 25px;
  height: 25px;
  background: rgba(80, 72, 102, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 999;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2);
  }

  i {
    color: #fff;
    font-size: 20px;
  }
}
</style>
