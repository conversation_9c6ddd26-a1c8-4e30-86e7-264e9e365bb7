<template>
  <el-table
    border
    size="small"
    style="width: 100%"
    header-cell-class-name='table-cell-header'
    :data="warData"
    :span-method="objectSpanMethod"
  >
    <el-table-column label="pk分组" align="center" width="100">
      <template>
        <div class="table-pk-column">pk</div>
      </template>
    </el-table-column>
    <el-table-column label="战队" prop="groundName" align="center" width="150">
      <template slot-scope="scope">
        <el-avatar :size="100">
          <img v-if="scope.row.groundName === 'red'" src="../../../../assets/imgs/helpStudyPkRank/defaultPK1.png" />
          <img v-else src="../../../../assets/imgs/helpStudyPkRank/defaultPK2.png" />
        </el-avatar>
        <div>{{ scope.row.supTeamName }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="topEmpName" label="总指挥" align="center" />
    <WarTableColumn prop="teamName" label="部门" align="center" />
    <WarTableColumn prop="empName" label="分校长" align="center" />
    <WarTableColumn prop="manPower" label="人力" align="center" />
    <template v-if="pkRange.includes('1')">
      <WarTableColumn prop="adultEduToday" label="今日成教" />
      <WarTableColumn prop="adultEduTotal" label="活动成教" align="center" />
    </template>
    <template v-if="pkRange.includes('2')">
      <WarTableColumn prop="nationalOpenToday" label="今日国开" align="center" />
      <WarTableColumn prop="nationalOpenTotal" label="活动国开" align="center" />
    </template>
    <template v-if="pkRange.includes('3')">
      <WarTableColumn prop="fullTimeToday" label="今日全日制" align="center" />
      <WarTableColumn prop="fullTimeTotal" label="活动全日制" align="center" />
    </template>
    <template v-if="pkRange.includes('4')">
      <WarTableColumn prop="selfStudyToday" label="今日自考" align="center" />
      <WarTableColumn prop="selfStudyTotal" label="活动自考" align="center" />
    </template>
    <template v-if="pkRange.includes('5')">
      <WarTableColumn prop="postgraduateToday" label="今日研究生" align="center" />
      <WarTableColumn prop="postgraduateTotal" label="活动研究生" align="center" />
    </template>
    <template v-if="pkRange.includes('6')">
      <WarTableColumn prop="vocationalEduToday" label="今日职业教育" align="center" />
      <WarTableColumn prop="vocationalEduTotal" label="活动职业教育" align="center" />
    </template>
    <template v-if="pkRange.includes('7')">
      <WarTableColumn prop="overseasEduToday" label="今日海外教育" align="center" />
      <WarTableColumn prop="overseasEduTotal" label="活动海外教育" align="center" />
    </template>

    <WarTableColumn prop="todayOrders" label="今日合计" align="center" />
    <WarTableColumn prop="performanceDeduction" label="调整业绩" align="center" />
    <WarTableColumn prop="totalOrders" label="活动合计" align="center" />

    <!-- 普通目标 普通目标可以存在多个-->
    <template v-if="targetType === 1 && targetConfigList.length">
      <el-table-column v-for="(item,index) in targetConfigList" :key="index" :label="item" align="center">
        <template slot-scope="scope">
          <div :class="{'totalColor': scope.row.isTotalRow}">
            <span>{{ handleTargetText(scope.row, index) }}</span>
          </div>
        </template>
      </el-table-column>
    </template>

    <!-- 助学积分、助学人数、活动人均 -->
    <el-table-column prop="totalScore" align="center">
      <template slot="header">
        <span>{{ pkType | pkTypeToText }}</span>
      </template>

      <template slot-scope="scope">
        <div :class="{'totalColor': scope.row.isTotalRow}">
          {{ scope.row.totalScore }}
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import WarTableColumn from './war-table-column.vue';
export default {
  name: 'WarTable',
  components: {
    WarTableColumn
  },
  filters: {
    pkTypeToText(val) {
      const pkTypeEnum = {
        1: '助学积分',
        2: '助学人数',
        3: '活动人均'
      };
      return pkTypeEnum[val] || '/';
    }
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    // 1：助学积分 2：助学人数 3：活动人均
    pkType: {
      type: Number,
      default: 0
    },
    // pk范围，1：成教 2：国开 3：全日制 4：自考 5：研究生 6：职业教育 7：海外教育
    pkRange: {
      type: Array,
      default: () => []
    },
    // 1: 普通目标, 2: 奖励目标
    targetType: {
      type: Number,
      default: 0
    },
    // 普通目标的配置
    targetConfigList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      warData: []
    };
  },
  watch: {
    tableData: {
      handler(newVal) {
        this.processTableData(newVal);
      }
    }
  },
  methods: {
    // 处理普通目标的文本
    handleTargetText(row, index) {
      if (row.targetConfigList && Array.isArray(row.targetConfigList)) {
        const text = row.targetConfigList[index];
        return text !== 'null' ? text : '';
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 合计行的列合并
      if (row.isTotalRow) {
        if (columnIndex === 3) {
          return {
            rowspan: 1,
            colspan: 2
          };
        } else if (columnIndex === 4) {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }

      function getRowspan(rowIndex, key) {
        const prevRow = this.warData[rowIndex - 1];
        if (prevRow && prevRow[key] === this.warData[rowIndex][key]) {
          return { rowspan: 0, colspan: 0 };
        }
        let nextRow = this.warData[rowIndex + 1];
        let rowspan = 1;
        while (nextRow && nextRow[key] === this.warData[rowIndex][key]) {
          rowspan++;
          nextRow = this.warData[++rowIndex + 1];
        }
        return { rowspan, colspan: 1 };
      }

      if (columnIndex === 0) {
        // 第1列的行合并
        return getRowspan.call(this, rowIndex, 'groupId');
      } else if (columnIndex === 1 || columnIndex === 2) {
        // 第2列、第3列的行合并
        return getRowspan.call(this, rowIndex, 'supTeamId');
      }
    },
    processTableData(body) {
      const groupedData = {};
      body.forEach(item => {
        const key = `${item.groupId}-${item.supTeamId}`;
        if (!groupedData[key]) {
          groupedData[key] = [];
        }
        groupedData[key].push(item);
      });

      // 处理红蓝分组
      Object.keys(groupedData).forEach((key, index) => {
        groupedData[key].forEach((groupedItem) => {
          if ((index + 1) % 2 === 0) {
            // 偶数
            groupedItem.groundName = 'blue';
          } else {
            // 奇数
            groupedItem.groundName = 'red';
          }
        });
      });

      const result = [];
      for (const key in groupedData) {
        const group = groupedData[key];
        const total = group.reduce((acc, curr) => {
          const fieldsToSum = [
            'manPower', // 人力
            'totalOrders', // 活动合计
            'adultEduToday', // 今日成教
            'adultEduTotal', // 活动成教
            'nationalOpenToday', // 今日国开
            'nationalOpenTotal', // 活动国开
            'fullTimeToday', // 今日全日制
            'fullTimeTotal', // 活动全日制
            'selfStudyToday', // 今日自考
            'selfStudyTotal', // 活动自考
            'postgraduateToday', // 今日研究生
            'postgraduateTotal', // 活动研究生
            'vocationalEduToday', // 今日职业教育
            'vocationalEduTotal', // 活动职业教育
            'overseasEduToday', // 今日海外教育
            'overseasEduTotal', // 活动海外教育
            'todayOrders', // 今日合计
            'performanceDeduction', // 调整业绩
            'totalScore' // 助学积分、助学人数、活动人均
          ];

          fieldsToSum.forEach(field => {
            acc[field] = (acc[field] || 0) + (curr[field] || 0);
          });

          // 普通目标
          acc.targetConfigList = acc.targetConfigList || [];
          curr.targetConfigList = curr.targetConfigList || [];
          curr.targetConfigList.forEach((value, index) => {
            acc.targetConfigList[index] = (acc.targetConfigList[index] || 0) + (parseInt(value) || 0);
          });

          return acc;
        }, {});

        total.groupId = group[0].groupId;
        total.supTeamId = group[0].supTeamId;
        total.isTotalRow = true; // 标识，代表这行是合计行
        total.topEmpName = '合计';
        total.teamName = '合计';
        total.empName = '合计';

        result.push(...group);
        result.push(total);
      }

      this.warData = result;
    }
  }
};
</script>

<style lang = "scss" scoped>
.table-pk-column {
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 28px;
  color: #f69;
}
.totalColor {
  color: #f69;
}
</style>
