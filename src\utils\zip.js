// 打包多个文件，压缩成zip下载
import JSZ<PERSON> from 'jszip';
import FileSaver from 'file-saver';
import axios from 'axios';

/**
 * 获取文件
 * @param url 文件的网络地址
 */
const getFile = (url) => {
  return new Promise((resolve, reject) => {
    const config = {
      method: 'GET',
      responseType: 'blob'
    };
    axios(url, config).then(res => {
      resolve(res);
    }).catch(error => {
      reject(error);
    });
  });
};

/**
 * 多个文件打包下载
 * @param files 文件list
 * @param zipName 压缩包名
 */
const buildZipDownload = (files, zipName) => {
  const zip = new JSZip();
  const promises = []; // 用于存储多个promise

  files.forEach(file => {
    const promise = getFile(file.fileUrl).then(res => {
      const fileName = file.fileName;
      zip.file(fileName, res.data, { binary: true });
    });
    promises.push(promise);
  });
  Promise.all(promises).then(() => {
    zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE', // STORE：默认不压缩 DEFLATE：需要压缩
      compressionOptions: {
        level: 9 // 压缩等级1~9    1压缩速度最快，9最优压缩方式
      }
    }).then((res) => {
      FileSaver.saveAs(res, zipName || '压缩包.zip'); // 利用file-saver保存文件
    });
  });
};

export default buildZipDownload;

// 使用：按一下格式要求使用
// 注意 fileName的后缀文件格式一定要带
// const files = [
//   { fileUrl: 'http://bms.yzwill.cn/pkChildActivity/pkChildActivityPerformanceExport?pkChildActivityId=162694693684541361', fileName: '哈哈.xlsx' },
//   { fileUrl: 'http://bms.yzwill.cn/pkChildActivity/pkChildActivityPerformanceExport?pkChildActivityId=162700340442072242', fileName: '试试.xlsx' }
// ];
// buildZipDownload(files);
