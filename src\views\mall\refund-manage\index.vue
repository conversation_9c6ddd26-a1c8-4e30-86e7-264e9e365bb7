<template>
  <div class="yz-base-container">
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane label="退款订单" name="refund">
        <refund @updateList="handleUpdateList" />
      </el-tab-pane>
      <el-tab-pane label="退款审核" name="examine" lazy>
        <examine ref="examine" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import refund from './refund';
import examine from './examine';
export default {
  components: {
    refund,
    examine
  },
  data() {
    return {
      activeName: 'refund'
    };
  },
  methods: {
    handleUpdateList() {
      if (this.$refs.examine) {
        this.$refs.examine.getTableList();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
