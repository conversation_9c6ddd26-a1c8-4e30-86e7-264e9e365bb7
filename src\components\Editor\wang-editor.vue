<template>
  <div class="wang-editor">
    <div ref="bar" style="display: none;"></div>
    <div ref="editor"></div>
  </div>
</template>

<script>
import E from 'wangeditor';
import { ossUri } from '@/config/request';
import xss from 'xss';
export default {
  name: 'WangEditor',
  components: {},
  props: {
    content: {
      type: String,
      default: ''
    },
    menus: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '在此处输入文本…'
    }
  },
  data() {
    return {
      editor: null,
      isExternal: true,
      unWatch: null,
      project: '',
      uploadUrl: ''
    };
  },
  watch: {
    content(newVal, oldVal) {
      if (oldVal === null || oldVal === undefined) {
        oldVal = '';
      }
      if (this.isExternal || oldVal.trim() === '') {
        this.setContent(newVal);
      }
    }
  },
  mounted() {
    this.project = this.$route.query.project;

    if (this.project === 'bms') {
      this.uploadUrl = '/graduatePaper/uploadPicture.do';
    } else {
      this.uploadUrl = '/paper/uploadPicture.do';
    }
    this.init();
  },
  beforeDestroy() {
    this.editor.destroy();
  },
  methods: {
    init() {
      const bar = this.$refs['bar'];
      const elem = this.$refs['editor'];
      this.editor = new E(bar, elem);
      this.editor.config.zIndex = 500;
      this.editor.config.menus = this.menus; // 配置菜单
      this.editor.config.placeholder = this.placeholder;
      this.editor.config.pasteFilterStyle = true;
      this.editor.config.height = 100;
      // 上传图片配置
      this.editor.config.customUploadImg = (resultFiles, insertImgFn) => {
        const formData = new FormData();
        formData.set('file', resultFiles[0]);
        this.$http.post(this.uploadUrl, formData, { uploadFile: true })
          .then(res => {
            const imgUrl = ossUri + res.body;
            insertImgFn(imgUrl);
          });
      };
      this.editor.config.showLinkImg = false;// 隐藏网络图片功能
      // 清除粘贴的样式
      this.editor.config.pasteTextHandle = function(pasteStr) {
        if (pasteStr === '' && !pasteStr) return '';
        console.log(pasteStr, '粘贴的内容');
        let str = pasteStr;
        str = str.replace(/style\s*?=\s*?(['"])[\s\S]*?\1/, '');
        str = str.replace(/<(?!\/?br\/?.+?>|\/?img.+?|\/?p.+?|\/?font.+?>)[^<>]*>/gi, '');
        const html = xss(str);
        console.log(html, '格式化后的内容');
        return html;
      };

      this.editor.config.onchangeTimeout = 500; // 修改为 500ms
      // 配置 onchange 回调函数
      this.editor.config.onchange = (newHtml) => {
        // // isExternal 控制回车bug问题
        this.isExternal = false;
        this.$emit('update:content', newHtml);
        setTimeout(() => {
          this.isExternal = true;
        });
      };
      this.editor.create();
      this.editor.txt.html(this.content); // 设置内容
    },
    setContent(content) {
      if (this.editor) {
        this.editor.txt.html(content);
      }
    }
  }
};
</script>

<style lang='scss' scoped>
::v-deep .w-e-text {
  padding: 0;
}

::v-deep .w-e-text-container .placeholder {
  left: 0;
}

</style>
