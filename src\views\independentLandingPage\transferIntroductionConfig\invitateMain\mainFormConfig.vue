<!-- 弹窗-邀约有礼表单配置 -->
<template>
  <common-dialog width="60%" title="邀约有礼表单配置" :visible.sync="visible" :showFooter="true" @open="openInit" @confirm="submitBtn" @close="closeBtn">
    <el-form ref="ruleForm" v-loading="allLoading" class="rulesForm" :model="querys" :rules="rules" :size="'mini'">
      <el-form-item class="rulesForm-label" label="页面标题：" :required="true" prop="headline">
        <el-input v-model.trim="querys.headline" placeholder="用于前端展示" clearable maxlength="8" show-word-limit />
      </el-form-item>
      <el-form-item class="rulesForm-label" label="页面头图：" :required="true" prop="banner">
        <upload-file :max-limit="1" :size="2" accept="image/jpg,image/png,image/gif,image/jpeg" exts="jpg|png|gif|jpeg" :file-list="querys.bannerImg" @remove="removeBannerImg" @success="successBannerImg" />
        <p style="font-size: 12px;color: #7c7c7c;">仅支持上传JPG/PNG/JPEG，推荐尺寸为 375*304（px）</p>
      </el-form-item>
      <el-form-item class="rulesForm-label" label="活动规则：" :required="true" prop="activityRules">
        <wang-editor ref="editors" v-model.trim="querys.activityRules" :value="querys.activityRules" :content.sync="querys.activityRules" :height="128" />
      </el-form-item>
      <!-- 可拖拽区域 -->
      <transition-group tag="div" class="rulesForm-model">
        <div v-for="item in moveList" :key="item.inds" class="model-li" draggable="true" @dragstart="handleDragStart($event, item)" @dragover.prevent="handleDragOver($event, item)" @dragenter="handleDragEnter($event, item)" @dragend="handleDragEnd($event, item)">
          <h4>{{ item.title }}模块：（可拖拽排序）</h4>
          <div class="rulesForm-itms">
            <el-form-item :label="`${item.title}模块标题：`" :required="true" :prop="item.tkey">
              <el-input v-model.trim="querys[item.tkey]" placeholder="用于前端展示" maxlength="8" clearable show-word-limit />
            </el-form-item>
            <el-form-item :label="`${item.title}模块副标题：`">
              <el-input v-model.trim="querys[item.skey]" placeholder="用于前端展示" maxlength="12" clearable show-word-limit />
            </el-form-item>
            <div v-if="item.type!=='taskSort'">
              <el-form-item :label="`${item.title}模块按钮：`" :required="true" :prop="item.type?item.bkey:''">
                <upload-file :max-limit="1" :size="2" accept="image/jpg,image/png,image/gif,image/jpeg" exts="jpg|png|gif|jpeg" :file-list="querys[item.bimg]" @remove="removeBtnImg(item)" @success="(evt)=>successBtnImg(evt,item)" />
                <p style="font-size: 12px;color: #7c7c7c;">仅支持上传JPG/PNG/JPEG，推荐尺寸为 202*43（px）</p>
              </el-form-item>
              <el-form-item label="按钮动效：" :required="true" :prop="item.type?item.rkey:''">
                <el-radio-group v-model="querys[item.rkey]">
                  <el-radio :label="'0'">按钮固定</el-radio>
                  <el-radio :label="'1'">呼吸动效</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div v-else>
              <p>任务明细：展示当前启用状态下的任务</p>
              <el-table border :size="'small'" :data="querys.taskData" header-cell-class-name="table-cell-header">
                <el-table-column type="index" label="展示顺序" align="center" />
                <el-table-column prop="typeText" label="任务类型" align="center" />
                <el-table-column prop="title" label="任务标题" align="center" />
                <el-table-column prop="detail" label="任务明细" align="center" />
                <el-table-column prop="giveZhimi" label="智米奖励" align="center" />
                <el-table-column prop="isSupportUnpaid" label="对未缴费的邀约人是否生效" align="center">
                  <template slot-scope="scope">
                    {{ scope.row.isSupportUnpaid==0?'否':'是' }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                  <template slot-scope="scope">
                    <el-button v-if="scope.$index&&scope.row.type!==7" :size="'mini'" style="margin-bottom: 6px;" type="primary" @click="openBind(scope)">上移</el-button>
                    <el-button v-if="scope.$index!=querys.taskData.length-1&&scope.row.type!==1" :size="'mini'" type="success" @click="openRecord(scope)">下移</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </transition-group>
      <el-form-item class="rulesForm-label" label="底部文案：">
        <el-input v-model.trim="querys.bottomCopywriter" placeholder="用于前端展示" clearable maxlength="20" show-word-limit />
      </el-form-item>
      <el-form-item class="rulesForm-label" label="邀约海报：" :required="true" prop="invitePoster">
        <upload-file :max-limit="1" :size="2" accept="image/jpg,image/png,image/gif,image/jpeg" exts="jpg|png|gif|jpeg" :file-list="querys.invitePImg" @remove="removeInvitePImg" @success="successInvitePImg" />
        <p style="font-size: 12px;color: #7c7c7c;">仅支持上传JPG/PNG/JPEG，推荐尺寸为 281*420（px）</p>
      </el-form-item>
      <!-- 邀约好友模块 -->
      <div class="rulesForm-box">
        <h4>邀约好友：</h4>
        <div class="rulesForm-itms">
          <el-form-item label="微信分享图：" :required="true" prop="vxSharePicture">
            <upload-file :max-limit="1" :size="2" accept="image/jpg,image/png,image/gif,image/jpeg" exts="jpg|png|gif|jpeg" :file-list="querys.vxShareImg" @remove="removeVxShareImg" @success="successVxShareImg" />
            <p style="font-size: 12px;color: #7c7c7c;">仅支持上传JPG/PNG/JPEG，推荐尺寸为 44*44（px）</p>
          </el-form-item>
          <el-form-item label="微信分享标题：" :required="true" prop="vxShareTitle">
            <el-input v-model.trim="querys.vxShareTitle" placeholder="用于前端展示" clearable maxlength="20" show-word-limit />
          </el-form-item>
          <el-form-item label="微信分享副标题：" :required="true" prop="vxShareSubhead">
            <el-input v-model.trim="querys.vxShareSubhead" placeholder="用于前端展示" clearable maxlength="20" show-word-limit />
          </el-form-item>
        </div>
      </div>
    </el-form>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';

let olds = 0;
let news = 0;
export default {
  components: {},
  props: {
    visible: { type: Boolean, default: false },
    params: { type: Object, default: () => {} }
  },
  data() {
    // 这里存放数据
    return {
      allLoading: false,
      moveList: [],
      querys: {
        headline: '',
        banner: '',
        bannerImg: [],
        activityRules: '',
        taskTitle: '',
        taskSubhead: '',
        taskData: [],
        shopTitle: '',
        shopSubhead: '',
        shopButton: '',
        shopBtnImg: [],
        shopButtonStatus: '',
        inviteTitle: '',
        inviteSubhead: '',
        inviteButton: '',
        inviteBtnImg: [],
        inviteButtonStatus: '',
        bottomCopywriter: '',
        invitePoster: '',
        invitePImg: [],
        vxSharePicture: '',
        vxShareImg: [],
        vxShareTitle: '',
        vxShareSubhead: ''
      },
      rules: {
        headline: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        banner: [{ required: true, message: '请上传图片', trigger: ['blur', 'change'] }],
        activityRules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        taskTitle: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        shopTitle: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        shopButton: [{ required: true, message: '请上传图片', trigger: ['blur', 'change'] }],
        shopButtonStatus: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        inviteTitle: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        inviteButton: [{ required: true, message: '请上传图片', trigger: ['blur', 'change'] }],
        inviteButtonStatus: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        bottomCopywriter: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        vxSharePicture: [{ required: true, message: '请上传图片', trigger: ['blur', 'change'] }],
        invitePoster: [{ required: true, message: '请上传图片', trigger: ['blur', 'change'] }],
        vxShareTitle: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
        vxShareSubhead: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
      },
      dragging: null
    };
  },
  // 方法集合
  methods: {
    // 初始化编辑
    async openInit() {
      this.allLoading = true;
      const { body } = await this.$http.post('/inviteTaskConfig/getList.do', {
        page: 1,
        rows: 30,
        isAllow: 1,
        orderType: 'sort',
        orderSort: 'ASC'
      });
      const newTaske = body?.data || [];
      let oneIns = null;
      let sevenIns = null;
      newTaske.map((item, index) => {
        item.giveZhimi = Number(item.giveZhimi || 0);
        item.typeText = '';
        item.type = Number(item.type);
        switch (item.type) {
          case 1:
            oneIns = index;
            item.typeText = '注册';
            break;
          case 2:
            item.typeText = '测评';
            break;
          case 3:
            item.typeText = '报名活动';
            break;
          case 4:
            item.typeText = '首次发帖';
            break;
          case 5:
            item.typeText = '添加老师';
            break;
          case 6:
            item.typeText = '报读';
            break;
          case 7:
            sevenIns = index;
            item.typeText = '缴费';
            break;
          default:
            break;
        }
      });
      if (oneIns !== null) {
        const news = newTaske[0];
        newTaske[0] = newTaske[oneIns];
        newTaske[oneIns] = news;
      }
      if (sevenIns !== null) {
        const news = newTaske[newTaske.length - 1];
        newTaske[newTaske.length - 1] = newTaske[sevenIns];
        newTaske[sevenIns] = news;
      }
      console.log('初始化编辑-表格数据-newTaske', newTaske);
      this.$http.post('/inviteTaskConfig/getInvitePageConfig.do', { id: 1 })
        .then((res) => {
          const obj = JSON.parse(JSON.stringify({ ...this.querys, ...res?.body }));
          const bools = res?.code === '00';
          console.log('初始化编辑-表单-obj', { ...obj });
          if (bools) {
            // 图片处理
            if (obj.banner) obj.bannerImg = [{ url: ossUri + obj.banner }];
            if (obj.shopButton) obj.shopBtnImg = [{ url: ossUri + obj.shopButton }];
            if (obj.inviteButton) obj.inviteBtnImg = [{ url: ossUri + obj.inviteButton }];
            if (obj.invitePoster) obj.invitePImg = [{ url: ossUri + obj.invitePoster }];
            if (obj.vxSharePicture) obj.vxShareImg = [{ url: ossUri + obj.vxSharePicture }];
            // 表格处理
            obj['taskData'] = newTaske;
            if (this.$refs['editors']) {
              this.$refs['editors'].setContent(obj.activityRules || '');
            }
            // 表单赋值
            this.querys = obj;
            // 模块处理
            const newsModel = [];
            const newsList = [
              { inds: 1, type: 'taskSort', title: '任务', tkey: 'taskTitle', skey: 'taskSubhead', bkey: '', bimg: '', rkey: '' },
              { inds: 2, type: 'shopSort', title: '商城', tkey: 'shopTitle', skey: 'shopSubhead', bkey: 'shopButton', bimg: 'shopBtnImg', rkey: 'shopButtonStatus' },
              { inds: 3, type: 'inviteSort', title: '邀约', tkey: 'inviteTitle', skey: 'inviteSubhead', bkey: 'inviteButton', bimg: 'inviteBtnImg', rkey: 'inviteButtonStatus' }
            ];
            const taskSort = Number(obj?.taskSort || 0) - 1;
            const shopSort = Number(obj?.shopSort || 0) - 1;
            const inviteSort = Number(obj?.inviteSort || 0) - 1;
            if (taskSort >= 0) {
              newsModel[taskSort] = { ...newsList[0], inds: taskSort + 1 };
            }
            if (shopSort >= 0) {
              newsModel[shopSort] = { ...newsList[1], inds: shopSort + 1 };
            }
            if (inviteSort >= 0) {
              newsModel[inviteSort] = { ...newsList[2], inds: inviteSort + 1 };
            }
            console.log(`邀约排序：${inviteSort}`, `商城排序：${shopSort}`, `任务排序：${taskSort}`, JSON.stringify(newsModel));
            this.moveList = JSON.parse(JSON.stringify(newsModel));
          }
          console.log('初始化编辑-obj', this.querys);
          // this.$forceUpdate();
          this.$refs['ruleForm']?.resetFields();
          this.allLoading = false;
        })
        .catch((err) => {
          this.allLoading = false;
          console.log('获取邀约上限变更记录-err', err);
        });
    },
    // 上移
    openBind({ $index, row }) {
      console.log('向上移动当前项-row', $index, olds);
      // 判断上一项是否为注册
      if ($index === 1) {
        const { type } = this.querys.taskData[0];
        if (type === 1) {
          this.$message({ message: '当前项不可以上移', type: 'warning' });
          return;
        }
      }
      const olds = this.querys.taskData[$index - 1];
      const cutSort = Number(row.sort || 0);
      row.sort = Number(olds.sort || 0);
      olds.sort = cutSort;
      this.querys.taskData.splice($index, 1, olds);
      this.querys.taskData.splice($index - 1, 1, row);
    },
    // 下移
    openRecord({ $index, row }) {
      const olds = this.querys.taskData[$index + 1];
      if (olds.type === 7) {
        this.$message({ message: '当前项不可以下移', type: 'warning' });
        return;
      }
      const cutSort = Number(row.sort || 0);
      row.sort = Number(olds.sort || 0);
      olds.sort = cutSort;
      console.log('向下移动当前项-row', $index, row, olds);
      this.querys.taskData.splice($index, 1, olds);
      this.querys.taskData.splice($index + 1, 1, row);
      this.$forceUpdate();
    },
    // 成功上传 头图
    successBannerImg(evt) {
      this.querys.banner = evt?.response;
    },
    // 删除 头图
    removeBannerImg() {
      this.querys.banner = '';
      this.querys.bannerImg = [];
    },
    // 成功上传 模块按钮
    successBtnImg(evt, item) {
      this.querys[item.bkey] = evt?.response;
    },
    // 删除 模块按钮
    removeBtnImg(item) {
      this.querys[item.bkey] = '';
      this.querys[item.bimg] = [];
    },
    // 成功上传 邀约海报
    successInvitePImg(evt) {
      this.querys.invitePoster = evt?.response;
    },
    // 删除 邀约海报
    removeInvitePImg() {
      this.querys.invitePoster = '';
      this.querys.invitePImg = [];
    },
    // 成功上传 微信分享图
    successVxShareImg(evt) {
      this.querys.vxSharePicture = evt?.response;
    },
    // 删除 微信分享图
    removeVxShareImg() {
      this.querys.vxSharePicture = '';
      this.querys.vxShareImg = [];
    },
    // 提交表单
    submitBtn() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.moveList.forEach(item => {
            this.querys[item.type] = item.inds;
          });
          const obs = JSON.parse(JSON.stringify(this.querys));
          const news = [];
          for (let i = 0; i < obs.taskData.length; i++) {
            const element = obs.taskData[i];
            news.push({ id: element.id, sort: element.sort });
          }
          obs['taskData'] = news;
          delete obs.bannerImg;
          delete obs.inviteBtnImg;
          delete obs.invitePImg;
          delete obs.shopBtnImg;
          delete obs.vxShareImg;
          console.log('提交表单-2', JSON.stringify(obs));
          this.$http.post('/inviteTaskConfig/updateInvitePageConfig.do', obs, { json: true })
            .then((res) => {
              const bools = res?.code === '00';
              if (bools) this.closeBtn();
              this.$message({
                message: `修改${bools ? '成功' : '失败'}！`,
                type: bools ? 'success' : 'error'
              });
            })
            .catch((err) => {
              console.log('获取邀约上限变更记录-err', err);
            });
        }
      });
    },
    // 关闭
    closeBtn() {
      this.querys = { bannerImg: [], taskData: [], shopBtnImg: [], inviteBtnImg: [], invitePImg: [], vxShareImg: [] };
      this.$forceUpdate();
      this.$refs['ruleForm']?.resetFields();
      this.$emit('on-close');
    },
    // 拖拽开始
    handleDragStart(event, item) {
      console.log('拖拽开始', item);
      this.dragging = item.type;
    },
    // 拖拽针对放置目标来设置
    handleDragOver(event) {
      event.dataTransfer.dropEffect = 'move';
    },
    // 拖拽查找起点模块与落地模块
    handleDragEnter(event, item) {
      event.dataTransfer.effectAllowed = 'move';
      console.log('拖拽查找起点模块与落地模块-item', { ...item });
      console.log('拖拽查找起点模块与落地模块-this.dragging', this.dragging);
      if (item.type === this.dragging) {
        return;
      }
      // 最初模块
      const lists = JSON.parse(JSON.stringify(this.moveList));
      for (let i = 0; i < lists.length; i++) {
        // 查找原先模块对应的索引
        if (lists[i].type === this.dragging) {
          olds = i;
        }
        // 查找当前移动后的索引
        if (lists[i].type === item.type) {
          news = i;
        }
      }
      console.log('查找原先模块对应的索引', olds, news);
    },
    // 拖拽结束
    handleDragEnd() {
      const lists = JSON.parse(JSON.stringify(this.moveList));
      console.log('拖拽结束-', olds, news);
      const newObs = lists[news];// 1 -> 0
      const oldObs = lists[olds];// 0 -> 1
      // 交换两者位置
      console.log('交换两者位置-', newObs, oldObs);
      lists[news] = { ...lists[olds], inds: newObs.inds };
      lists[olds] = { ...newObs, inds: lists[olds].inds };
      console.log('拖拽结束-', lists);
      this.moveList = lists;
      this.dragging = null;
      olds = 0;
      news = 0;
    }
  }
};
</script>

<style lang="scss">
.rulesForm {
  margin: 40px 60px;
  .el-form-item {
    display: flex;
    .el-form-item__content {
      flex: 1;
    }
  }
  .rulesForm-label {
    .el-form-item__label {
      font-weight: 550;
    }
  }
  .rulesForm-model {
    position: relative;
    padding: 0;
    list-style-type: none;
    .model-li {
      padding: 10px;
      margin-bottom: 18px;
      border: 1px solid #DCDFE6;
      border-radius: 6px;
    }
    p {
      margin: 0 0 15px 10px;
      color: #a1a2a3;
    }
  }
  .rulesForm-box {
    margin: 10px;
    .rulesForm-itms {
        margin-left: 40px;
    }
  }
}
.yz-common-dialog__footer {
  text-align: center !important;
}
.item {
 margin-top: 10px;
 transition: all linear .3s
}
</style>
