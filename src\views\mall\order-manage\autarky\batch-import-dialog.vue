<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    confirmText="开始导入"
    title="自营导入"
    :visible.sync='show'
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        size='mini'
        :model='form'
        label-width='120px'
      >
        <el-form-item label='模板：'>
          <a :href="templateUrl" download>
            <el-button type="primary" plain>下载模板</el-button>
          </a>
        </el-form-item>

        <el-form-item label='选择文件：'>
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            :action="action"
            :on-change="handleChange"
            :on-exceed="handleExceed"
            :on-success="uploadSuccess"
            :name="field"
            :file-list="fileList"
            multiple
            :limit="1"
            :auto-upload="false"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击选择文件</em></div>
            <div slot="tip" class="el-upload__tip">
              <p>说明：</p>
              <p>(关键列：订单编号、物流名称、物流单号)</p>
              <p style="color: red;">请严格按模板填写，否则可能导致无法准确导入</p>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      form: {},
      fileList: [],
      field: 'file',
      templateUrl: ossUri + '/product/%E8%87%AA%E8%90%A5%E5%A5%BD%E7%89%A9%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95.xlsx',
      action: '/mallOrder/importOrder'
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    submit() {
      if (this.fileList.length > 0) {
        this.$confirm('确认导入？一经导入将无法撤销，请谨慎操作！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$refs.upload.submit();
        });
      } else {
        this.$message.error('请选择需要导入的文件');
      }
    },
    handleChange(file, fileList) {
      this.fileList = fileList;
    },
    uploadSuccess(response, file, fileList) {
      this.$refs.upload.clearFiles();
      response.body = response.code !== '00' ? response.msg : '导入成功';
      this.$alert(response.body, '提示', {
        dangerouslyUseHTMLString: true
      }).then(() => {
        this.$emit('success');
        this.show = false;
      });
    },
    handleExceed() {
      this.$message.error('每次只能上传一个文件');
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
