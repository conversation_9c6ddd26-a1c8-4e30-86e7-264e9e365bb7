<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref="searchForm"
      size="mini"
      label-width="130px"
      class="yz-search-form"
      :model="form"
      @submit.native.prevent="search"
    >
      <el-form-item label="学员姓名" prop="stdName">
        <el-input v-model="form.stdName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="学号" prop="schoolRoll">
        <el-input v-model="form.schoolRoll" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="院校" prop="unvsId">
        <infinite-selects
          v-model="form.unvsId"
          :props="{
            apiName: 'getUnvs',
            value: 'unvs_id',
            label: 'unvs_name',
            query: 'sName',
          }"
          @change="handleJointChange"
        />
      </el-form-item>
      <el-form-item label="专业层次" prop="pfsnLevel">
        <el-select v-model="form.pfsnLevel" placeholder="请选择">
          <el-option
            v-for="(item, index) in schoolLevel"
            :key="index"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select
          v-model="form.grade"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="(item, index) in grade"
            :key="index"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="专业" prop="pfsnId">
        <el-select
          v-model="form.pfsnId"
          v-loadmore="loadSubject"
          :remote-method="querySubject"
          filterable
          remote
          clearable
          placeholder="请选择"
          @clear="clearSubject"
        >
          <el-option
            v-for="(item, index) in subject"
            :key="index"
            :label="item.label"
            :value="item.pfsnId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学生论文资料状态" prop="checkStatus">
        <el-select v-model="form.checkStatus" placeholder="请选择">
          <el-option label="请选择" value="" />
          <el-option label="未完成" value="0" />
          <el-option label="审核中" value="1" />
          <el-option label="审核通过" value="2" />
          <el-option label="审核不通过" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否分配老师" prop="isDistribution">
        <el-select v-model="form.isDistribution" placeholder="请选择">
          <el-option label="请选择" value="" />
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="分配老师" prop="distributionTeacher">
        <el-input v-model="form.distributionTeacher" placeholder="请输入" />
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
      </div>
    </el-form>
    <!-- 按钮区 -->
    <div class="yz-table-btnbox">
      <el-button
        type="primary"
        size="small"
        @click="teacherBatchAssgin"
      >批量分配老师</el-button>
    </div>
    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="50" />
      <el-table-column
        prop="stdName"
        label="学员姓名"
        width="100"
        align="center"
      />
      <el-table-column
        prop="schoolRoll"
        label="学号"
        width="180"
        align="center"
      />
      <el-table-column prop="grade" label="年级" width="180" align="center" />
      <el-table-column
        prop="unvsName"
        label="院校"
        width="200"
        align="center"
      />
      <el-table-column prop="pfsnLevel" label="层次" width="100" align="center">
        <template slot-scope="scope">
          <div>
            {{ scope.row.pfsnLevel | pfsnLevel }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="pfsnName" label="专业" align="center" />
      <el-table-column
        prop="checkStatus"
        label="学生论文资料状态"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.checkStatus === '0'"
            class="tag"
          >未完成</el-tag>
          <el-tag
            v-if="scope.row.checkStatus === '1'"
            class="tag"
          >审核中</el-tag>
          <el-tag
            v-if="scope.row.checkStatus === '2'"
            class="tag"
          >审核通过</el-tag>
          <el-tag
            v-if="scope.row.checkStatus === '3'"
            class="tag"
          >审核不通过</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="isDistribution"
        label="是否已分配老师"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.isDistribution === '1'"
            class="tag"
            type="success"
          >是</el-tag>
          <el-tag
            v-if="scope.row.isDistribution === '0'"
            class="tag"
          >否</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="distributionTeacherName"
        label="分配老师"
        width="180"
        align="center"
      />
      <el-table-column align="center" label="操作" width="100">
        <template v-slot="scope">
          <el-button
            size="small"
            type="primary"
            @click="teacherAssgin(scope.row)"
          >分配</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <!-- 批量分配老师 -->
    <batch-assgin :visible.sync="baVisible" :paperList="paperList" />
    <no-select-student :visible.sync="nstVisible" />
  </div>
</template>

<script>
import batchAssgin from './assginTeacherDialog/batch-assgin';
import { getTextFromDict } from '@/utils';
import noSelectStudent from './assginTeacherDialog/no-select-student.vue';

const form = {
  stdName: '', // 学员姓名
  schoolRoll: '', // 学号
  unvsId: '', // 院校
  pfsnLevel: '', // 专业层次
  grade: '', // 年级
  pfsnId: '', // 专业
  checkStatus: '', // 资料状态
  isDistribution: '', // 是否分配老师
  distributionTeacher: '' // 分配老师
};
export default {
  components: {
    batchAssgin,
    noSelectStudent
  },
  filters: {
    pfsnLevel(val) {
      const name = getTextFromDict('pfsnLevel', val);
      let text = '';
      if (name.indexOf('高中') !== -1) {
        text += '[专科]';
      } else if (name.indexOf('本科') !== -1) {
        text += '[本科]';
      } else {
        text += '[' + name + ']';
      }
      return text;
    }
  },
  data() {
    return {
      baVisible: false,
      nstVisible: false,
      form: form,
      grade: [], // 年级
      subject: [], // 专业
      schoolLevel: [], // 院校类型
      tableData: [],
      tableLoading: false,
      subjectQuery: '',
      canBatch: false,
      paperList: [],
      subjectPage: {
        page: 1,
        total: 0,
        limit: 10
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.grade = this.$dictJson.grade;
    this.schoolLevel = this.$dictJson.pfsnLevel;
    this.getTableList();
    this.getSubject();
  },
  methods: {
    handleJointChange() {
      this.clearSubject();
    },
    clearSubject() {
      this.subjectQuery = '';
      this.subject = [];
      this.subjectPage.page = 1;
      this.getSubject();
    },
    querySubject(query) {
      this.subjectQuery = query;
      this.subjectPage.page = 1;
      this.subject = [];
      setTimeout(() => {
        this.getSubject();
      }, 100);
    },
    loadSubject() {
      if (this.subjectPage.total === this.subject.length) {
        return;
      }
      this.subjectPage.page += 1;
      this.getSubject();
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    handleQUeryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQUeryParams();
      this.$post('getStudentPaperList', params).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    getSubject() {
      const params = {
        sName: this.subjectQuery,
        sId: this.form.unvsId,
        page: this.subjectPage.page,
        rows: this.subjectPage.limit,
        ext1: this.form.pfsnLevel, // 专业层次
        ext2: this.form.grade // 年级
      };
      this.$http.get('/baseinfo/sPfsn.do', { params: params }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          body.data.forEach((item) => {
            const levelName = getTextFromDict('pfsnLevel', item.pfsnLevel);
            item.label = `(${item.pfsnCode})${item.pfsnName}[${levelName}]`;
          });
          this.subject = this.subject.concat(body.data);
        }
      });
    },
    teacherBatchAssgin() {
      if (this.canBatch) {
        this.baVisible = true;
      } else {
        this.nstVisible = true;
      }
    },
    teacherAssgin(row) {
      this.paperList = [];
      this.paperList.push(row.gpId);
      this.baVisible = true;
    },
    handleSelectionChange(val) {
      var rowData = val;
      this.paperList = [];
      rowData.forEach((item) => {
        this.paperList.push(item.gpId);
      });
      if (val.length) {
        this.canBatch = true;
      } else {
        this.canBatch = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped></style>
