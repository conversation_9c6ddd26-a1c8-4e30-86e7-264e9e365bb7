<template>
  <div>
    <el-dialog
      :title="optionsType"
      :visible.sync="rankingShow"
      width="75%"
      :before-close="handleClose"
    >
      <el-form ref="form" :model="form" label-width="110px" class="yz-search-form">
        <el-form-item label="校区：">
          <el-input v-model="form.campusName" placeholder="请选择校区" :disabled="true" />
        </el-form-item>
        <el-form-item label="部门:">
          <el-input v-model="form.dpName" placeholder="请选择部门" :disabled="true" />
        </el-form-item>
        <el-form-item label="姓名:">
          <el-input v-model="form.userName" :disabled="true" />
        </el-form-item>
        <el-form-item label="时间段:">
          <el-date-picker v-model="tiemValue" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="changeTiem" />
        </el-form-item>
        <el-form-item label="操作类型:">
          <el-select v-model="form.type" placeholder="请选择操作类型">
            <el-option label="搜索关键字" value="1" />
            <el-option label="点击量" value="2" />
            <el-option label="收藏量" value="3" />
            <el-option label="问题有用" value="4" />
            <el-option label="问题无用" value="5" />
          </el-select>
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" @click="query">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </el-form>
      <div style="float:right;margin:15px 25px 15px 0;">
        <!-- <el-button type="primary" @click="problemData()">问题数据</el-button> -->
        <el-button type="primary" @click="exportList()">导出Excel</el-button>
      </div>
      <el-table ref="multipleTable" :data="tableData" style="width: 100%; margin: 25px 0" border>
        <el-table-column prop="createTime" label="操作时间" align="center" />
        <!-- <el-table-column prop="dpName" label="部门" align="center" /> -->
        <el-table-column prop="typeName" label="操作类型" align="center" />
        <el-table-column prop="question" label="问题标题/关键词" align="center" />
        <!-- <el-table-column prop="collectTotal" label="收藏量" align="center" />
        <el-table-column prop="questionTotal" label="发布次数" align="center" />
        <el-table-column prop="usefulTotal" label="问题（有用）" align="center" />
        <el-table-column prop="uselessTotal" label="问题（没用）" align="center" /> -->
      </el-table>
      <div class="pageClass">
        <el-pagination
          :current-page.sync="page.currentPage"
          :page-size="page.pageSize"
          :total="page.total"
          :page-sizes="[10, 20, 30, 40]"
          layout="total, prev, pager, next, sizes,jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span> -->
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment';
import { ossUri, downUri } from '@/config/request';
export default {
  props: {
    rankingShow: {
      type: Boolean,
      default: false
    },
    optionsType: {
      type: String,
      default: ''
    },
    showList: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        dpId: '',
        startSearchTime: '',
        endSearchTime: '',
        campusName: '',
        type: '1'
      },
      tiemValue: {},
      tableData: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 1
      }
    };
  },
  watch: {
    showList(newVal, oldVal) {
      if (newVal) {
        this.loadList();

        this.loadForm();
      }
    }
  },
  created() {
  },
  methods: {
    handleClose() {
      this.$emit('dialogVisible', false);
    },
    changeTiem(val) {
      this.form.startSearchTime = moment(this.tiemValue[0]).format('YYYY-MM-DD');
      this.form.endSearchTime = moment(this.tiemValue[1]).format('YYYY-MM-DD');
    },
    query() {
      this.loadList();
    },
    reset() {
      this.tiemValue = {};
      this.form.type = '1';
      this.loadList();
    },
    loadForm() {
      console.log(this.showList, 'showList');
      this.form.campusName = this.showList.campusName;
      this.form.dpName = this.showList.dpName;
      this.form.userName = this.showList.userName;
    },
    exportList() {
      var exportUrl = downUri + '/question/report/exportQuestionUserDetail.do?userId=' + this.showList.userId + 'type=' + this.form.type + '&startSearchTime=' + this.form.startSearchTime + '&endSearchTime=' + this.form.endSearchTime;
      // 导出文件
      window.location.href = exportUrl;
    },
    loadList() {
      const params = {
        // campusId: this.form.searchWord,
        startSearchTime: this.form.startSearchTime ? moment(this.form.startSearchTime).format('YYYY-MM-DD') : '',
        endSearchTime: this.form.endSearchTime ? moment(this.form.endSearchTime).format('YYYY-MM-DD') : '',
        type: this.form.type,
        userId: this.showList.userId,
        start: this.page.currentPage,
        length: this.page.pageSize
      };
      this.$http.post('/question/report/getAllQuestionUserDetailReport.do', params).then(res => {
        if (res.ok) {
          this.tableData = res.body.data;
          this.page.total = res.body.recordsTotal;
        }
      });
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      console.log(`每页 ${val} 条`);
      this.loadList();
    },
    handleCurrentChange(val) {
      this.page.currentPage = val;
      console.log(`当前页: ${val}`);
      this.loadList();
    }

  }
};
</script>

<style>
  .pageClass{
  float: right;
  margin-top: -15px;
}
</style>
