<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form ref="form" label-width="100px" :rules="rules" :model="form">
        <el-form-item label="分支名称：" prop="name">
          <el-input v-model="form.name" placeholder="请输入分支名字" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="ok('form')">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    addBranchs: {
      type: Object,
      default: () => {}
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      form: {
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入分支名称', trigger: 'blur' },
          { max: 30, message: '您已经超过30个字，请勿继续添加！', trigger: 'change' }
        ]
      },
      types: '',
      branchName: ''
    };
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        if (this.type === 'edit') {
          this.form.name = this.addBranchs.name;
        } else {
          this.form.name = '';
        }
      }
    }
  },
  created() {
    // this.form = this.addBranchs;
  },
  methods: {
    handleClose() {
      this.form.name = '';
      this.$emit('addSonDialogVisible', false);
    },
    // 新增/修改-子级菜单
    ok(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          const params = {
            parentId: this.addBranchs.id,
            name: this.form.name
          };
          if (this.type === 'add') {
            this.$http.post('/question/branch/addQuestionBranch.do', params, { json: true }).then(res => {
              if (res.ok) {
                // TODO  需要返回新增的节点的id
                this.$parent.$refs.menuTree.getProblemTreeList();
                // const children = this.addBranchs.children;
                // if (children?.length > 0) {
                //   this.$parent.$refs.menuTree.$refs.elTree.insertBefore({ id: parseInt(params.name), name: params.name }, children[0].id);
                // } else {
                //   this.$parent.$refs.menuTree.$refs.elTree.append({ id: parseInt(params.name), name: params.name }, params.parentId);
                // }
                this.$message.success('添加成功');
              }
            });
          } else {
            params.parentId = this.addBranchs.parentId;
            params.id = this.addBranchs.id;
            this.$http.post('/question/branch/updateQuestionBranchById.do', params, { json: true }).then(res => {
              if (res.ok) {
                // 视图更新
                this.$set(this.addBranchs, 'name', params.name);
                this.$message.success('修改成功');
              }
            });
          }
          this.handleClose();
        }
      });
    }
  }

};
</script>

<style lang="scss" scoped>

</style>
