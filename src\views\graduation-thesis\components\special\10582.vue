<template>
  <!-- 嘉应学院 -->
  <div class="jyxy">
    <div class="">
      <h4>英文标题：</h4>
      <wang-editor :content.sync="data.englishTitle" placeholder="请输入英文版论文标题" />
    </div>
    <div class="">
      <h4>作者姓名：</h4>
      <wang-editor :content.sync="data.englishAuthorName" />
    </div>
    <div class="">
      <h4>作者单位：</h4>
      <wang-editor :content.sync="data.authorUnit" />
    </div>
    <div class="">
      <h4>地址：</h4>
      <wang-editor :content.sync="data.authorAddress" />
    </div>
    <div class="">
      <h4>邮政编码：</h4>
      <wang-editor :content.sync="data.authorPostCode" />
    </div>
    <div class="">
      <h4>英文摘要：</h4>
      <wang-editor :content.sync="data.englishSummary" />
    </div>
    <div class="">
      <h4>英文关键字：</h4>
      <wang-editor :content.sync="data.englishKeyWords" />
    </div>
  </div>
</template>

<script>
import wangEditor from '@/components/Editor/wang-editor';
export default {
  components: {
    wangEditor
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      item: ''
    };
  },
  created() {},
  mounted() {},
  beforeDestroy() {}
};
</script>

<style lang='scss' scoped>

</style>
