<template>
  <common-dialog
    is-full
    :title="newstitle"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main team-member">
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        border
        size="small"
        height="calc(100vh - 80px)"
        style="width: 100%"
        header-cell-class-name='table-header-cell'
        :data="tableData"
      >
        <el-table-column type="index" label="序号" align="center" />
        <el-table-column label="部门" prop="dpName" align="center" />
        <el-table-column label="助学老师姓名" prop="empName" align="center" />
        <el-table-column label="成教业绩调整" prop="cjActivityReduceTotal" align="center" />
        <el-table-column label="国开业绩调整" prop="gkActivityReduceTotal" align="center" />
        <el-table-column label="全日制业绩调整" prop="qrzActivityReduceTotal" align="center" />
        <el-table-column label="自考业绩调整" prop="zkActivityReduceTotal" align="center" />
        <el-table-column label="研究生业绩调整" prop="yjsActivityReduceTotal" align="center" />
        <el-table-column label="职业教育调整" prop="zyjyActivityReduceTotal" align="center" />
        <el-table-column label="海外教育调整" prop="hwActivityReduceTotal" align="center" />
        <el-table-column label="调整业绩合计" prop="reduce" align="center" />
        <template slot="empty">
          <div class="data-null">
            <img src="../../../assets/imgs/helpStudyPkRank/data-null.png" alt="" />
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    query: {
      type: Object,
      default: function() {
        return { pkRange: [], pkType: '', dialogTitle: '' };
      }
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: []
    };
  },
  computed: {
    newstitle() {
      const text = this.query.requestUrl === 1 ? '个人调整业绩情况' : '战队调整业绩情况';
      return this.query.dialogTitle + text;
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    getTableList() {
      console.log(this.query, 'this.query');
      this.tableLoading = true;
      const params = {
        pkActId: this.query.pkActId,
        pkChildId: this.query.pkChildId,
        teamId: this.query.newTeamId
      };
      this.$post(this.query.requestUrl === 1 ? 'pkSingleReducePerformanceDetail' : 'reducePerformanceDetail', params).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body;
        }
        this.tableLoading = false;
      });
    },
    open() {
      this.getTableList();
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .yz-common-dialog__header {
  background: #2B1F54;
  color: #fff;
  border: none;

  .title {
    color: #fff !important;
  }
}

::v-deep .yz-common-dialog__content {
  overflow: hidden;
}

.team-member {
  background: #180E36;

  // 表格
  ::v-deep .el-table {
    color: #fff;
    background: none;
  }

  ::v-deep .el-table tr {
    background: #2B1F54;
    color: #fff;
  }

  ::v-deep .el-table--border {
    border: 1px solid rgba(#fff, 0.1);
  }

  ::v-deep .el-table::before {
    height: 0;
  }

  ::v-deep .el-table th.el-table__cell.is-leaf {
    border: none;
  }

  ::v-deep .table-header-cell {
    background: #4731A6;
    color: #FFFFFF;
  }

  ::v-deep .el-table__cell {
    border-right: 1px solid rgba(#fff, 0.1);
    border-bottom: 1px solid rgba(#fff, 0.1);
  }

  ::v-deep .el-table__row:hover {
    background: #39287C;
  }

  ::v-deep .el-table__body tr:hover>td.el-table__cell {
    background-color: #39287C;
  }

  // 分页容器
  ::v-deep .el-pager li.active {
    color: #409EFF;
    background: linear-gradient(90deg, rgba(95, 153, 254, 0.5) 0%, #396BFF 100%);
    border: none;
  }

  ::v-deep .el-pager li {
    background-color: #180E36;
    border: 1px solid rgba(255, 255, 255, 0.6);
  }

  ::v-deep .el-pagination__total, ::v-deep .el-pagination__jump {
    color: #fff;
  }

  .yz-table-pagination {
    margin-top: 20px;
  }

}

</style>
