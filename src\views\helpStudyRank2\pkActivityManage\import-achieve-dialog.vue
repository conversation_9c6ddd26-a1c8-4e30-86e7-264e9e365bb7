<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    confirmText="开始导入"
    :title="title"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        size='mini'
        :model='form'
        label-width='120px'
      >
        <el-form-item label='模板：'>
          <!-- 学历教育 -->
          <a v-if="type == 4" class="dialog-ahs" :href="templateUrl">下载模板</a>
          <!-- 职业教育 -->
          <a v-else-if="type == 5" class="dialog-ahs" :href="careerTemplateUrl">下载模板</a>
          <el-button v-else type="primary" plain @click="dwnExcelBtn">下载模板</el-button>
        </el-form-item>

        <el-form-item label='选择文件：'>
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            :action="action"
            :data="params"
            :before-upload="beforeUpload"
            :on-exceed="handleExceed"
            :on-success="uploadSuccess"
            :name="field"
            :file-list="fileList"
            multiple
            :limit="1"
            :auto-upload="false"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击选择文件</em></div>
            <div slot="tip" class="el-upload__tip">
              <p>说明：</p>
              <p>{{ config[type].ps }}</p>
              <p style="color: red;">请严格按模板填写，否则可能导致无法准确导入。</p>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-dialog width="26%" title="提示" :visible.sync="innerVisible" append-to-body @close="cancelBtns">
        <div v-html="innerText"></div>
      </el-dialog>
    </div>
  </common-dialog>
</template>

<script>
import { downUri } from '@/config/request';
import { httpPostDownFile } from '@/utils/downExcelFile';

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      require: true,
      default: '1'
    },
    // 上传附带参数
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      form: {},
      fileList: [],
      field: '',
      uploadParams: '',
      action: '',
      templateUrl: downUri + '/excel/performanceV2.xlsx', // 学历教育模板
      careerTemplateUrl: downUri + '/excel/vocationalPerformance.xlsx', // 职业教育模板
      config: {
        // 个人
        '1': {
          title: 'PK人员名单模板',
          action: '/pkTeam/importPersonal', // 导入接口地址
          field: 'importPersonal', // 上传文件字段名
          ps: '(关键列：所属部门，姓名、身份证号，职位。)'
        },
        // 部门
        '2': {
          title: '团队人员名单模板',
          action: '/pkTeam/importPersonal',
          field: 'importPersonal',
          ps: '(关键列：所属部门，姓名、身份证号，职位，是否人力)'
        },
        // 战队
        '3': {
          title: '战队人员名单模板',
          action: '/pkTeam/importPersonal',
          field: 'importPersonal',
          ps: '(关键列：战队PK分组，战队名称、战队总负责人，所属部门)'
        },
        // 学历教育
        '4': {
          title: '学历教育业绩调整模板',
          action: '/pkPerformance/importUpdatePerformanceV2',
          field: 'excelPerformance',
          ps: '(关键列：助学老师姓名，助学老师部门，员工号)'
        },
        // 职业教育
        '5': {
          title: '职业教育业绩调整模板',
          action: '/pkPerformance/importVocationalPerformanceV2',
          field: 'excelPerformance',
          ps: '(关键列，助学老师姓名，助学老师部门，员工号)'
        }
      },
      innerVisible: false,
      innerText: ''
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() { },
    // 获取职业教育导入Excel模板
    dwnExcelBtn() {
      const urs = '/pkTeam/exportPkUserTemplate';
      const title = this.config[this.type]?.title || '模板';
      httpPostDownFile({
        url: urs,
        params: this.params,
        name: title,
        config: { json: false }
      });
    },
    submit() {
      this.$refs.upload.submit();
    },
    uploadSuccess(response) {
      this.$refs.upload.clearFiles();
      this.innerText = response.code !== '00' ? response.msg : response.body;
      this.innerVisible = true;
    },
    cancelBtns() {
      this.innerVisible = false;
      this.$emit('success');
      this.show = false;
    },
    beforeUpload() {
      return new Promise((resolve) => {
        this.$nextTick(() => {
          this.uploadParams = this.params;
          this.field = this.config[this.type].field;
          this.action = this.config[this.type].action;
          resolve();
        });
      });
    },
    handleExceed() {
      this.$message.error('只能上传一个文件!');
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.dialog-ahs {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  margin: 0;
  font-weight: 500;
  font-size: 12px;
  text-align: center;
  padding: 9px 15px;
  border-radius: 4px;
  color: #409EFF;
  border: 1px solid #409EFF;
  background: #ecf5ff;
  transition: .1s;
  -webkit-transition: .1s;
  appearance: none;
  -webkit-appearance: none;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
.dialog-ahs:hover {
  color: #FFFFFF;
  background: #409EFF;
}
</style>

