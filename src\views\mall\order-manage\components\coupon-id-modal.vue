<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    title="订单对应的优惠券id"
    :visible.sync="show"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <div class="flex-box">
        <span class="couponId">优惠券id: {{ couponId }}</span>
        <el-button class="ml10" type="primary" size="mini" @click="handleCopy">复制优惠券id</el-button>
      </div>
    </div>
  </common-dialog>
</template>

<script>
import { copyText } from '@/utils/copyText';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      show: false,
      couponId: ''
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 复制优惠券id
    handleCopy() {
      copyText(this.couponId);
    },
    // 打开弹框
    open() {
      this.getCouponId();
    },
    getCouponId() {
      this.couponId = this.currentId;
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss" scoped>
.common-dialog {
  ::v-deep .yz-common-dialog {
    margin-top: 30vh !important;
  }
}
.flex-box {
  display: flex;
  justify-content: space-between;
  .couponId {
    font-size: 18px;
  }
}
</style>
