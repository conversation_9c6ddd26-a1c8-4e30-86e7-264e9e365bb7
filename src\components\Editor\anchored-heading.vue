<script>
export default {
  name: 'AnchoredHeading',
  props: {
    level: {
      type: Number,
      required: true
    },
    id: {
      type: [Number, String],
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    contenteditable: {
      type: String,
      default: 'true'
    }
  },
  data() {
    return {
      isChecked: false,
      text: this.value
    };
  },
  watch: {
    value(val) {
      // console.log(this.isChecked, 'isChecked');
      // 解决光标跳动BUG
      if (!this.isChecked) {
        this.text = val;
      }
    }
  },
  methods: {
    handleInput(e) {
      this.isChecked = true;
      const val = e.target.innerText;
      this.$emit('input', val);
    },
    handleBlur(e) {
      this.isChecked = false;
      this.text = this.value;
      e.view.blur();
    },
    // 禁止回车
    handleKeyDown(e) {
      if (e.keyCode === 13) {
        e.preventDefault();// 禁用回车的默认事件
      }
    },
    // 处理粘贴事件
    handlePaste(e) {
      e.preventDefault();
      let text;
      const clp = (e.originalEvent || e).clipboardData;
      if (clp === undefined || clp === null) {
        text = window.clipboardData.getData('text') || '';
        console.log(text);
        if (text !== '') {
          if (window.getSelection) {
            const newNode = document.createElement('span');
            newNode.innerHTML = text;
            window.getSelection().getRangeAt(0).insertNode(newNode);
          } else {
            document.selection.createRange().pasteHTML(text);
          }
        }
      } else {
        text = clp.getData('text/plain') || '';
        if (text !== '') {
          document.execCommand('insertHTML', false, text);
        }
      }
    }
  },
  render: function(createElement) {
    let config = {};
    if (this.$slots.default) {
      config = {
        'class': 'bg default',
        attrs: {
          id: this.id
        }
      };
    } else {
      config = {
        'class': 'bg default',
        attrs: {
          id: this.id,
          contenteditable: this.contenteditable,
          placeholder: '在此输入标题...'
        },
        on: {
          input: this.handleInput,
          blur: this.handleBlur,
          keydown: this.handleKeyDown,
          paste: this.handlePaste
        },
        domProps: {
          innerHTML: this.$slots.default ? '' : this.text
        }
      };
    }

    return createElement(
      'h' + this.level, // 标签名称
      config,
      this.$slots.default // 子节点数组
    );
  }
};
</script>

<style lang='scss' scoped>
.bg {
  background: #ffffff;
}

.default {
  outline: none;
}

.default:empty::before {
  content: attr(placeholder);
  color: #C0C4CC;
}

h1 {
  font-size: 26px;
}

h2 {
  font-size: 21px;
}

h3 {
  font-size: 16px;
}

h1,h2,h3 {
  margin: 20px 0 5px 0;
}

</style>
