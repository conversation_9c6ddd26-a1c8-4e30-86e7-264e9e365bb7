<template>
  <el-dialog
    title="利息配置"
    :visible.sync="showRecordPop"
    width="50%"
    :before-close="handleClose"
  >
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      label-width="150px"
    >
      <el-form-item label="收费类型:" prop="type">
        <span>{{ content.recruitType | recruitType }}</span>
      </el-form-item>
      <el-form-item label="支付类型:" prop="type">
        <span>{{ content.typeText }}</span>
      </el-form-item>
      <el-form-item
        label="首付金额:"
        prop="downAmount"
        :rules="{
          required: true,
          message: '请输入',
          trigger: 'blur'
        }"
      >
        <el-input-number
          v-model="ruleForm.downAmount"
          :controls="false"
          :min="0"
          placeholder="请输入"
        />
        <span
          class="tip"
        >* 备注：优惠统一在尾款上扣除，以扣除后的金额申请分期</span>
      </el-form-item>
      <el-form-item label="是否开启:" prop="isEnable">
        <el-radio-group v-model="ruleForm.isEnable">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="ruleForm.isEnable" label="配置分期参数:">
        <el-table :data="ruleForm.details" border style="width: 100%">
          <el-table-column label="支持期数" width="180">
            <template slot-scope="scope">
              <el-checkbox
                v-model="scope.row.isEnable"
                :label="scope.row.period + '期'"
              />
            </template>
          </el-table-column>
          <el-table-column label="配置">
            <template slot-scope="scope">
              <el-form-item
                label="利息:"
                :rules="{
                  required: true
                }"
              >
                <span>{{ scope.row | interestRate }}%</span>
              </el-form-item>
              <el-form-item
                label="利息模式:"
                :prop="'details.' + scope.$index + '.subsidyType'"
                :rules="{
                  required: true,
                  message: '请选择',
                  trigger: 'change'
                }"
              >
                <el-radio-group
                  v-model="scope.row.subsidyType"
                  :disabled="!scope.row.isEnable"
                  @input="(ev) => handleRadio(ev, scope.row)"
                >
                  <el-radio
                    v-for="(item, index) in scope.row.rateList"
                    :key="index"
                    :label="item.subsidyType"
                    :disabled="item.subsidyType === 2"
                  >
                    {{ item.subsidyType | subsidyType }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                label="商贴比例:"
                :prop="'details.' + scope.$index + '.subsidyProportion'"
                :rules="{
                  required: true,
                  message: '请输入',
                  trigger: 'blur'
                }"
              >
                <el-input-number
                  v-model="scope.row.subsidyProportion"
                  :controls="false"
                  :precision="2"
                  :min="0"
                  :max="100"
                  placeholder="请输入"
                  :disabled="true"
                />
                <span style="padding-left: 10px">%</span>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleOk">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTextFromDict } from '@/utils';
import { getMulPrecision, getDividePrecision } from '@/utils/precision';

export default {
  filters: {
    recruitType(val) {
      if (!val) return;
      const name = getTextFromDict('recruitType', val);
      return name;
    },
    interestRate(val) {
      if (!val.rateList) return;
      const indexVal = val.rateList.find((item) => item.subsidyType === val.subsidyType);
      const name = getMulPrecision(indexVal.interestRate, 100);
      return name;
    },
    subsidyType(val) {
      if (!val) return;
      const arr = ['客息', '混合', '贴息'];
      return arr[val - 1];
    }
  },
  props: {
    showRecordPop: {
      type: Boolean,
      default: false
    },
    recordId: {
      type: String | Number,
      default: ''
    }
  },
  data() {
    return {
      content: {},
      ruleForm: {
        downAmount: 0,
        isEnable: 0,
        details: []
      }
    };
  },
  watch: {
    showRecordPop(newVal) {
      if (newVal) {
        this.getRecordDetail();
      }
    }
  },
  methods: {
    getRecordDetail() {
      this.$http
        .get('/instalment/config/queryById', { params: { id: this.recordId }})
        .then((res) => {
          const { fail, body } = res;
          if (!fail) {
            this.content = body;
            this.ruleForm.isEnable = body.isEnable;
            this.ruleForm.downAmount = body.downAmount;
            body.details.map((item) => {
              item.isEnable = item.isEnable === 1;
              item.subsidyProportion = getMulPrecision(item.subsidyProportion, 100);
            });
            this.ruleForm.details = body.details;
            console.log('*****************', body);
            this.getRateList();
          }
        });
    },
    getRateList() {
      this.$http
        .get(`/instalment/config/rate/list?type=${this.content.type}`)
        .then((res) => {
          const { fail, body } = res;
          if (!fail) {
            this.ruleForm.details.map((i, index) => {
              const list = body.filter((j) => i.period === j.period);
              this.$set(this.ruleForm.details, index, { ...i, rateList: list });
            });
          }
        });
    },
    // 取接口数据，不可写死
    handleRadio(index, row) {
      const { subsidyProportion } = row?.rateList[index - 1];
      console.log('*****************', index, subsidyProportion);
      row.subsidyProportion = subsidyProportion || 0;
    },
    handleClose() {
      this.$emit('showRecordPop', false);
    },
    handleOk() {
      const { isEnable, details, downAmount } = this.ruleForm;
      if (!downAmount) {
        return;
      }
      const list = JSON.parse(JSON.stringify(details));
      const enableList = list.filter((item) => item.isEnable);
      if (isEnable && enableList.length === 0) {
        this.$message.warning('请至少选择一个分期配置');
        return;
      }
      list.map((item) => {
        item.isEnable = item.isEnable ? 1 : 0;
        item.subsidyProportion = getDividePrecision(item.subsidyProportion, 100);
      });
      const data = {
        id: this.recordId,
        isEnable: isEnable,
        downAmount,
        details: list
      };
      this.$post('instalmentUpdate', data, { json: true }).then((res) => {
        if (!res?.fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.handleClose();
          this.$parent.getTableList();
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.descriptions {
  ::v-deep tbody:nth-child(2) {
    .el-descriptions-row:nth-child(1) {
      display: none;
    }
  }
  ::v-deep .disabledbg {
    background: #f9f9f9 !important;
  }
  .tip {
    color: red;
    margin-left: 20px;
  }
}

.tip {
  color: red;
  margin-left: 20px;
}
</style>
