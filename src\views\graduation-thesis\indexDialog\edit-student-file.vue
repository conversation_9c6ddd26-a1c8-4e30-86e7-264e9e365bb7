<template>
  <common-dialog
    :show-footer="true"
    width="750px"
    title="编辑学务资料"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        size='mini'
        :model='form'
        label-width='140px'
      >
        <el-form-item label='姓名' prop='stdName'>
          <el-input v-model="form.stdName" disabled />
        </el-form-item>

        <el-form-item label='身份证号码' prop='idCard'>
          <el-input v-model="form.idCard" disabled />
        </el-form-item>

        <el-form-item label='学号' prop='schoolRoll'>
          <el-input v-model="form.schoolRoll" disabled />
        </el-form-item>

        <el-form-item label='年级' prop='grade'>
          <el-input v-model="form.grade" disabled />
        </el-form-item>

        <el-form-item label='院校' prop='unvsName'>
          <el-input v-model="form.unvsName" disabled />
        </el-form-item>

        <el-form-item label='层次' prop='pfsnLevel'>
          <el-input v-model="form.pfsnLevel" disabled />
        </el-form-item>

        <el-form-item label='专业' prop='pfsnName'>
          <el-input v-model="form.pfsnName" disabled />
        </el-form-item>

        <el-form-item label='班主任' prop='tutor'>
          <el-input v-model="form.tutor" disabled />
        </el-form-item>

        <el-form-item label='纸质资料状态' prop='paperDataStatus'>
          <el-select v-model="form.paperDataStatus" disabled>
            <el-option label="未收到" value="0" />
            <el-option label="收到" value="1" />
            <el-option label="收到不合格" value="2" />
            <el-option label="收到且合格" value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label='答辩结果' prop='status'>
          <span>{{ form.status | status }}</span>
        </el-form-item>

        <el-form-item label='论文题目' prop='paperTitle'>
          <el-input v-model="form.paperTitle" placeholder="论文题目" />
        </el-form-item>

        <el-form-item label='指导老师' prop='guideTeacher'>
          <el-input v-model="form.guideTeacher" placeholder="指导老师" />
        </el-form-item>

        <el-form-item label='指导老师职称' prop='guideTeacherJobTitle'>
          <el-input v-model="form.guideTeacherJobTitle" placeholder="100字符以内" />
        </el-form-item>

        <el-form-item label='指导老师邮箱' prop='guideTeacherEmail'>
          <el-input v-model="form.guideTeacherEmail" placeholder="指导老师邮箱" />
        </el-form-item>

        <el-form-item label='指导老师联系电话' prop='guideTeacherPhone'>
          <el-input v-model="form.guideTeacherPhone" placeholder="指导老师联系电话" />
        </el-form-item>

        <el-form-item label='论文提交截止日期' prop='endDate'>
          <el-date-picker
            v-model="form.endDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          />
        </el-form-item>

        <el-form-item label='论文答辩日期' prop='replyDate'>
          <el-date-picker
            v-model="form.replyDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          />
        </el-form-item>

        <el-form-item label='学院(系)' prop='schoolDepartment'>
          <el-input v-model="form.schoolDepartment" placeholder="请输入" />
        </el-form-item>

        <el-form-item label='班级(年级)' prop='className'>
          <el-input v-model="form.className" placeholder="请输入" />
        </el-form-item>

        <el-form-item label='教学点' prop='teachSchool'>
          <el-input v-model="form.teachSchool" placeholder="请输入" />
        </el-form-item>

        <el-form-item label='学生论文提示信息' prop='paperPromptInformation' style="display: block">
          <el-input
            v-model="form.paperPromptInformation"
            type="textarea"
            :rows="6"
            placeholder="请输入学生论文提示信息"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  filters: {
    status(val) {
      if (val === '1') {
        return '不通过';
      }
      if (val === '2') {
        return '通过';
      }
      return '未答辩';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      form: {}
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    formData(val) {
      this.form = JSON.parse(JSON.stringify(val));
    }
  },
  mounted() {},
  methods: {
    open() {
      this.form = JSON.parse(JSON.stringify(this.formData));
      this.getStudentInfo();
    },
    // 获取学生学务信息
    getStudentInfo() {
      const params = {
        gpId: this.form.gpId
      };
      this.$http.post('/graduatePaper/toGraduatePaperEditNew.do', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form = body;
            console.log(body);
          }
        });
    },
    // 提交
    submit() {
      const params = {
        gpId: this.form.gpId,
        paperTitle: this.form.paperTitle,
        guideTeacherJobTitle: this.form.guideTeacherJobTitle,
        guideTeacherEmail: this.form.guideTeacherEmail,
        guideTeacherPhone: this.form.guideTeacherPhone,
        endDate: this.form.endDate,
        replyDate: this.form.replyDate,
        schoolDepartment: this.form.schoolDepartment,
        className: this.form.className,
        teachSchool: this.form.teachSchool,
        guideTeacher: this.form.guideTeacher,
        paperPromptInformation: this.form.paperPromptInformation
      };
      this.$post('graduatePaperEdit', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.show = false;
          }
        });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
