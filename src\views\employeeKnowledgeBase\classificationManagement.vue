<template>
  <div class="yz-base-container">
    <el-row>
      <menu-tree ref="menuTree" isShowMore @problemInfoList="problemInfoList" @handleCrumbs="handleCrumbs" />
      <el-col :span="18" class="yz-base-container-content">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item v-for="item in crumbs" :key="item.id">{{ item.name }}</el-breadcrumb-item>
        </el-breadcrumb>
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%; margin: 25px 0"
          height="calc(100vh - 220px)"
          border
        >
          <el-table-column prop="realName" label="负责人" width="180" align="center" />
          <el-table-column prop="campusName" label="所在校区" width="180" align="center" />
          <el-table-column prop="dpName" label="所在部门" align="center" />
          <el-table-column prop="isStaff" label="是否在职" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isStaff == 1">
                <span style="color:#00B46D">在职</span>
              </span>
              <span v-else-if="scope.row.isStaff == 2">
                <span style="color:#D75C89">离职</span>
              </span>
              <span v-else>
                <span style="color:#D75C89">休假</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pageClass">
          <el-pagination
            :current-page.sync="page.currentPage"
            :page-size="page.pageSize"
            :total="page.total"
            :page-sizes="[10, 20, 30, 40]"
            layout="total, prev, pager, next, sizes,jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
    </el-row>
    <addClassification
      :dialogVisible.sync='addChargeDialogVisible'
      :addBranchs="newBranch"
      @addChargeDialogVisible='addChargeDialogVisible=false'
    />
    <addBranch
      :dialogVisible.sync='addSonDialogVisible'
      :addBranchs="newBranch"
      :type='type'
      :title="title"
      @addSonDialogVisible='addSonDialogVisible=false'
    />
  </div>
</template>

<script>
import menu from './menu';
import addClassification from './addClassification';
import addBranch from './addBranch';
export default {
  components: { menuTree: menu, addClassification, addBranch },
  data() {
    return {
      tableData: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 1
      },
      branchShow: false,
      branchInfo: '',
      crumbs: [],
      loading: true,
      newBranch: {},
      addChargeDialogVisible: false,
      addSonDialogVisible: false,
      title: '',
      type: ''
    };
  },
  created() {},
  methods: {
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.problemInfoList(this.branchInfo);
    },
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.problemInfoList(this.branchInfo);
    },
    problemInfoList(data) {
      this.loading = true;
      this.branchInfo = data;
      const params = {
        branchId: this.branchInfo,
        start: this.page.currentPage,
        length: this.page.pageSize
      };
      this.$http.get('/question/responsible/selectAllBranchResponsibleEmployeeUser.do', { params: params }).then(res => {
        if (res.ok) {
          this.tableData = res.body.page.data;
          this.page.total = res.body.page.recordsFiltered;
        }
        setTimeout(() => {
          this.loading = false;
        }, 200);
      });
    },
    handleDelete(row) {
      this.$confirm('是否删除此菜单负责人？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const datas = {
          responsibleId: row.responsibleId
        };
        this.$http.get('/question/responsible/delectByResponsibleId.do', { params: datas }).then(res => {
          if (res.ok) {
            this.problemInfoList(this.branchInfo);
            this.$message.success('删除成功');
          } else {
            this.$$message.error('删除失败');
          }
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    handleCrumbs(data) {
      this.crumbs = data;
    }
  }
};
</script>

<style lang = "scss" scoped>
.yz-base-container {
  padding: 0;
  border-radius: 0;
}
.yz-base-container-content {
  padding: 27px 25px;
  border-left: 10px solid #f0f2f5;
  box-sizing: content-box;
}
.pageClass{
  float: right;
  margin-top: -15px;
  margin-bottom:35px
}
.dialog{
  div{
    width: 100px;
    height: 25px;
    line-height: 25px;
    border: 1px solid #ccc;
    cursor: pointer;
  }
}
</style>
