<template>

  <div>
    <common-dialog
      class="common-dialog"
      width="1000px"
      title="选择任务导入"
      :visible.sync="show"
      :remindId='remindId'
      @open="open"
      @close="close"
    >
      <div class="dialog-main"></div>
      <!-- 表单 -->
      <el-form
        ref="searchForm"
        size="mini"
        label-width="120px"
        class="yz-search-form"
        :model="form"
        @submit.native.prevent="search"
      >
        <el-form-item label="学服任务" prop="taskId">
          <el-select v-model="form.taskId" v-loadmore="loadMoreTask" clearable placeholder="请选择" filterable :filter-method="taskFilter">
            <el-option
              v-for="item in taskList"
              :key="item.taskId"
              :label="item.taskName"
              :value="item.taskId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否全部完成" prop="taskStatus">
          <el-select v-model="form.taskStatus" clearable placeholder="请选择" filterable>
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="年级" prop="grade">
          <el-select v-model="form.grade" clearable placeholder="请选择" filterable>
            <el-option
              v-for="item in gradeList"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否是目标学员" prop="isChecked">
          <el-select v-model="form.isChecked" clearable placeholder="请选择" filterable>
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="学业编码" prop="learnId">
          <el-input v-model="form.learnId" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="远智编码" prop="yzCode">
          <el-input v-model="form.yzCode" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="用户分群名称" prop="tagGroupId">
          <el-select v-model="form.tagGroupId" v-loadmore="loadMoreTagGroupName" clearable placeholder="请选择" filterable :filter-method="tagGroupNameFilter" @visible-change="tagVisibleChange">
            <el-option
              v-for="item in tagGroupList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <div class="search-reset-box " style="margin-right: 10px; ">
          <el-button
            type="primary"
            icon="el-icon-search"
            native-type="submit"
            size="mini"
          >搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
        </div>
        <!-- 勾选区 -->
        <div class="yz-table-select">
          <div class="select-title">学员阶段</div>
          <el-checkbox-group v-model="checkStatusList">
            <el-checkbox v-for="item in stuStatusList" :key="item.value" :label="item.value" :value="item.value">{{ item.key }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form>
      <!-- 按钮区 -->
      <div class="yz-table-btnbox" style="margin-right: 10px;">
        <el-button
          type="primary"
          size="small"
          @click="addSelect()"
        >添加选中</el-button>
        <el-button
          type="warning"
          size="small"
          @click="deleteSelect()"
        >清除选中</el-button>
        <el-button
          type="primary"
          size="small"
          @click="addAll()"
        >添加全部</el-button>
        <el-button
          type="danger"
          size="small"
          @click="deleteAll()"
        >删除全部</el-button>
      </div>
      <!-- 表格 -->
      <el-table
        ref="table"
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%;"
        header-cell-class-name="table-cell-header"
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          align="center"
          type="selection"
          width="50"
        />
        <el-table-column prop="taskName" label="学服任务名称" align="center" />
        <el-table-column
          label="是否目标学员"
          prop="taskId"
          align="center"
        >
          <template v-slot="scope">
            <div v-if="scope.row.remindId">是</div>
            <div v-else>否</div>
          </template>
        </el-table-column>
        <el-table-column prop="learnId" label="学业编码" align="center" />
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="stdName" label="学员姓名" align="center" />
        <el-table-column prop="grade" label="年级" align="center" />
        <el-table-column prop="recruitType" label="招生类型" align="center" />

        <el-table-column
          label="院校专业"
          width="220"
          align="center"
        >
          <template v-slot="scope">
            <div>
              {{ scope.row.unvsName }};{{ scope.row.pfsnName }}[{{ scope.row.pfsnLevel }}]({{ scope.row.grade }}级)
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="stdStage" label="学员阶段" align="center" />

      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination" style="margin-bottom: 10px">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getRemindTaskStudentList"
        />

      </div>
    </common-dialog>
  </div>
</template>

<script>
const form = {
  remindId: '', // 消息Id
  taskId: '', // 任务Id
  taskStatus: '', // 是否完成
  grade: '', // 年级
  isChecked: '', // 是否是目标学员
  learnId: '', // 学业编码
  yzCode: '', // 远智慧编码
  stdStages: '', // 学员状态集合
  tagGroupId: '' // 用户分群名字id
};

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    remindId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      show: false,
      form: form,
      tableLoading: false,
      tableData: [],
      taskList: [],
      gradeList: [],
      stuStatusList: [], // 已勾选学员状态列表
      checkStatusList: [], // 学员状态可选列表
      checkStudentList: {
        remindId: '',
        taskId: '',
        learnIds: ''
      }, // 已勾选学员数据
      taskPage: {
        start: 1,
        total: 0,
        name: '',
        limit: 10
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tagGroupList: [],
      tagGroupPage: {
        page: 1,
        total: 0,
        sName: '',
        rows: 10,
        businessType: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    'taskPage.name'(val) {
      this.initTaskList();
    },
    'form.taskId'(val) {
      if (!val) {
        this.taskPage.name = '';
        this.initTaskList();
      }
    },
    'tagGroupPage.sName'(val) {
      this.initTagGroupNameList();
    }
  },
  methods: {
    open() {
      this.form.remindId = this.remindId;
      this.getTaskList();
      this.getGradeList();
      this.getStuStatusList();
    },
    getCampusList() {
      this.$post('getCampusList').then((res) => {
        if (res.code === '00') {
          this.campusList = res.body;
        }
      });
    },
    close() {
      // this.$emit('update:visible', false);
      // this.$emit('close');
      this.stuStatusList = [];
      this.checkStatusList = [];
      this.$refs['searchForm'].resetFields();
      this.tableData = [];
      // this.$emit('getPaperDatas');
      this.$emit('closeSelectTaskImport');
    },
    handleQUeryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    // 学服任务列表
    getTaskList() {
      this.$post('getTaskList', this.taskPage).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.taskList = this.taskList.concat(body.data);
          this.taskPage.total = body.recordsTotal;
        }
      });
    },
    taskFilter(val) {
      this.taskPage.name = val;
    },
    loadMoreTask() {
      if (this.taskPage.total === this.taskList.length) {
        return;
      }
      this.taskPage.start += 1;
      this.getTaskList();
    },
    initTaskList() {
      this.taskList = [];
      this.taskPage.start = 1;
      this.getTaskList();
    },
    // 获取年级列表
    getGradeList() {
      this.$post('getDictInfoList', { name: 'grade' }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.gradeList = body;
        }
      });
    },
    // 获取学业状态列表
    getStuStatusList() {
      this.$post('getDictInfoList', { name: 'stdStage' }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.stuStatusList = body;
        }
      });
    },
    getRemindTaskStudentList() {
      this.tableLoading = true;
      let stdStages = '';
      for (let i = 0; i < this.checkStatusList.length; i++) {
        if (i === 0) {
          stdStages = this.checkStatusList[i];
        } else {
          stdStages = stdStages + ',' + this.checkStatusList[i];
        }
      }
      this.form.stdStages = stdStages;
      const params = this.handleQUeryParams();
      this.$post('getRemindTaskStudentList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        if (this.form.tagGroupId) {
          this.getRemindTaskStudentList();
        } else {
          if (this.form.taskId) {
            // this.pagination.page = 1;
            this.getRemindTaskStudentList();
          } else {
            this.$message({
              message: '请选择学服任务',
              type: 'warning'
            });
          }
        }
      }
    },
    handleSelectionChange(val) {
      const rowData = val;
      this.checkStudentList.remindId = this.form.remindId;
      this.checkStudentList.taskId = this.form.taskId;
      let learnIds = '';
      for (let i = 0; i < rowData.length; i++) {
        if (i === 0) {
          learnIds = rowData[i].learnId.toString();
        } else {
          learnIds = learnIds + ',' + rowData[i].learnId.toString();
        }
      }
      this.checkStudentList.learnIds = learnIds;
    },
    addSelect() {
      if (this.checkStudentList.learnIds.length === 0) {
        this.$message({
          message: '请选择学员',
          type: 'warning'
        });
        return;
      }
      this.$post('addRemindTaskStudent', this.checkStudentList, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.$message({
            message: '添加选中成功！',
            type: 'success'
          });
          this.pagination.page = 1;
          this.getRemindTaskStudentList();
        }
      });
    },
    deleteSelect() {
      if (this.checkStudentList.learnIds.length === 0) {
        this.$message({
          message: '请选择学员',
          type: 'warning'
        });
        return;
      }
      this.$post('delRemindStudent', this.checkStudentList, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.$message({
            message: '清除选中成功！',
            type: 'success'
          });
          this.pagination.page = 1;
          this.getRemindTaskStudentList();
        }
      });
    },
    addAll() {
      this.$post('addAllRemindStudentTask', this.form, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.pagination.page = 1;
          this.$message({
            message: '添加全部成功！',
            type: 'success'
          });
          this.getRemindTaskStudentList();
        }
      });
    },
    deleteAll() {
      this.$post('delAllRemindStudentTask', this.form, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.$message({
            message: '删除全部成功！',
            type: 'success'
          });
          this.pagination.page = 1;
          this.getRemindTaskStudentList();
        }
      });
    },
    getTagGroupNameList() {
      this.$post('getTagGroupSelectList', this.tagGroupPage).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tagGroupList = this.tagGroupList.concat(body.data);
          this.tagGroupPage.total = body.recordsTotal;
        }
      });
    },
    loadMoreTagGroupName() {
      if (this.tagGroupPage.total === this.tagGroupList.length) {
        return;
      }
      this.tagGroupPage.page += 1;
      this.getTagGroupNameList();
    },
    tagGroupNameFilter(val) {
      this.tagGroupPage.sName = val;
    },
    initTagGroupNameList() {
      this.tagGroupList = [];
      this.tagGroupPage.page = 1;
      this.getTagGroupNameList();
    },
    tagVisibleChange(visible) {
      if (visible) {
        this.initTagGroupNameList();
      }
    }
  }
};
</script>
<style lang = "scss" scoped>
.yz-table-select {
  display: flex;
  margin: 10px 0;
  // padding: 0 20px;
  .select-title {
    width: 120px;
    padding-right:  10px;
    text-align: right;
  }
}
</style>
