<template>
  <div>
    <el-dialog :title="problemTitle" :visible.sync="dialogVisible" width="55%" :before-close="handleClose" :close-on-click-modal="false">
      <el-form ref="formName" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="问题分类：" prop="crumbs">
          <el-cascader
            v-model="form.crumbs"
            :options="options"
            clearable
            placeholder="请选择问题分类"
          />
        </el-form-item>
        <el-form-item label="问题：" prop="question">
          <el-input v-model="form.question" placeholder="请输入问题" clearable />
        </el-form-item>
        <el-form-item label="搜索关键词：" prop="searchKeyword">
          <el-input v-model="form.searchKeyword" :maxlength="20" placeholder="请输入关键词" clearable />
          <span v-if="searchKeyStatu && form.searchKeyword" style="color:red;margin-bottom:-15px">提示：关键词与已有问题搜索关键词重复，请重写填写</span>
        </el-form-item>
        <el-form-item label="内容：" prop="answer">
          <wang-editor ref="answer" v-model="form.answer" class="editor-color content" :content.sync="form.answer" />
        </el-form-item>
        <el-form-item label="附件" prop="file">
          <el-upload
            ref="uploadRef"
            v-loading="loading"
            element-loading-text="正在上传中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 00)"
            class="upload-demo"
            :action="action"
            multiple
            :on-success="uploadSuccess"
            :on-change="onChangeUpload"
            :before-upload="beforeUpload"
            :before-remove="beforeRemove"
            :file-list="fileList"
          >
            <!-- :limit="1"  :on-exceed="handleExceed" -->
            <el-button class="options-btn" type="primary">选择文件</el-button>
            <span class="upload-tips">（温馨提示：上传文件大小需控制在200M以内）</span>
          </el-upload>
        </el-form-item>
        <el-form-item label="可用时间：" prop="questionStatus">
          <el-radio-group v-model="form.questionStatus">
            <el-radio :label="0">有效期</el-radio>
            <el-radio :label="1">永久有效</el-radio>
            <el-radio :label="2">失效</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.questionStatus == 0 ? true : false" label="有效期时间：" prop="validTime">
          <div class="block">
            <el-date-picker
              v-model="form.validTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTiem"
            />
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel('formName')">取 消</el-button>
        <el-button type="primary" @click="ok('formName')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment';
import wangEditor from '@/components/Editor/wang-editor';
export default {
  // components: {
  //   wangEditor
  // },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    problemInfo: {
      type: Object,
      default: () => {}
    },
    problemTitle: {
      type: String,
      default: ''
    },
    problemType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      components: {
        wangEditor
      },
      form: {
        sysRemindText: '',
        questionStatus: 0,
        answer: '',
        usableStartDate: '',
        usableEndDate: '',
        searchKeyword: '',
        file: '',
        crumbs: '',
        validTime: [],
        question: ''
      },
      loading: false,
      action: '/sysRemind/upload.do',
      fileList: [],
      isShowTime: false,
      problemList: [],
      crumbs: '',
      dataTime: [],
      searchKeyInfo: '',
      options: [],
      searchKeyStatu: false,
      rules: {
        crumbs: [
          { required: true, message: '请输入问题分类', trigger: 'blur' }
        ],
        question: [
          { required: true, message: '请输入问题', trigger: 'blur' }
        ],
        searchKeyword: [
          { required: false, message: '请输入搜索关键词：', trigger: 'blur' }
        ],
        answer: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ],
        questionStatus: [
          { required: true, message: '请选择可用时间', trigger: 'blur' }
        ],
        validTime: [
          { required: true, message: '请选择有效期时间' }
        ]
      },
      attachment: '',
      delIds: [],
      delUrls: []
    };
  },
  watch: {
    problemInfo(val) {
      if (val) {
        // this.edit();
        // this.form = val;
        // let str = '';
        // val.crumbs.reverse();
        // val.crumbs.forEach(item => {
        //   str += item.name + '>';
        // });
        // this.form.crumbs = str.substring(0, str.length - 1);
        // // setTimeout(function() {
        // // }, 500);
        // if (this.$refs['answer']) {
        //   this.$refs['answer'].setContent(val.answer || '');
        // }
        // this.dataTime = [val.startDate, val.endDate];
      }
    },
    'form.searchKeyword'(newVal, oldVal) {
      this.searchKeyword = newVal;
      if (newVal) {
        this.getSearchKey();
      }
    },
    async dialogVisible(val) {
      if (val) {
        this.load();
        this.options = await this.listToTreeData(this.$parent.$refs.menuTree.$data.data);
      }
    }
  },
  created() {
    // this.load();
  },
  methods: {
    edit(id) {
      const params = {
        questionId: id
      };
      this.$http.post('/question/questionDetail.do', params).then(async res => {
        if (res.ok) {
          if (this.$refs['answer']) {
            this.$refs['answer'].setContent(res.body.answer || '');
          }
          this.dataTime = res.body.usableStartDate && res.body.usableEndDate ? [res.body.usableStartDate, res.body.usableEndDate] : [];
          this.form = {
            ...res.body,
            validTime: this.dataTime
          };
          // let str = '';
          if (res.body.crumbs) {
            const crumbs = JSON.parse(res.body.crumbs).reverse();
            // crumbs.forEach(item => {
            //   str += item.name + '>';
            // });
            // this.form.crumbs = str.substring(0, str.length - 1);
            const temp = [];
            crumbs.map((item) => {
              temp.push(item.id);
            });
            this.form.crumbs = temp;
          }
          const annexList = res.body.annexList;
          if (annexList && annexList.length > 0) {
            const temp = [];
            annexList.map((item) => {
              temp.push({
                name: item.annexName,
                url: item.annexUrl,
                annexId: item.annexId
              });
            });
            this.fileList = temp;
          }
        }
      });
    },

    listToTreeData(list) {
      list.forEach(item => {
        item.label = item.name;
        item.value = item.id;
        if (item.children && item.children.length > 0) {
          this.listToTreeData(item.children);
        }
      });
      return list;
    },
    changeTiem(val) {
      this.form.usableStartDate = moment(val[0]).format('YYYY-MM-DD');
      this.form.usableEndDate = moment(val[1]).format('YYYY-MM-DD');
      console.log(this.form.usableStartDate, this.form.usableEndDate, ' this.form.usableStartDate');
    },
    getSearchKey() {
      const params = {
        id: this.problemInfo.id,
        searchKeyword: this.searchKeyword
      };
      this.$http.post('/question/checkSearchKeywordIsRepetition.do', params).then(res => {
        if (res.body) {
          this.searchKeyStatu = true;
        } else {
          this.searchKeyStatu = false;
        }
      });
    },
    load() {
      const params = {
        parentId: '8'
      };
      this.$http.post('/question/branch/selectQuestionBranchListByParentId.do', params).then(async res => {
        if (res.ok) {
          this.problemList = res.body;
        }
      });
    },
    async ok(formName) {
      if (this.form.questionStatus !== 0) {
        this.form.usableStartDate = '';
        this.form.usableEndDate = '';
      }
      if (this.searchKeyStatu && this.form.searchKeyword) {
        this.$message.error('提示：关键词与已有问题搜索关键词重复，请重新填写');
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const uploadFiles = this.$refs.uploadRef.$data.uploadFiles;
          const arr = [];
          uploadFiles.map((item) => {
            if (item.annexId) {
              arr.push({
                annexName: item.name,
                annexUrl: item.url
              });
            } else {
              const i = item.response.body;
              arr.push({
                annexName: i.fileName,
                isNew: '1',
                annexUrl: i.fileUrl
              });
            }
          });
          const index = this.form.crumbs.length - 1;
          const params = {
            branchId: this.form.crumbs[index],
            searchKeyword: this.form.searchKeyword,
            question: this.form.question,
            answer: this.form.answer,
            questionStatus: this.form.questionStatus,
            usableStartDate: this.form.usableStartDate,
            usableEndDate: this.form.usableEndDate,
            resource: arr
          };

          if (this.problemType === 'add') {
            this.$http.post('/question/addQuestion.do', params, { json: true }).then(async res => {
              if (res.ok) {
                this.$message.success('新增成功');
                this.$parent.loadList(params.branchId);
                this.fileList = [];
              }
            });
          } else {
            const temp = {
              ...params,
              questionId: this.problemInfo.questionId,
              isAllow: this.problemInfo.isAllow,
              categoryId: this.problemInfo.categoryId,
              delIds: this.delIds
            };
            this.$http.post('/question/updateQuestion.do', temp, { json: true }).then(async res => {
              if (res.ok) {
                this.$message.success('修改成功');
                this.$parent.loadList(params.branchId);
                this.fileList = [];
              }
            });
          }
          this.fileList = [];
          if (this.$refs['answer']) {
            this.$refs['answer'].setContent('');
          }
          this.$refs[formName].resetFields();
          this.$emit('dialogVisible', false);
        }
      });
      // this.fileList = [];
      // this.form = {};
      // this.dataTime = {};
    },
    cancel(formName) {
      // this.form = {};
      this.fileList = [];
      this.delIds = [];
      // this.dataTime = {};
      if (this.$refs['answer']) {
        this.$refs['answer'].setContent('');
      }
      this.$refs[formName].resetFields();
      this.$emit('dialogVisible', false);
    },
    handleClose() {
      // this.form = {};
      this.fileList = [];
      this.delIds = [];
      // this.dataTime = {};
      if (this.$refs['answer']) {
        this.$refs['answer'].setContent('');
      }

      if (this.$refs.formName) {
        this.$refs.formName.resetFields();
      }
      this.$emit('dialogVisible', false);
    },
    uploadSuccess(response, file, fileList) {
      if (response.code !== '00') {
        this.$nextTick(() => {
          this.loading = false;
          this.fileList = fileList.filter(item => item.response.ok == true);
        });
        this.$message({ message: response.msg, type: 'error', duration: '5000' });
        return false;
      }
      this.$nextTick(() => {
        this.loading = false;
      });
      if (response.code === '00') {
        // this.basicInfos.fileName = response.body.fileName;
        // this.basicInfos.fileUrl = response.body.fileUrl;
        this.$message({
          message: '附件上传成功',
          type: 'success'
        });
        // this.attachment = response.body.fileUrl;
        // this.fileList = fileList;
      }
    },
    beforeUpload(file) {
      const isLt100M = file.size / 1024 / 1024 > 100;
      if (isLt100M) {
        this.$message.error('上传文件大小不能超过200MB!');
        this.loading = false;
        return false;
      }
      this.loading = true;
    },
    onChangeUpload(file, fileList) {
      // console.log(fileList);
      // this.fileList = fileList;
      // const isLt100M = file.size / 1024 / 1024 > 100;
      // if (isLt100M) {
      //   this.$message.error('上传文件大小不能超过200MB!');
      //   const currIdx = this.fileList.indexOf(file);
      //   this.fileList.splice(currIdx, 1);
      //   return;
      // }
      // this.loading = true;
    },
    beforeRemove(file, fileList) {
      if (file.status === 'success') {
        return this.$confirm(`确定移除 ${file.name}？`).then(() => {
          // this.fileList = fileList.filter((item) => item.name !== file.name);
          if (file.annexId) {
            this.delIds.push(file.annexId);
          }
        });
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    }
  }
};
</script>

<style lang = "scss" scoped>
::v-deep .el-breadcrumb {
    line-height: inherit !important;
}
.el-cascader, .el-tag {
    display: flex;
}
</style>
