<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    title="查看物流信息"
    :visible.sync="show"
    :show-footer="false"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <el-descriptions :column="1">
        <el-descriptions-item label="京东订单号"><span style="color: red">{{ form.jdOrderId }}</span></el-descriptions-item>
        <el-descriptions-item label="物流信息">
          <el-steps direction="vertical" :active="0">
            <el-step v-for="(item,index) in form.orderTrack" :key="index" :title="item.content + '--' + item.operator" :description="item.msgTime" />
          </el-steps>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前row
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      form: {}
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框
    open() {
      if (this.row && this.row.orderId) {
        const params = {
          logisticsNo: this.row.logisticsNo,
          productType: this.row.productType
        };
        this.$post('zmLogisticsInfo', params, { json: true }).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form = body;
          }
        });
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang = "scss" scoped>
.des-title {
  font-size: 16px;
  font-weight: 700;
  color: #303133;
  margin: 12px 0;
}
</style>
