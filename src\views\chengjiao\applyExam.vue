<template>
  <div
    v-loading="loading"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    class="wrap"
  >
    <div class="left">
      <div class="block">
        <div class="title">注册信息</div>
        <el-descriptions class="descriptions" :column="2" size="medium" border>
          <el-descriptions-item :span='2' label="姓名">{{ stuInfo.xm }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ stuInfo.xbname }}</el-descriptions-item>
          <el-descriptions-item label="出生日期">{{ stuInfo.csrq }}</el-descriptions-item>
          <el-descriptions-item label="证件类型">{{ stuInfo.zjlxname }}</el-descriptions-item>
          <el-descriptions-item label="证件号">{{ stuInfo.zjdm }}</el-descriptions-item>
          <el-descriptions-item label="民族">{{ stuInfo.mzname }}</el-descriptions-item>
          <el-descriptions-item label="户口所在地">{{ stuInfo.hkname }}</el-descriptions-item>
          <el-descriptions-item label="密码">{{ stuInfo.password }}</el-descriptions-item>
          <el-descriptions-item label="确认密码">{{ stuInfo.password }}</el-descriptions-item>
          <el-descriptions-item label="移动电话" :span='2'>
            {{ stuInfo.lxsj }}<span class="tips">(此手机号码用于绑定您的网上报名号)</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="block">
        <div class="title">基本信息</div>
        <el-descriptions class="descriptions" :column="2" size="medium" border>
          <el-descriptions-item :span='2' label="预报名号" label-class-name="conspicuous" content-class-name="conspicuous">
            {{ stuInfo.username || registerNum }}
          </el-descriptions-item>
          <el-descriptions-item label="居住证信息" :span='2'>{{ stuInfo.jzzszdmc }}</el-descriptions-item>
          <el-descriptions-item label="政治面貌">{{ stuInfo.zzmmname }}</el-descriptions-item>
          <el-descriptions-item label="考试语种" :span='2'>
            {{ stuInfo.wyyzname }}
            <span class="tips">(专升本考生只能选报英语)</span>
          </el-descriptions-item>
          <el-descriptions-item label="考试类型">
            {{ stuInfo.kslxdm === '0' ? '参加考试' : '免试入学' }}<span class="tips">(免试生请选择免试入学)</span>
          </el-descriptions-item>
          <el-descriptions-item label="照顾加分">
            <span class="tips">(山区县和25岁不用勾选)</span>
          </el-descriptions-item>
          <el-descriptions-item label="考试类别">{{ stuInfo.kslbname }}</el-descriptions-item>
          <el-descriptions-item label="报考科类">
            {{ stuInfo.jhlbname }}
            <span class="tips">(报考专升本的外语类专业请选择"文史类")</span>
          </el-descriptions-item>
          <el-descriptions-item label="考试科目组" :span='2'>{{ stuInfo.kmzname }}</el-descriptions-item>
          <el-descriptions-item label="考试县区">{{ stuInfo.xqname }}</el-descriptions-item>
          <el-descriptions-item label="报名点">{{ stuInfo.bmddmmc }}</el-descriptions-item>
          <el-descriptions-item label="考前学历">{{ stuInfo.kqxlname }}</el-descriptions-item>
          <el-descriptions-item label="职业" :span='2'>{{ stuInfo.zyname }}</el-descriptions-item>
          <el-descriptions-item label="毕业学校">{{ stuInfo.byxx }}</el-descriptions-item>
          <el-descriptions-item label="毕业年月">{{ stuInfo.byrq }}</el-descriptions-item>
          <el-descriptions-item label="毕业专业" :span='2'>{{ stuInfo.byzy }}</el-descriptions-item>
          <el-descriptions-item label="毕业证书号" :span='2'>
            {{ stuInfo.byzshm }}
            <span class="tips">(未取得专科毕业证的专科生请输入"待定")</span>
          </el-descriptions-item>
          <el-descriptions-item label="邮政编码">{{ stuInfo.yzbm }}</el-descriptions-item>
          <el-descriptions-item label="固定电话">{{ stuInfo.lxdh }}</el-descriptions-item>
          <el-descriptions-item label="通讯地址" :span='2'>
            <p class="mb-10">
              <span class="mr-50">省： {{ stuInfo.exam_province_name || '' }}</span>
              <span class="mr-50">市： {{ stuInfo.exam_city_name || '' }}</span>
              <span class="mr-50">区（县）：{{ stuInfo.exam_district_name || '' }}</span>
            </p>
            <p>详细地址：{{ stuInfo.txdz }}</p>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="block">
        <div class="title">报考志愿</div>
        <!-- 专升本 -->
        <table v-if="stuInfo.kslbdm === '1'" class="table">
          <thead>
            <tr>
              <th colspan="2">批 次</th>
              <th width="28%">报考院校 (请填写院校代码)</th>
              <th width="56%">报考专业 (请填写专业代码)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colspan="2" rowspan="2" align="left">专科升本科</td>
              <td>院校1： <input v-model="stuInfo.zsbpc1bkyx1" class="input" disabled /></td>
              <td>
                <span class="mr">专业1： <input v-model="stuInfo.zsbpc1bkyx1zy1" class="input" disabled /></span>
                <span>专业2：<input v-model="stuInfo.zsbpc1bkyx1zy2" class="input" disabled /></span>
              </td>
            </tr>
            <tr>
              <td>院校2：<input v-model="stuInfo.zsbpc1bkyx2" class="input" disabled /></td>
              <td>
                <span class="mr">专业1：<input v-model="stuInfo.zsbpc1bkyx2zy1" class="input" disabled /></span>
                <span>
                  专业2：<input v-model="stuInfo.zsbpc1bkyx2zy2" class="input" disabled />
                </span>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 高起专 -->
        <table v-if="stuInfo.kslbdm === '5'" class="table">
          <thead>
            <tr>
              <th colspan="2">批 次</th>
              <th width="28%">报考院校 (请填写院校代码)</th>
              <th width="56%">报考专业 (请填写专业代码)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colspan="1" rowspan="6">高起专</td>
              <td colspan="1" rowspan="2">脱产班</td>
              <td>院校1：<input class="input" disabled /></td>
              <td>
                <span class="mr">
                  专业1：<input class="input" disabled />
                </span>
                <span>
                  专业2：<input class="input" disabled />
                </span>
              </td>
            </tr>
            <tr>
              <td>院校2：<input class="input" disabled /></td>
              <td>
                <span class="mr">
                  专业1：<input class="input" disabled />
                </span>
                <span>
                  专业2：<input class="input" disabled />
                </span>
              </td>
            </tr>
            <tr>
              <td colspan="1" rowspan="3">非脱产班</td>
              <td>院校1：<input v-model="stuInfo.gqgpc4bkyx1" disabled class="input" /> </td>
              <td>
                <span class="mr">
                  专业1：<input v-model="stuInfo.gqgpc4bkyx1zy1" class="input" disabled />
                </span>
                <span>
                  专业2：<input v-model="stuInfo.gqgpc4bkyx1zy2" class="input" disabled />
                </span>
              </td>
            </tr>
            <tr>
              <td>院校2：<input v-model="stuInfo.gqgpc4bkyx2" disabled class="input" /></td>
              <td>
                <span class="mr">
                  专业1：<input v-model="stuInfo.gqgpc4bkyx2zy1" class="input" disabled />
                </span>
                <span>
                  专业2：<input v-model="stuInfo.gqgpc4bkyx2zy2" class="input" disabled />
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="block">
        <div class="title">附件资料下载</div>
        <div class="operate-btn">
          <el-button size="small" type="success" @click="downAllFile">一键下载</el-button>
          <el-button
            v-if="sfzFiles.length > 0"
            size="small"
            type="primary"
            plain
            @click="downPDF('sfz')"
          >下载身份证 (PDF格式)</el-button>
          <el-button
            v-if="jzzFiles.length > 1"
            size="small"
            type="primary"
            plain
            @click="downPDF('jzz')"
          >下载居住证 (PDF格式)</el-button>
          <el-button
            v-if="hkbFiles.length > 1"
            size="small"
            type="primary"
            plain
            @click="downPDF('hkb')"
          >下载户口本 (PDF格式)</el-button>
        </div>

        <!-- 隐藏的元素用于生成pdf -->
        <div class="hidden">
          <img
            v-for="item in jzzFiles"
            :key="item.annexId"
            class="jzz"
            :src="item.annexUrl | splitOssUrl('?'+ imgSize)"
            alt="居中证"
          />
          <img
            v-for="item in hkbFiles"
            :key="item.annexId"
            class="hkb"
            :src="item.annexUrl | splitOssUrl('?'+ imgSize)"
            alt="户口本"
          />
          <img
            v-for="item in sfzFiles"
            :key="item.annexId"
            class="sfz"
            :src="item.annexUrl | splitOssUrl('?'+ imgSize)"
            alt="身份证"
          />
        </div>

        <table class="table">
          <thead>
            <tr>
              <th>附件资料名称</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in idCardFiles" :key="index" class="tr-active">
              <td>{{ item.annexName }}</td>
              <td>
                <el-link type="primary" @click="handleDownFile(item, item.annexUrl, item.annexName)">
                  下载 <i :class="item.loading ? 'el-icon-loading' : 'el-icon-download'"></i>
                </el-link>
              </td>
            </tr>
            <tr v-for="item in sceneConfirmFiles" :key="item.annexId" class="tr-active">
              <td>{{ item.annexTypeName }}</td>
              <td>
                <el-link type="primary" @click="handleDownFile(item, item.annexUrl, item.annexTypeName)">
                  下载 <i :class="item.loading ? 'el-icon-loading' : 'el-icon-download'"></i>
                </el-link>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

    </div>
    <div class="right">
      <iframe
        ref="iframeContainer"
        class="iframe"
        src="/school-paper/#/cj/applyExam/iframe"
        scrolling="no"
        @load="onIframeContainerLoad"
      ></iframe>
    </div>
  </div>
</template>

<script>
import downFile from '@/utils/downFile';
import { ossUri, netNewspaperURL, eeaProxyURL } from '@/config/request';
import buildZipDownload from '@/utils/zip';
import { jsPDF } from 'jspdf';
export default {
  data() {
    return {
      iframeWin: null, // 考试院 iframe页面的 window
      iframeElem: null, // 考试院 iframe 元素
      iframeContainerWin: null,
      studentRegisterNum: null, // 学员预报名号
      learnId: '', // 学员id
      stuInfo: {},
      registerNum: null, // 预报名号
      annexList: [],
      sceneConfirmFiles: [],
      jzzFiles: [], // 身份证,居住证等附件资料地址
      hkbFiles: [], // 户口本,社保等附件资料
      sfzFiles: [], // 身份证 正反面文件路径
      pdfWidth: 19,
      loading: true,
      lastVisitUrl: null,
      imgSize: 'x-oss-process=image/interlace,1/format,jpg/quality,q_30',
      xpcjTimer: null, // 相片采集定时器
      hqkwTimer: null
    };
  },
  computed: {
    idCardFiles() {
      return this.annexList.filter(item => item.isRequire === '1');
    }
  },
  mounted() {
    /**
     * 注意使用这个页面的时候 需要将 main.js 的注释 初始化字典数据代码注释了
     */

    this.learnId = this.$route.query.learnId || '';

    this.getStudentInfo();
    this.getStudentAnnex();
    this.getSceneConfirmFiles();
  },
  methods: {
    downAllFile() {
      this.$message({
        message: '正在下载中，请耐心等待...',
        type: 'success'
      });

      if (this.jzzFiles.length > 1) {
        this.downPDF('jzz');
      }

      if (this.hkbFiles.length > 1) {
        this.downPDF('hkb');
      }

      if (this.sfzFiles.length > 0) {
        this.downPDF('sfz');
      }

      const allFiles = [].concat(this.idCardFiles, this.sceneConfirmFiles);
      const files = [];

      allFiles.forEach(item => {
        const fileName = item.annexName || item.annexTypeName;

        const fileType = '.jpg'; // 默认值
        // const fileSuffixIndex = item.annexUrl.lastIndexOf('.');
        // if (fileSuffixIndex !== -1) {
        //   fileType = item.annexUrl.substring(fileSuffixIndex);
        // }

        files.push({
          fileUrl: ossUri + item.annexUrl + '?v=' + new Date().getTime() + '&' + this.imgSize,
          fileName: this.stuInfo.xm + fileName + fileType
        });
      });

      buildZipDownload(files, `${this.stuInfo.xm}+${this.stuInfo.zjdm}`);
    },
    downPDF(type) {
      let pdfHeight = 0;
      const elems = document.querySelectorAll('.' + type);
      elems.forEach(el => {
        // pdfHeight += el.height;
        pdfHeight += (el.height * 19) / el.width;
      });
      // pdfHeight = (pdfHeight * this.pdfWidth) / 400;

      // eslint-disable-next-line new-cap
      const pdf = new jsPDF({ unit: 'cm', format: [21, pdfHeight + 3] });

      let top = 1;
      for (let i = 0; i < elems.length; i++) {
        const img = elems[i];
        const destHeight = (img.height * 19) / img.width;
        // pdf内容添加图片
        pdf.addImage(img.src + '&v=' + new Date().getTime(), 'jpeg', 1, top, this.pdfWidth, destHeight);
        top = top + destHeight + 1;
      }

      // 保存pdf
      let fileName = '';
      if (type === 'jzz') {
        fileName = this.stuInfo.xm + '居住证.pdf';
      }
      if (type === 'hkb') {
        fileName = this.stuInfo.xm + '户口本.pdf';
      }
      if (type === 'sfz') {
        fileName = this.stuInfo.xm + '身份证.pdf';
      }

      pdf.save(fileName);
    },
    // 更新学员网报状态
    updateStudentRegisterStatus(status) {
      const params = {
        learnId: this.learnId,
        operationType: status, // 1-> 手动网报注册完成 2 -> 手动网报资料提交完成
        username: '',
        password: ''
      };

      if (status === 1) {
        params.username = this.registerNum;
        params.password = this.stuInfo.password;
      }

      this.$post('manualNetworkUpdateStatus', params, { baseURL: netNewspaperURL }).then(res => {
        const { fail } = res;
        if (!fail) {
          if (status === 1) {
            this.$message({
              message: '保存预报名号成功，您的预报名号为：' + this.registerNum,
              type: 'success'
            });
          }
        }
      });
    },
    // 处理单个文件下载
    handleDownFile(item, fileUrl, fileName) {
      item.loading = true;
      fileName = this.stuInfo.xm + fileName + '.jpg';
      const url = ossUri + fileUrl + '?time=' + new Date().getTime() + '&' + this.imgSize;
      downFile(url, fileName, () => {
        item.loading = false;
      });
    },
    // 获取户籍资料文件列表
    getSceneConfirmFiles() {
      const params = {
        learnId: this.learnId,
        start: '0',
        length: '10'
      };

      this.$post('getannexlist', params, { baseURL: netNewspaperURL }).then(res => {
        const { fail, body } = res;
        if (!fail && Array.isArray(body.data)) {
          body.data.forEach((item) => { item.loading = false; });
          this.sceneConfirmFiles = body.data.filter(item => item.annexUrl);
          this.sceneConfirmFiles.forEach(item => {
            // 居住证
            if (item.annexType === 8 || item.annexType === 9) {
              this.jzzFiles.push(item);
            }
            // 户口本
            if (item.annexType === 10 || item.annexType === 11) {
              this.hkbFiles.push(item);
            }
          });
        }
      });
    },
    // 获取附件资料
    getStudentAnnex() {
      const data = {
        learnId: this.learnId,
        recruitType: 1
      };
      this.$post('getAnnexList', data, {
        baseURL: netNewspaperURL
      })
        .then(res => {
          const { fail, body } = res;
          if (!fail && Array.isArray(body.data)) {
            body.data.forEach((item) => { item.loading = false; });
            this.annexList = body.data;
            const identityCard = this.annexList.find(item => item.annexType === '0');
            if (identityCard) {
              this.sfzFiles.push(identityCard);
            }
          }
        });
    },
    // 获取 Iframe 的元素
    getIframeElem(selectors) {
      return this.iframeWin.document.documentElement.querySelector(selectors);
    },
    // 获取学员信息
    getStudentInfo() {
      const params = {
        learnId: this.learnId
      };

      this.$post('getSudentInfo', params, {
        baseURL: netNewspaperURL
      }).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          this.stuInfo = body;

          let timer = setInterval(() => {
            if (this.iframeElem) {
              clearInterval(timer);
              timer = null;
              if (body.username) {
                this.iframeElem.src = eeaProxyURL + '/cr/cgbm/login.jsp';
              } else {
                this.iframeElem.src = eeaProxyURL + '/cr/cgbm/cgzc.jsp';
              }
              this.loading = false;
            }
          }, 100);
        }
      });
    },
    // 填充注册表单信息
    fillRegisterFormData() {
      console.log('填充注册表单信息');
      // 姓名
      this.getIframeElem('#xm').value = this.stuInfo.xm;
      // 性别
      this.getIframeElem(`input[name=xbdm][value='${this.stuInfo.xbdm}']`).checked = true;
      // 出生日期
      this.getIframeElem('#csrq').value = this.stuInfo.csrq;
      // 手机号码;
      this.getIframeElem('#lxsj').value = this.stuInfo.lxsj;
      // 身份证
      this.getIframeElem('#zjdm').value = this.stuInfo.zjdm;
      // 户口所在地
      this.getIframeElem('#hkdm').value = this.stuInfo.hkdm;
      // 身份证类型
      this.getIframeElem('#zjlxdm').value = this.stuInfo.zjlxdm;
      // 密码
      this.getIframeElem('#pwd').value = this.stuInfo.password;
      // 确认密码
      this.getIframeElem('#qrpwd').value = this.stuInfo.password;
      // 民族
      this.getIframeElem('#mzdm').value = this.stuInfo.mzdm;

      // 执行考试院的身份证检验方法
      this.getIframeElem('#zjdm').onchange();

      // 禁用密码输入框
      this.getIframeElem('#pwd').disabled = true;
      this.getIframeElem('#qrpwd').disabled = true;

      // 密码和确认密码输入框明文展示
      this.getIframeElem('#eyes').click();
      this.getIframeElem('#qreyes').click();

      // 禁用出生日期选择和身份证输入，由于嵌套了 考试院网页，会导致选择出生日期的时候，页面卡死
      this.getIframeElem('#csrq').disabled = true;
      this.getIframeElem('#zjdm').disabled = true;
    },
    // 注册成功
    registerSuccess() {
      // 获取预报名号
      this.registerNum = this.getRegisterNum();
      // 更新状态和提交学生的预报名号和密码到Yz系统
      this.updateStudentRegisterStatus(1);
    },
    // 获取报名号
    getRegisterNum() {
      return this.iframeWin.document.documentElement.querySelector('#xm').value;
    },
    // 填写学员基本信息
    fillStudentBaseInfo() {
      console.log('填充学员信息');
      // 居住证信息
      this.getIframeElem('#jzzszd').value = this.stuInfo.jzzszd || '';
      // 政治面貌
      this.getIframeElem('#zzmmdm').value = this.stuInfo.zzmmdm;
      // 考试语种
      this.getIframeElem('#dybmks_wyyzdm').value = this.stuInfo.wyyzdm;
      // 考试类型
      this.getIframeElem('#kslxdm').value = this.stuInfo.kslxdm || 0;
      // 照顾加分
      // this.getIframeElem('#zgjfbj').checked = true;
      // 考生类别
      this.getIframeElem('#kslbdm').value = this.stuInfo.kslbdm;
      this.getIframeElem('#kslbdm').onchange();
      // 报考科类
      this.getIframeElem('#jhlbdm').value = this.stuInfo.jhlbdm;
      // 考试科目组
      this.getIframeElem('#kmzdm').value = this.stuInfo.kmzdm;

      if (typeof this.stuInfo.xqdm === 'string' && this.stuInfo.xqdm.length === 4) {
        // 考试县区
        this.getIframeElem('#xqdm').value = this.stuInfo.xqdm;
        this.getIframeElem('#xqdm').onchange();
      }

      // 报名点
      this.getIframeElem('#bmddm').value = this.stuInfo.bmddm || '';
      // 考前学历
      this.getIframeElem('#kqxl').value = this.stuInfo.kqxl;
      // 职业
      this.getIframeElem('#zydm').value = this.stuInfo.zydm;
      // 毕业学校
      this.getIframeElem('#byxx').value = this.stuInfo.byxx;
      // 毕业年月
      this.getIframeElem('#byrq').value = this.stuInfo.byrq;
      // 毕业年月禁用选择, 为了解决点击页面卡死问题
      // this.getIframeElem('#byrq').disabled = true;
      // 毕业专业
      this.getIframeElem('#byzy').value = this.stuInfo.byzy;
      // 毕业证书号
      this.getIframeElem('#byzshm').value = this.stuInfo.byzshm;
      // 邮政编码
      this.getIframeElem('#yzbm').value = this.stuInfo.yzbm;
      // 固定电话
      this.getIframeElem('#lxdh').value = this.stuInfo.lxdh;
      // 通讯地址--详细地址
      this.getIframeElem('#txdz').value = this.stuInfo.txdz;

      if (this.stuInfo.kslbdm === '1') {
        // 专升本--报考院校1
        this.getIframeElem('#zsbpc1bkyx1').value = this.stuInfo.zsbpc1bkyx1;
        // 专升本--报考专业1
        this.getIframeElem('#zsbpc1bkyx1zy1').value = this.stuInfo.zsbpc1bkyx1zy1;
        // 专升本--报考专业2
        this.getIframeElem('#zsbpc1bkyx1zy2').value = this.stuInfo.zsbpc1bkyx1zy2 || '';

        // 专升本--报考院校2
        this.getIframeElem('#zsbpc1bkyx2').value = this.stuInfo.zsbpc1bkyx2 || '';
        // 专升本--报考专业1
        this.getIframeElem('#zsbpc1bkyx2zy1').value = this.stuInfo.zsbpc1bkyx2zy1 || '';
        // 专升本--报考专业2
        this.getIframeElem('#zsbpc1bkyx2zy2').value = this.stuInfo.zsbpc1bkyx2zy2 || '';
      }

      if (this.stuInfo.kslbdm === '5') {
        // 高起专--报考院校1
        this.getIframeElem('#gqgpc4bkyx1').value = this.stuInfo.gqgpc4bkyx1;
        // 高起专--报考专业1
        this.getIframeElem('#gqgpc4bkyx1zy1').value = this.stuInfo.gqgpc4bkyx1zy1;
        // 高起专--报考专业2
        this.getIframeElem('#gqgpc4bkyx1zy2').value = this.stuInfo.gqgpc4bkyx1zy2 || '';

        // 高起专--报考院校2
        this.getIframeElem('#gqgpc4bkyx2').value = this.stuInfo.gqgpc4bkyx2 || '';
        // 高起专--报考专业1
        this.getIframeElem('#gqgpc4bkyx2zy1').value = this.stuInfo.gqgpc4bkyx2zy1 || '';
        // 高起专--报考专业2
        this.getIframeElem('#gqgpc4bkyx2zy2').value = this.stuInfo.gqgpc4bkyx2zy2 || '';
      }

      // // 等待几毫秒，避免下拉数据还没加载处理，就执行了onchange
      setTimeout(() => {
        // 通讯地址--省
        this.getIframeElem('#sf').value = this.stuInfo.sf || '';
        this.getIframeElem('#sf').onchange();
        // 通讯地址--市
        this.getIframeElem('#ds').value = this.stuInfo.ds || '';
        this.getIframeElem('#ds').onchange();
        // 通讯地址--县
        this.getIframeElem('#xq').value = this.stuInfo.xq || '';
      }, 200);
    },
    // 填充登录页信息
    fillStudentLoginFormData() {
      console.log('填充登录页表单信息');
      // 考试号
      this.getIframeElem('#dlid').value = this.stuInfo.username;
      // 密码
      this.getIframeElem('#pwd').value = this.stuInfo.password;
    },
    // 父容器 Iframe
    onIframeContainerLoad() {
      this.iframeContainerWin = this.$refs.iframeContainer.contentWindow;
      const pathname = this.iframeContainerWin.location.pathname;
      console.log('父iframeURL: ' + pathname);

      // 父Iframe地址被考试院覆盖后,我们把他重定向回来我们的页面
      if (pathname !== '/school-paper/') {
        this.lastVisitUrl = pathname;
        this.$refs.iframeContainer.src = '/school-paper/#/cj/applyExam/iframe';
        return;
      }

      this.iframeElem = this.iframeContainerWin.document.documentElement.querySelector('#cgzcIframe');
      this.iframeElem.addEventListener('load', this.onIframeLoad);

      if (this.lastVisitUrl) {
        this.iframeElem.src = eeaProxyURL + this.lastVisitUrl;
        this.lastVisitUrl = null;
      }
    },
    // 解析表格数据，获取学生报名信息资料
    getStudentApplyInfo() {
      const data = {};
      let lastLabel = null;
      const tableElem = this.getIframeElem('.tlist');
      const trElems = tableElem.querySelectorAll('tr');

      for (let i = 0; i < trElems.length; i++) {
        const tdElems = trElems[i].querySelectorAll('td');
        for (let j = 0; j < tdElems.length; j++) {
          const td = tdElems[j];
          if (td.className === 'ltd') {
            let labelName = td.innerText.replaceAll(/\s+/g, '');
            const index = labelName.indexOf('：');
            if (index !== -1) {
              labelName = labelName.substring(0, index);
            }
            lastLabel = labelName;
          } else {
            if (lastLabel) {
              data[lastLabel] = td.innerText.trim();
            }
          }
        }
      }
      return data;
    },
    // 获取 iframe的 html字符串
    getIframePageHtmlStr() {
      return this.iframeWin.document.documentElement.outerHTML;
    },
    // 获取考试号
    getExamNumber() {
      try {
        const htmlStr = this.iframeElem.document.documentElement.querySelector('font').outerHTML;
        const res = htmlStr.match('考生号为：<font color="red">(.*?)</font>');
        return res[1];
      } catch (e) {
        return '';
      }
    },
    // 处理预报名成功
    handlePreRegistrationSuccess() {
      const params = {
        learnId: this.learnId,
        html: this.getIframePageHtmlStr()
      };
      this.$post('autonomousUpdate', params, { baseURL: netNewspaperURL });
    },
    // 报名成功
    handleApplySuccess() {
      const params = {
        learnId: this.learnId,
        html: this.getIframePageHtmlStr()
      };
      this.$post('registrationConfirmedSuccess', params, { baseURL: netNewspaperURL });
    },
    // 处理考试院页面iframe 加载完成
    onIframeLoad() {
      if (this.xpcjTimer) {
        clearInterval(this.xpcjTimer);
        this.xpcjTimer = null;
      }

      if (this.hqkwTimer) {
        clearInterval(this.hqkwTimer);
        this.hqkwTimer = null;
      }

      this.iframeWin = this.iframeElem.contentWindow;
      const pathname = this.iframeWin.location.pathname;

      // 功能：强制覆盖 parent 避免 iframe 修改父页面的路由
      // 缺陷：会导致考试院报读表单点击日期选择器页面卡死，必要时开启
      this.iframeWin.parent = this.iframeWin;

      if (pathname.indexOf('/cgbm/login.jsp') !== -1) {
        console.log(`(${pathname}): 考试登录页-加载完成`);
        this.fillStudentLoginFormData();
        return;
      }

      if (pathname.indexOf('/cgbm/cgzc.jsp') !== -1) {
        console.log(`(${pathname}): 考生注册页-加载完成`);
        if (!this.loading) {
          this.fillRegisterFormData();
        } else {
          let timer = setInterval(() => {
            if (!this.loading) {
              clearInterval(timer);
              timer = null;
              this.fillRegisterFormData();
            }
          }, 500);
        }
        return;
      }

      if (pathname.indexOf('/cgbm/cgtxzl.jsp') !== -1) {
        console.log(`(${pathname}): 考生预报名信息填写页-加载完成`);

        // 没预报名号,才发送预报名到服务器
        if (!this.stuInfo.username) {
          this.registerSuccess();
        }

        this.fillStudentBaseInfo();
        return;
      }

      if (pathname.indexOf('/cgbm/ybcg.jsp') !== -1) {
        console.log(`(${pathname}): 考生预报名信息预览页-加载完成`);
        this.updateStudentRegisterStatus(2);

        // 解决 h5 获取考位人脸识别弹窗的 iframe 跨域问题
        this.hqkwTimer = setInterval(() => {
          const iframeElem = this.getIframeElem('#rzhy');
          if (iframeElem) {
            const iframeUrl = iframeElem.src;
            const regex = new RegExp('https://www.eeagd.edu.cn');
            if (regex.test(iframeUrl)) {
              iframeElem.src = iframeElem.src.replace('https://www.eeagd.edu.cn', 'https://nrs.yzou.cn');
              console.log('[H5获取考位人脸识别]: 路径替换成功');
            }
          }
        }, 400);

        // [误删] 暂停使用 handlePreRegistrationSuccess 函数
        // this.handlePreRegistrationSuccess();

        // （误删）暂时没用 Start
        const data = this.getStudentApplyInfo();
        console.log(JSON.stringify(data, null, 2));
        // 暂时没用 ENd
        return;
      }

      if (pathname.indexOf('/cgbm/ybmxg.jsp') !== -1) {
        console.log(`(${pathname}): 考生预报名信息修改页-加载完成`);
      }

      if (pathname.indexOf('/cgbm/qrcg.jsp') !== -1) {
        console.log(`(${pathname}): 考生确认报名成功页-加载完成`);

        // [误删] 暂停使用 handleApplySuccess 函数
        // this.handleApplySuccess();

        // （误删）暂时没用 Start
        const data = this.getStudentApplyInfo();
        data['考生号'] = this.getExamNumber();
        console.log(JSON.stringify(data, null, 2));
        // 暂时没用 ENd
      }

      // 相片采集页
      if (pathname.indexOf('/cgbm/xpcj.jsp') !== -1) {
        console.log(`(${pathname}): 相片采集页-加载完成`);
        // 解决 h5 采集照片弹窗的 iframe 跨域问题
        this.xpcjTimer = setInterval(() => {
          const iframeElem = this.getIframeElem('#xspz');
          if (iframeElem) {
            const iframeUrl = iframeElem.src;
            const regex = new RegExp('https://www.eeagd.edu.cn');
            if (regex.test(iframeUrl)) {
              iframeElem.src = iframeElem.src.replace('https://www.eeagd.edu.cn', 'https://nrs.yzou.cn');
              console.log('[H5相片采集方式]: 路径替换成功');
            }
          }
        }, 400);
      }
    }
  }
};
</script>

<style scoped lang="scss">
.wrap {
  display: flex;

  ::v-deep .el-loading-spinner {
    top: 50vh;
  }

}

.title {
  font-weight: bold;
  color: #904209;
  text-align: center;
  padding: 20px 0;
}

.left,.right {
  width: 50%;
  height: 100vh;
}

.left {
  text-align: center;
  font-size: 20px;
  padding: 0 50px 50px 50px;
  overflow: auto;
}

.right {
  border-left: 1px solid #ccc;
  padding-bottom: 50px;
  overflow: hidden;

  .iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}

.descriptions {
  background: #ccc;

  ::v-deep .el-descriptions-item__cell {
    border: 1px solid #ccc !important;
  }

  .tips {
    margin-left: 8px;
    color: red;
  }

}

.operate-btn {
  text-align: center;

  ::v-deep .el-button {
    margin-bottom: 10px;
  }

}

.table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #aaa;
  color: #909399;
  font-size: 14px;

  tr {
    background: transparent;
  }

  td,th {
    padding: 10px;
    border: 1px solid #ccc;
  }

  td {
    color: #606266;
  }

  .input {
    width: 130px;
    height: 21px;
    background: #fff;
    border: 1px solid #ccc;
    color: red;
  }

  .mr {
    margin-right: 8px;
  }

}

.hidden {
  display: none;
}

::v-deep .conspicuous {
  font-weight: bold !important;
  color: #303133 !important;
}

.tr-active {
  &:hover {
    background: rgb(209, 238, 238);
  }
}

.mr-50 {
  margin-right: 50px;
}

.mb-10 {
  margin-bottom: 10px;
}
</style>
