<template>
  <div class="yz-base-container">
    <el-row>
      <menu-tree ref="menuTree" @problemInfoList="loadList" />
      <el-col :span="19" class="yz-base-container-content">
        <el-form ref="form" :model="form" label-width="110px" class="yz-search-form">
          <el-form-item label="问题状态：">
            <el-select v-model="form.questionStatus" placeholder="请选择问题状态" clearable>
              <el-option label="有效" value="0" />
              <el-option label="永久有效" value="1" />
              <el-option label="失效" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建人：">
            <el-input v-model="form.createUser" placeholder="请输入创建人" clearable />
          </el-form-item>
          <el-form-item label="所属部门：">
            <el-select v-model="form.departmentName" filterable clearable placeholder="请选择所属部门">
              <el-option v-for="item in departmentInfo" :key="item.dpId" :label="item.dpName" :value="item.dpId" />
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态：">
            <el-select v-model="form.auditStatus" placeholder="请选择审核状态" clearable>
              <el-option label="待审核" value="0" />
              <el-option label="通过" value="1" />
              <el-option label="不通过" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="问题：">
            <el-input v-model="form.question" placeholder="请输入问题" clearable />
          </el-form-item>
          <el-form-item label="搜索关键词：">
            <el-input v-model="form.searchKeyword" placeholder="请输入搜索关键词" clearable />
          </el-form-item>
          <div class="search-reset-box">
            <el-button type="primary" @click="query">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </el-form>
        <div style="float:right;margin:15px 25px 15px 0;">
          <el-button type="primary" @click="add()">新增</el-button>
          <el-button type="primary" @click="hot()">热门问题</el-button>
          <el-button type="primary" @click="dataReport()">数据报表</el-button>
          <el-button type="primary" @click="batchDel">批量删除</el-button>
        </div>
        <el-table
          ref="multipleTable"
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%; margin: 25px 0"
          height="calc(100vh - 300px)"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="39" />
          <el-table-column prop="question" label="问题" align="center" width='200' />
          <el-table-column prop="answer" label="答案" align="center" width='100'>
            <template slot-scope="scope">
              <span>{{ scope.row.answer.substring(0,10) }}<a @click="mode(scope.row)">更多</a></span>
            </template>
          </el-table-column>
          <el-table-column prop="searchKeyword" label="搜索关键字" align="center" />
          <el-table-column prop="createUser" label="创建人" align="center" width='70' />
          <el-table-column prop="departmentName" label="所属部门" align="center" width='100' />
          <el-table-column prop="updateTime" sortable label="修改日期" align="center" :formatter="dateFormat" width='110' />
          <el-table-column prop="createTime" sortable label="创建日期" align="center" :formatter="dateFormat" width='110' />
          <el-table-column prop="questionStatus" label="问题状态" align="center" width='80'>
            <template slot-scope="scope">
              <span v-if="scope.row.questionStatus ==0">
                <span>有效</span>
              </span>
              <span v-else-if="scope.row.questionStatus ==1">
                <span>永久有效</span>
              </span>
              <span v-else>
                <span>失效</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="auditStatus" label="审核状态" align="center" width='80'>
            <template slot-scope="scope">
              <span v-if="scope.row.auditStatus == 0">
                <span style="color:#666666">待审核</span>
              </span>
              <span v-else-if="scope.row.auditStatus == 1">
                <span style="color:#008000">通过</span>
              </span>
              <span v-else>
                <span style="color:#FF0000">不通过</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="审核说明" align="center" width='100' />
          <el-table-column prop="usableStartDate" label="可用时间" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.startDate+'~'+scope.row.endDate || '' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width='100'>
            <template slot-scope="scope">
              <div class="iconStyle">
                <div class="el-icon-edit-outline" @click="handleEdit(scope.$index, scope.row)"></div>
                <div class="el-icon-delete" @click="handleDelete(scope.$index, scope.row)"></div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pageClass">
          <el-pagination
            :current-page.sync="page.currentPage"
            :page-size="page.pageSize"
            :total="page.total"
            :page-sizes="[10, 20, 30, 40]"
            layout="total, prev, pager, next, sizes,jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
    </el-row>
    <editQuestion ref="editQuestion" :dialogVisible.sync='dialogVisible' :problemType="problemType" :problemInfo="problemInfo" :problemTitle="problemTitle" @dialogVisible='dialogVisible=false' />
    <hotProblem :visible.sync="hotShow" :optionsType="optionsType" @dialogVisible='hotShow=false' />
    <dataReport :visible.sync="dataShow" :optionsType="dataType" @dialogData='dataShow=false' />
    <examine :visible.sync="remindShow" :optionsType="optionsExamineType" :infoList="infoList" @dialogVisible="remindShow=false" />
  </div>
</template>

<script>
import moment from 'moment';
import editQuestion from './editQuestion.vue';
import examine from './examine.vue';
import hotProblem from './hotProblem.vue';
import dataReport from './dataReport.vue';
import menu from './menu';

export default {
  components: { editQuestion, hotProblem, dataReport, examine, menuTree: menu },
  data() {
    const data = [];
    return {
      data: JSON.parse(JSON.stringify(data)),
      tableData: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 1
      },
      form: {},
      dialogVisible: false,
      departmentInfo: [],
      multipleSelection: [],
      hotShow: false,
      dataShow: false,
      optionsType: '',
      dataType: '',
      problemInfo: {},
      editShow: false,
      problemTitle: '',
      problemType: '',
      remindShow: false,
      optionsExamineType: '',
      infoList: {},
      loading: true,
      branchId: 19
    };
  },
  created() {
    this.departmentList();
  },
  methods: {
    listToTreeData(list) {
      list.forEach(item => {
        item.label = item.name;
        if (item.children && item.children.length > 0) {
          this.listToTreeData(item.children);
        }
      });
      return list;
    },
    query() {
      this.loadList(this.branchId);
    },
    reset() {
      this.form = {};
      this.loadList(this.branchId);
    },
    loadList(id) {
      if (this.branchId !== id) {
        this.page.currentPage = 1;
      }
      this.loading = true;
      this.branchId = id;
      const data = {
        questionStatus: this.form.questionStatus || undefined,
        createUser: this.form.createUser || undefined,
        departmentName: this.form.departmentName || undefined,
        auditStatus: this.form.auditStatus || undefined,
        question: this.form.question || undefined,
        searchKeyword: this.form.searchKeyword || undefined,
        branchIds: id ? [id] : [19],
        start: this.page.currentPage,
        length: this.page.pageSize,
        type: 1
      };
      this.$http.post('/question/getAllQuestion.do', data, {
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(res => {
        if (res.ok) {
          res.body.data.forEach(item => {
            if (item.usableStartDate) {
              item.startDate = moment(item.usableStartDate).format('YYYY-MM-DD');
              item.endDate = moment(item.usableEndDate).format('YYYY-MM-DD');
            } else {
              item.startDate = '';
              item.endDate = '';
            }
            item.crumbs = JSON.parse(item.crumbs);
          });
          this.tableData = res.body.data;
          this.page.total = res.body.recordsTotal;
          setTimeout(() => {
            this.loading = false;
          }, 200);
        }
      });
    },
    mode(data) {
      this.infoList = data;
      this.optionsExamineType = '问题详情';
      this.remindShow = true;
    },
    hot() {
      this.hotShow = true;
      this.optionsType = '搜索热门问题管理';
    },
    dataReport() {
      this.dataType = '数据报表（团队）';
      this.dataShow = true;
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.page.pageSize = val;
      this.loadList(this.branchId);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.loadList(this.branchId);
    },
    handleEdit(index, row) {
      this.problemType = 'edit';
      this.problemInfo = row;
      this.problemTitle = '修改问题';
      this.dialogVisible = true;
      // eslint-disable-next-line eqeqeq
      if (this.problemInfo != {}) {
        this.$refs.editQuestion.edit(this.problemInfo.questionId);
      }
    },
    add() {
      this.problemType = 'add';
      this.problemTitle = '添加问题';
      this.dialogVisible = true;
    },
    batchDel() {
      if (this.multipleSelection.length <= 0) {
        this.$message.error('请勾选删除的问题！');
        return false;
      }
      let id = '';
      this.multipleSelection.forEach(item => {
        id += item.questionId + ',';
      });
      this.$confirm('是否删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          questionIds: id
        };
        this.$http.post('/question/delete.do', params).then(res => {
          if (res.ok) {
            this.loadList();
            this.$message.success('删除成功');
          }
        });
      });
    },
    handleDelete(index, row) {
      this.$confirm('是否删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          questionIds: row.questionId
        };
        this.$http.post('/question/delete.do', params).then(res => {
          if (res.ok) {
            this.loadList(row.branchId);
            this.$message.success('删除成功');
          }
        });
      });
    },
    // 列表选中状态
    handleSelectionChange(rows) {
      this.multipleSelection = rows;
    },
    dateFormat(row, column) {
      var date = row[column.property];
      if (date === undefined) {
        return '';
      }
      return moment(date).format('YYYY-MM-DD');
    },
    // 部门列表接口
    departmentList(value) {
      const params = {
        // campusName: this.form.campusName
        campusId: value
      };
      this.$http.post('/dep/selectAllList.do', params).then(res => {
        if (res.ok) {
          this.departmentInfo = res.body;
        }
      });
    }
  }
};
</script>

<style lang = "scss" scoped>
.yz-base-container {
  padding: 0;
  border-radius: 0;
}
.yz-base-container-content {
  padding: 27px 25px;
  border-left: 10px solid #f0f2f5;
  box-sizing: content-box;
}
.custom-tree-node {
  display: flex;
  /* align-items: center; */
}
.pageClass {
  float: right;
  margin-top: -15px;
}
.iconStyle {
  display: flex;
  justify-content: space-evenly;
  font-size:25px;
  /* cursor:pointer; */
  /* text-align:center */
  div{
    cursor:pointer;
  }
}
</style>
