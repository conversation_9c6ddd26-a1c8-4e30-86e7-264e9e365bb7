<template>
  <common-dialog
    class="common-dialog"
    width="100%"
    :isFull="true"
    title="新增退款申请"
    :visible.sync="show"
    :show-footer="false"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <!-- 顶部筛选 -->
      <el-form
        ref="searchForm"
        :model="form"
        label-width="120px"
        class="yz-search-form"
        size="mini"
        @submit.native.prevent='search'
      >
        <el-form-item label="下单人远智编号:" prop="yzCode">
          <el-input v-model="form.yzCode" placeholder="请输入下单人远智编号" clearable />
        </el-form-item>
        <el-form-item label="下单人手机号码:" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入下单人手机号码" clearable />
        </el-form-item>
        <el-form-item label="下单人真实姓名:" prop="realName">
          <el-input v-model="form.realName" placeholder="请输入下单人真实姓名" clearable />
        </el-form-item>
        <el-form-item label="订单号:" prop="orderId">
          <el-input v-model="form.orderId" placeholder="请输入订单号" clearable />
        </el-form-item>

        <div class="search-reset-box">
          <el-button
            type="primary"
            native-type="submit"
            size="mini"
          >查询</el-button>
          <el-button size="mini" @click="search(0)">重置</el-button>
        </div>
      </el-form>
      <el-table ref="table" v-loading="tableLoading" size="small" :data="tableData" style="width: 100%; margin: 25px 0" border>
        <el-table-column prop="orderId" label="订单号" align="center" />
        <el-table-column prop="productId" label="商品id" align="center" />
        <el-table-column prop="productName" label="商品名称" align="center" />
        <el-table-column label="商品类型" align="center">
          <template slot-scope="scope">
            {{ scope.row.productType | goodsTypeEnum }}
          </template>
        </el-table-column>
        <el-table-column prop="productSpecName" label="规格名称" align="center" />
        <el-table-column prop="marketPrice" label="商品单价（元）" align="center" />
        <el-table-column prop="amount" label="购买数量" align="center" />
        <el-table-column prop="totalPrice" label="商品总价（元）" align="center" />
        <el-table-column prop="freightAmount" label="运费（元）" align="center" />
        <el-table-column prop="couponDiscount" label="优惠券抵扣（元）" align="center">
          <template slot-scope="scope">
            <p>-{{ scope.row.couponDiscount }}</p>
          </template>
        </el-table-column>
        <el-table-column prop="zmDiscount" label="智米抵扣（元）" align="center" />
        <el-table-column prop="balanceDiscount" label="余额抵扣（元）" align="center" />
        <el-table-column prop="paymentPrice" label="现金支付（元）" align="center" />
        <el-table-column label="下单用户信息" align="center">
          <template slot-scope="scope">
            <p>远智编号: {{ scope.row.yzCode }}</p>
            <p>真实姓名: {{ scope.row.realName }}</p>
            <p>手机号: {{ scope.row.mobile }}</p>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="下单时间" align="center" />
        <el-table-column label="订单状态" align="center">
          <template slot-scope="scope">
            {{ scope.row.status | logisticsStatusEnum }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status !== 'REFUND'" class="mt10" type="primary" size="small" @click="handleInitiateRefund(scope.row)">发起退款</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
      <initiate-modal :visible.sync="initiateVisible" :currentId="currentId" @success="handleSuccess" />
    </div>
  </common-dialog>
</template>

<script>
import initiateModal from './initiate-refund-modal';
import { arrToEnum } from '@/utils';
import { logisticsStatus, goodsType } from './../../type';
const logisticsStatusEnum = arrToEnum(logisticsStatus);
const goodsTypeEnum = arrToEnum(goodsType);
export default {
  components: {
    initiateModal
  },
  filters: {
    yesOrNoEnum(val) {
      const JudgeEnum = { 0: '禁用', 1: '启用' };
      return JudgeEnum[val] || '/';
    },
    logisticsStatusEnum(val) {
      return logisticsStatusEnum[val] || '/';
    },
    goodsTypeEnum(val) {
      return goodsTypeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      logisticsStatus: logisticsStatus,
      initiateVisible: false, // 发起退款弹框
      currentId: '',
      form: {
        yzCode: '',
        mobile: '',
        realName: '',
        orderId: ''
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableLoading: false,
      tableData: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    handleSuccess() {
      // 向退款订单页面发射事件
      this.$emit('updateList');
    },
    open() {
      this.getTableList();
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    handleInitiateRefund(row) {
      this.$http.get(`/mallRefund/checkApprovalStatus/${row.orderId}`, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          if (body) {
            this.currentId = row.orderId;
            this.initiateVisible = true;
          } else {
            this.$message({
              message: '该订单已提交了退款申请，待审核中，请勿重复提交！',
              type: 'error'
            });
          }
        }
      });
    },
    // 编辑
    handleEdit(row) {
      this.currentId = row.orderId;
      this.autarkyVisible = true;
    },
    // 查询和重置
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 处理筛选参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQueryParams();
      this.$post('getRefundApplyList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>

<style lang = "scss" scoped>
.option-box {
  margin: 15px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    color:red;
  }
}
</style>
