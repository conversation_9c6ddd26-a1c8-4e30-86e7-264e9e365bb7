<template>
  <div class="container">
    <div class="placeholder">
      <div class="box-center">
        <el-button
          v-if="project === 'bst'"
          type="primary"
          size="small"
          @click="$router.go(-1)"
        >返回</el-button>
      </div>
    </div>
    <div class="edit-draft">
      <div class="directory" :style="leftStyle">
        <div class="right">
          <div>
            <div v-show="sidebarShow" class="editor-sidebar">
              <div class="top">
                <div class='title'>论文大纲</div>
              </div>
              <div class="tree">
                <el-scrollbar style="height: 100%">
                  <el-tree
                    ref="tree"
                    :data="treeData"
                    :props="defaultProps"
                    node-key="id"
                    default-expand-all
                    :expand-on-click-node="false"
                  >
                    <div
                      slot-scope="{ node, data }"
                      class="custom-tree-node"
                      @click="anchorPoint(data.id)"
                      @mouseover="data.isMenuShow = true"
                      @mouseout="data.isMenuShow = false"
                    >
                      <span
                        v-show="data.isDisabled"
                        class="label"
                        :title="data.title"
                        placeholder="标题不能为空"
                        v-text="data.title"
                      ></span>
                      <el-input
                        v-if="!data.isDisabled"
                        v-model="data.title"
                        v-el-focus
                        v-width="200"
                        class="font-16"
                        size="mini"
                        :disabled="data.isDisabled"
                        @blur="onBlur(data)"
                      />
                      <!--                    v-show="data.isMenuShow"-->
                      <span v-show="data.isMenuShow" class="menus">
                        <el-tooltip class="item" effect="dark" content="编辑" placement="top">
                          <i class="yz-icon-edit" @click="() => data.isDisabled = !data.isDisabled"></i>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="删除" placement="top">
                          <i class="yz-icon-minus" @click="() => remove(node, data)"></i>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="添加" placement="top">
                          <i class="yz-icon-plus" @click="() => add(node)"></i>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="添加子级" placement="top">
                          <i v-if="data.level < 3" class="yz-icon-child" @click="() => append(data)"></i>
                        </el-tooltip>
                      </span>
                    </div>
                  </el-tree>
                </el-scrollbar>
              </div>

              <div class="bottom">
                <el-row type="flex">
                  <el-col :span="project === 'bms' ? 0 : 12">
                    <el-popover
                      v-model="visible"
                      :class="{ hidden: project === 'bms'}"
                      placement="top"
                      trigger="hover"
                      :content="currentDate + ' 本地自动保存成功！网络良好时系统每隔几分钟自动保存, 请放心填写论文！'"
                    >
                      <p slot="reference" class="tip">{{ currentDate }} 本地自动保存成功</p>
                    </el-popover>
                  </el-col>
                  <el-col :span="project === 'bms' ? 24 : 12">
                    <div class="word-count">论文字数：{{ paperTotal }}</div>
                  </el-col>
                </el-row>
                <el-button type="primary" size="small" plain>
                  <a v-if="templateUrl" :href="templateUrl" target="_blank">下载模板</a>
                </el-button>
                <el-button
                  class="no-default"
                  type="primary"
                  size="small"
                  plain
                  @click="look"
                >论文预览</el-button>
                <el-button v-if="project === 'bst'" type="primary" size="small" plain @click="cachePaper(1)">暂存论文</el-button>
                <el-button type="primary" size="small" @click="submit">提交论文</el-button>
              </div>
            </div>
            <div class="susp" @click="openSidebar">
              <div><i :class="sidebarShow ? 'el-icon-caret-left': 'el-icon-caret-right'"></i></div>
            </div>
          </div>
        </div>
      </div>

      <div class="editor" :style="rightStyle">
        <div class="menu" :style="{ width: rightStyle.width}">
          <ul>
            <li>
              <el-tooltip class="item" effect="dark" content="图片" placement="top">
                <upload-file
                  list-type="text"
                  :action="action"
                  :show-file-list="false"
                  :data="uploadParams"
                  :fileList="fileList"
                  name="file"
                  exts="jpeg|jpg|png|webp"
                  @beforeUpload="beforeUpload"
                  @remove="handleRemoveImg"
                  @success="uploadSuccess"
                >
                  <button>
                    <img width="18px" height="18px" src="../../assets/imgs/icon-picture.png" alt="" />
                  </button>
                </upload-file>
              </el-tooltip>
            </li>
          </ul>
        </div>

        <div class="content">
          <div>
            <anchored-heading :level="1">论文标题：</anchored-heading>
            <wang-editor ref="paperTitle" class="editor-color" :content.sync="paper.attachmentName" />
          </div>
          <div>
            <anchored-heading :level="2">论文摘要：</anchored-heading>
            <wang-editor ref="summary" class="editor-color summary" :content.sync="paper.summary" />
          </div>
          <div>
            <anchored-heading :level="3">论文关键字：</anchored-heading>
            <wang-editor ref="keyWords" class="editor-color" :content.sync="paper.keyWords" />
          </div>
          <editor :tree="treeData" />
          <!-- 参考文献 -->
          <references ref="references" :reference="paper.references" />
          <!-- 特殊化 -->
          <component
            :is="'template'+ unvsCode"
            v-if="templates[unvsCode]"
            :data="paper"
          />
        </div>
      </div>
    </div>
    <demo-video />
  </div>
</template>

<script>
import moment from 'moment';
import editor from '@/components/Editor';
import wangEditor from '@/components/Editor/wang-editor';
import AnchoredHeading from '@/components/Editor/anchored-heading';
import references from '@/components/Editor/references';
import demoVideo from './components/demoVideo';
import defaultDirectory from './default-directory.json';
import { ossUri } from '@/config/request';
import template10582 from './components/special/10582';
import templates from './templates.json';
const proxy = {
  // bst 项目
  bst: [
    '/paper/cacheOnlinePaper.do', // 暂存接口
    '/paper/onlinePaperUpdate.do', // 提交接口
    '/paper/getStudentOnlinePaper', // 获取论文信息
    '/paper/preview', // 预览接口
    '/paper/uploadPicture.do', // 上传
    '/paper/getReferredTemplate.do' // 获取附件模板地址
  ],
  // bms 项目
  bms: [
    '/graduatePaper/cacheOnlinePaper.do', // 暂存接口
    '/graduatePaper/onlinePaperUpdate.do', // 提交接口
    '/graduatePaper/getStudentOnlinePaper', // 获取论文信息
    '/graduatePaper/preview', // 预览接口
    '/graduatePaper/uploadPicture.do', // 上传
    '/graduatePaper/getReferredTemplate.do'// 获取附件模板地址
  ]
};
export default {
  components: {
    editor,
    wangEditor,
    AnchoredHeading,
    references,
    demoVideo,
    template10582
  },
  data() {
    return {
      unvsCodes: ['10582'],
      templates: templates,
      action: '',
      loading: false,
      index: 3,
      editor: null,
      activeName: '1',
      visible: false,
      fileList: [],
      defaultProps: {
        label: 'title'
      },
      paper: {
        // paperTitle: '',
        attachmentName: '',
        summary: '',
        keyWords: '',
        references: [],
        englishSummary: '', // 英文摘要
        englishKeyWords: '', // 英文关键字
        englishTitle: '', // 英文标题
        englishAuthorName: '', // 作者姓名
        authorUnit: '', // 作者单位
        authorAddress: '', // 作者地址
        authorPostCode: '' // 作者邮政编码
      },
      treeData: [],
      saveTip: false,
      uploadParams: {
        id: '1',
        type: 'png',
        size: '200',
        lastModifiedDate: new Date()
      },
      project: '', // 项目类型
      learnId: '', // 学员id
      taskId: '', // 任务id
      attachmentId: '', // 附件id
      paperUploadType: '', // 论文类型
      time: null,
      currentDate: null,
      templateUrl: '', // 论文模板url
      sidebarShow: true,
      isCivil: false, // 是否是土木工程论文
      leftStyle: {
        width: '425px'
      },
      rightStyle: {
        width: '768px'
      },
      textLength: 0,
      unvsCode: null
    };
  },
  computed: {
    // 论文总数
    paperTotal() {
      let length = 0;
      length += this.getText(this.paper.attachmentName).length;
      length += this.getText(this.paper.summary).length;
      length += this.getText(this.paper.keyWords).length;

      const references = this.paper.references;
      references.forEach(item => {
        length += this.getText(item.content).length;
      });

      const recursion = (array) => {
        array.forEach(item => {
          length += this.getText(item.title).length;
          length += this.getText(item.content).length;

          if (item.children && item.children.length > 0) {
            recursion(item.children);
          }
        });
      };
      recursion(this.treeData);
      return length;
    }
  },
  mounted() {
    this.project = this.$route.query.project;
    this.learnId = this.$route.query.learnId;
    this.taskId = this.$route.query.taskId;
    this.paperUploadType = this.$route.query.paperUploadType;
    this.attachmentId = this.$route.query.attachmentId;
    this.unvsCode = this.$route.query.unvsCode || null;

    this.action = proxy[this.project][4];
    this.currentDate = moment().format('HH:mm');
    this.getPaperInfo();
    this.getTemplateUrl();
    this.isCivilEngineering();
    if (this.project !== 'bms') {
      this.timedSave();
    }
  },
  methods: {
    openSidebar() {
      this.sidebarShow = !this.sidebarShow;
      if (this.sidebarShow) {
        // this.leftStyle.width = '768px';
        this.leftStyle.width = '425px';
        this.rightStyle.width = '768px';
      } else {
        // this.leftStyle.width = '1200px';
        this.leftStyle.width = '30px';
        this.rightStyle.width = '1162px';
      }
    },
    onBlur(data) {
      if (data.title.trim() === '') {
        data.title = '新建标题';
        this.$message.error('标题名不能为空');
      }
      data.isDisabled = true;
    },
    getText(str) {
      if (typeof str === 'string') {
        return str.replace(/<[^<>]+>/g, '').replace(/&nbsp;/gi, '');
      } else {
        return '';
      }
    },
    // 判断富文本内容是否为空
    isNull(value) {
      if (value === '') return true;
      const str = value.replace(/<[^<>]+>/g, '').replace(/&nbsp;/gi, '');
      if (str.trim() === '') {
        return true;
      } else {
        return false;
      }
    },
    // 目录定位
    anchorPoint(id) {
      const elem = document.getElementById(id);
      if (elem) {
        elem.scrollIntoView({ block: 'center' });
      }
    },
    // 获取论文模板链接地址
    getTemplateUrl() {
      const params = {
        learnId: this.learnId
      };
      const url = proxy[this.project][5];
      this.$http.post(url, params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.templateUrl = ossUri + body;
          }
        });
    },
    look() {
      const loading = this.$loading({
        lock: true,
        text: '生成论文中，请稍等...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      const params = this.handleParams();
      const url = proxy[this.project][3];
      this.$http.post(url, params, { json: true })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            loading.close();
            const fileUrl = ossUri + body;
            const url = 'https://view.officeapps.live.com/op/view.aspx?src=' + fileUrl + '?v=' + new Date().getTime();
            window.open(url);
          } else {
            loading.close();
          }
        });
    },
    // 定时暂存
    timedSave() {
      // 一分钟暂存一次
      this.time = setInterval(() => {
        this.cachePaper();
      }, 60000);
    },
    // 获取毕业论文信息
    getPaperInfo() {
      const params = {
        learnId: this.learnId,
        taskId: this.taskId,
        paperUploadType: this.paperUploadType, // 0 是老师提交 其他是学生
        attachmentId: this.attachmentId || ''
      };
      const url = proxy[this.project][2];
      this.$http.post(url, params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            const setVal = (array, level) => {
              array.forEach(item => {
                item.level = level + 1;
                item.isMenuShow = false;
                item.isDisabled = true;
                if (item.children && item.children.length > 0) {
                  setVal(item.children, item.level);
                }
              });
            };
            if (body.list.length > 0) {
              setVal(body.list, 0);
              this.treeData = body.list;
            } else {
              this.treeData = defaultDirectory;
            }
            // 如果是土木工程专业，则不显示结语
            if (this.isCivil && this.treeData[this.treeData.length - 1].title === '结语') {
              this.treeData.splice(length - 1, 1);
            }

            this.paper.attachmentName = body.attachmentName;
            this.paper.keyWords = body.keyWords;
            this.paper.summary = body.summary;

            this.paper.englishSummary = body.englishSummary;
            this.paper.englishKeyWords = body.englishKeyWords;
            this.paper.englishTitle = body.englishTitle;
            this.paper.englishAuthorName = body.englishAuthorName;
            this.paper.authorUnit = body.authorUnit;
            this.paper.authorAddress = body.authorAddress;
            this.paper.authorPostCode = body.authorPostCode;

            const list = [
              { content: '' },
              { content: '' },
              { content: '' }
            ];

            if (Array.isArray(body.references)) {
              if (body.references.length <= 0) {
                body.references = list;
              }
            } else {
              body.references = list;
            }

            this.paper.references = body.references;
          }
        });
    },
    // 验证必填项
    validate(data) {
      const length = this.paperTotal;
      if (length < 6000) {
        this.$message.error(`按照高校要求，论文字数需要达到6000字以上，目前您的论文${length}字，请补充内容`);
        return false;
      }

      if (this.isNull(data.attachmentName)) {
        this.$message.error('请填写论文标题！');
        return false;
      }

      if (this.isNull(data.summary)) {
        this.$message.error('请填写论文摘要！');
        return false;
      }

      if (this.isNull(data.keyWords)) {
        this.$message.error('请填写论文关键字！');
        return false;
      }

      // 目录大纲至少要三一个一级标题
      if (data.list.length <= 2) {
        this.$message.error('论文大纲至少三个一级标题和正文！');
        return;
      }

      if (data.list.length > 3) {
        let qualifiedNum = 0; // 合格数
        // 递归
        const recursion = (array) => {
          for (let i = 0; i < array.length; i++) {
            const item = array[i];
            if (!this.isNull(item.content)) {
              qualifiedNum += 1;
              return;
            }
            recursion(item.children);
          }
        };

        data.list.forEach(item => {
          if (!this.isNull(item.content)) {
            qualifiedNum += 1;
          } else {
            recursion(item.children);
          }
        });

        if (qualifiedNum <= 2) {
          this.$message.error('论文大纲至少三个一级标题和带正文！');
          return;
        }
      }

      // 参考文献不能为空
      for (let i = 0; i < data.references.length; i++) {
        const item = data.references[i].content;
        if (this.isNull(item)) {
          this.$message.error('参考文献不能为空！');
          return false;
        }
      }

      return true;
    },
    // 处理参数
    handleParams() {
      const type = this.project === 'bms' ? '0' : this.paperUploadType;

      const paper = JSON.parse(JSON.stringify(this.paper));
      paper.attachmentName = this.getText(paper.attachmentName);
      paper.summary = this.getText(paper.summary);
      paper.keyWords = this.getText(paper.keyWords);
      paper.templateType = this.templates[this.unvsCode];

      paper.englishSummary = this.getText(paper.englishSummary);
      paper.englishKeyWords = this.getText(paper.englishKeyWords);
      paper.englishTitle = this.getText(paper.englishTitle);
      paper.englishAuthorName = this.getText(paper.englishAuthorName);
      paper.authorUnit = this.getText(paper.authorUnit);
      paper.authorAddress = this.getText(paper.authorAddress);
      paper.authorPostCode = this.getText(paper.authorPostCode);

      return {
        learnId: this.learnId,
        taskId: this.taskId,
        attachmentId: this.attachmentId || '',
        paperUploadType: type,
        ...paper,
        list: this.treeData,
        // references: this.$refs.references.list, // 参考文献
        // attachmentName: this.paper.attachmentName,
        attachmentUrl: ''
      };
    },
    // 暂存论文
    cachePaper(type) {
      this.currentDate = moment().format('HH:mm');
      const params = this.handleParams();
      const url = proxy[this.project][0];
      this.$http.post(url, params, { json: true })
        .then(res => {
          const { fail } = res;
          if (!fail) {
            if (type === 1) {
              this.$message({
                message: '暂存成功',
                type: 'success'
              });
            } else {
              this.visible = true;
              setTimeout(() => {
                this.visible = false;
              }, 3000);
            }
          }
        });
    },
    submit() {
      const params = this.handleParams();
      const valid = this.validate(params); // 校检数据

      if (valid) {
        const url = proxy[this.project][1];
        this.$http.post(url, params, { json: true })
          .then(res => {
            const { fail, body } = res;
            if (!fail) {
              this.$message({
                message: '提交成功, 即将关闭此页面',
                type: 'success'
              });
              if (this.project === 'bms') {
                this.$router.replace({
                  path: '/verify',
                  query: {
                    learnId: this.learnId,
                    attachmentId: body.studentAttachmentId,
                    studentId: body.studentAttachmentId,
                    unvsCode: this.unvsCode
                  }
                });
              } else {
                setTimeout(() => {
                  window.top.history.go(-1);
                }, 2000);
              }
            }
          });
      }
    },
    beforeUpload({ type, size }) {
      return new Promise((resolve, reject) => {
        this.$nextTick(() => {
          // ...
          resolve();
        });
      });
    },
    handleRemoveImg({ file, fileList }) {},
    uploadSuccess({ response, file, fileList }) {
      const html = `<img src="${ossUri + response.body}" width="200" height="200">`;
      document.execCommand('insertHTML', false, html);
    },
    append(data) {
      this.index += 1;
      const newChild = {
        id: this.index,
        level: data.level + 1,
        title: '新建标题',
        children: [],
        isMenuShow: false,
        content: '',
        isDisabled: true
      };
      if (!data.children) {
        this.$set(data, 'children', []);
      }
      data.children.push(newChild);
    },
    // 添加同级菜单
    add(node) {
      this.index += 1;
      this.$refs['tree'].insertAfter({
        id: this.index,
        title: '新建标题',
        level: node.level,
        children: [],
        isMenuShow: false,
        content: '',
        isDisabled: true
      }, node);
    },
    remove(node, data) {
      if (this.treeData.length === 1 && node.level === 1) {
        this.$message.error('最后一个字节点不允许删除');
        return;
      }
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex(d => d.id === data.id);
      children.splice(index, 1);
    },
    // 判断是否是土木工程的论文
    isCivilEngineering() {
      var paperName = this.$route.query.templateName;
      var num = paperName.search('国开1001（土木工程本科论文模板）');
      if (num !== -1) {
        this.isCivil = true;
      }
    }

  }
};
</script>

<style lang='scss' scoped>
body {
  background: #FAFAFA;
}
.container {
  background: #FAFAFA;
  min-height: 100vh;
  padding-top: 52px;
}

.word-count {
  font-size: 14px;
  padding: 8px 0;
}

.placeholder {
  width: 100%;
  height: 52px;
  background: #FAFAFA;
  position: fixed;
  top: 0;
  z-index: 666;
}

.no-default {
  ::v-deep .is-plain:active {
    color: #409EFF;
    background: #ecf5ff;
    border-color: #b3d8ff;
  }
}

.font-16 {
  ::v-deep .el-input__inner {
    font-size: 16px;
    color: #3A3B3D;
    font-weight: 600;
  }
}

.directory {
  height: 90vh;
  display: inline-block;
  vertical-align: top;
}

.box-center {
  margin: 0 auto;
  max-width: 1200px;
  min-width: 1200px;
  height: 100%;
  overflow: hidden;
  .el-button {
    margin: 10px 0;
  }
}

.edit-draft {
  margin: 0 auto;
  max-width: 1200px;
  min-width: 1200px;
  overflow: hidden;

  .editor {
    width: 768px;
    min-height: 90vh;
    display: inline-block;
    vertical-align: top;
    background: #ffffff;
    position: relative;
    overflow: hidden;
    //transition: .2s;
    border: 1px solid #c9d8db;
    border-top: none;
    margin-left: 7px;

    .content {
      border-radius: 10px;
      padding: 65px 20px 100px 20px;
    }

    .menu {
      width: 766px;
      position: fixed;
      text-align: center;
      height: 65px;
      background: #fff;
      z-index: 1000;
      border: 1px solid #c9d8db;
      border-left: none;
      display: flex;
      justify-content: center;
      ul {
        padding: 12px 0;
        li {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          cursor: pointer;

          button {
            background: transparent;
            border: none;
            outline: none;
          }

          &:hover {
            background-color: #F6F6F6;
          }

          i {
            font-size: 18px;
            cursor: pointer;
          }
        }
      }
    }

  }

  // 哎，，从右边到左边 名字不改了
  .right {
    //width: 408px;
    height: 90vh;
    //margin-left: 8px;
    display: inline-block;
    position: fixed;
    top: 52px;
    overflow: hidden;
  }

  .editor-sidebar {
    width: 408px;
    height: 90vh;
    background: #ffffff;
    border-radius: 10px;
    display: inline-block;
    vertical-align: top;

    .tree {
      border: 1px solid #c9d8db;
      border-right: none;
      border-top: none;
      height: calc(90vh - 145px);
      //overflow: auto;
    }

    .top {
      display: flex;
      justify-content: space-between;
      height: 65px;
      line-height: 65px;
      padding: 0 10px;
      border: 1px solid #D8D8D8;
      border-right: none;

      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .notice {
        font-size: 14px;
        color: #C0C4CC;
      }

    }

    .bottom {
      width: 408px;
      height: 82px;
      position: absolute;
      bottom: 0;
      padding-bottom: 16px;
      text-align: center;
      border: 1px solid #D8D8D8;
      border-right: none;
      background: #fff;

      .tip {
        padding: 8px 0;
        color: #F56C6C;
        font-size: 14px;
        user-select: none;
      }
    }

    .hidden {
      visibility: hidden;
    }
  }
}

.susp {
  width: 17px;
  height: 90vh;
  display: inline-block;
  position: relative;
  border-left: 1px solid #c9d8db;

  div {
    width: 17px;
    height: 102px;
    line-height: 102px;
    text-align: center;
    background: url("../../assets/imgs/susp.png") no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    user-select: none;

    &:hover {
      background: url("../../assets/imgs/susp_h.png") no-repeat;
      background-size: 100% 100%;
      color: #fff;
    }
  }
}

::v-deep .el-tree-node__content {
  height: 46px;
}

::v-deep .el-tree-node__expand-icon {
  color: initial;
  font-size: 14px;
}

::v-deep .el-tree-node__expand-icon.is-leaf {
  color: transparent;
}

::v-deep .custom-tree-node {
  font-size: 16px;
  color: #3A3B3D;
  font-weight: 600;
  padding: 15px 5px 15px 5px;
  flex: 1;
  //display: flex;
  //align-items: center;
  //justify-content: space-between;
  position: relative;

  .label {
    width: 340px;
    height: 18px;
    display: block;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;

    &:empty::before {
      content: attr(placeholder);
      color: #C0C4CC;
    }
  }

  .menus {
    position: absolute;
    right: 0;
    top: 13px;
    padding-right: 10px;
    background-color: #F5F7FA;
  }

  i {
    margin-left: 14px;
  }
}

::v-deep .el-input.is-disabled .el-input__inner {
  cursor: pointer;
  background-color: transparent;
  border-color: transparent;
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

.editor-color {
  ::v-deep .w-e-text p,
  .w-e-text h1,
  .w-e-text h2,
  .w-e-text h3,
  .w-e-text h4,
  .w-e-text h5,
  .w-e-text table,
  .w-e-text pre {
    color: #606266;
  }
}

.summary {
  ::v-deep .w-e-text p,
  .w-e-text h1,
  .w-e-text h2,
  .w-e-text h3,
  .w-e-text h4,
  .w-e-text h5,
  .w-e-text table,
  .w-e-text pre {
    margin: 13px 0;
    text-indent: 2em;
    color: #606266;
  }

  //::v-deep .w-e-text-container .placeholder {
  //  left: 2.2em;
  //}

}

</style>
