<template>
  <common-dialog
    :show-footer="true"
    width="600px"
    title="纸质资料状态"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        size='mini'
        :model='form'
        :rules="rules"
        label-width='120px'
      >
        <el-form-item label='纸质资料状态' prop='paperDataStatus'>
          <el-select v-model="form.paperDataStatus" placeholder="请选择">
            <el-option label="未收到" value="0" />
            <el-option label="收到" value="1" />
            <el-option label="收到不合格" value="2" />
            <el-option label="收到且合格" value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label='备注' prop='checkPaperRemark' style="display: block">
          <el-input
            v-model="form.checkPaperRemark"
            type="textarea"
            :rows="6"
            placeholder="请输入备注"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      form: {
        paperDataStatus: '',
        checkPaperRemark: ''
      },
      rules: {
        paperDataStatus: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        checkPaperRemark: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {
      this.getInfo();
    },
    // 获取纸质资料信息
    getInfo() {
      const params = {
        gpId: this.row.gpId,
        paperDataStatus: this.row.paperDataStatus,
        learnId: this.row.learnId,
        checkPaperRemark: this.row.checkPaperRemark
      };
      this.$post('getPaperDataStatus', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.paperDataStatus = body.paperDataStatus;
            this.form.checkPaperRemark = body.checkPaperRemark;
          }
        });
    },
    submit() {
      const params = {
        gpId: this.row.gpId,
        learnId: this.row.learnId,
        paperDataStatus: this.form.paperDataStatus,
        checkPaperRemark: this.form.checkPaperRemark,
        'admin-role-save': ''
      };
      this.$post('updatePaperStatus', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.show = false;
          }
        });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
