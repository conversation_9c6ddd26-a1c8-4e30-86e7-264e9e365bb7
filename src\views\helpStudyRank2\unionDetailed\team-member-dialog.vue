<template>
  <common-dialog
    is-full
    :title="query.dialogTitle + '战队人力情况'"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main team-member">
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        border
        size="small"
        style="width: 100%"
        height="calc(100vh - 80px)"
        header-cell-class-name='table-header-cell'
        :data="tableData"
      >
        <el-table-column label="员工号" prop="staffNo" align="center" />
        <el-table-column label="助学老师姓名" prop="empName" align="center" />
        <el-table-column label="所属部门" prop="dpName" align="center" />
        <el-table-column label="岗位" prop="jobTitle" align="center" />
        <el-table-column label="是否在职" prop="empStatus" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.empStatus | empStatus }}</span>
          </template>
        </el-table-column>
        <el-table-column label="参加PK" prop="isJoin" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.isJoin === '1' ? '参加' : '不参加' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否计算人力" prop="manPower" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.manPower === "1" ? '是': '否' }}</span>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="data-null">
            <img src="../../../assets/imgs/helpStudyPkRank/data-null.png" alt="" />
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>
    </div>
  </common-dialog>
</template>

<script>
export default {
  filters: {
    empStatus(val) {
      if (!val) return;
      const data = {
        '1': '在职',
        '2': '离职',
        '3': '休假'
      };
      return data[val];
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    query: {
      type: Object,
      default: function() {
        return { pkRange: [], pkType: '', dialogTitle: '' };
      }
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    getTableList() {
      this.tableLoading = true;
      const params = {
        pkChildId: this.query.pkChildId,
        teamId: this.query.newTeamId
      };
      this.$post('clanPersonList', params).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body;
        }
        this.tableLoading = false;
      });
    },
    open() {
      this.getTableList();
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .yz-common-dialog__header {
  background: #2B1F54;
  color: #fff;
  border: none;

  .title {
    color: #fff !important;
  }
}

::v-deep .yz-common-dialog__content {
  overflow: hidden;
}

.team-member {
  //min-height: calc(100vh - 30px);
  background: #180E36;

  // 表格
  ::v-deep .el-table {
    min-height: calc(100vh - 80px);
    color: #fff;
    background: none;
  }

  ::v-deep .el-table tr {
    background: #2B1F54;
    color: #fff;
  }

  ::v-deep .el-table--border {
    border: 1px solid rgba(#fff, 0.1);
  }

  ::v-deep .el-table::before {
    height: 0;
  }

  ::v-deep .el-table th.el-table__cell.is-leaf {
    border: none;
  }

  ::v-deep .table-header-cell {
    background: #4731A6;
    color: #FFFFFF;
  }

  ::v-deep .el-table__cell {
    border-right: 1px solid rgba(#fff, 0.1);
    border-bottom: 1px solid rgba(#fff, 0.1);
  }

  ::v-deep .el-table__row:hover {
    background: #39287C;
  }

  ::v-deep .el-table__body tr:hover>td.el-table__cell {
    background-color: #39287C;
  }

  // 分页容器
  ::v-deep .el-pager li.active {
    color: #409EFF;
    background: linear-gradient(90deg, rgba(95, 153, 254, 0.5) 0%, #396BFF 100%);
    border: none;
  }

  ::v-deep .el-pager li {
    background-color: #180E36;
    border: 1px solid rgba(255, 255, 255, 0.6);
  }

  ::v-deep .el-pagination__total, ::v-deep .el-pagination__jump {
    color: #fff;
  }

  .yz-table-pagination {
    margin-top: 20px;
  }
}
</style>
