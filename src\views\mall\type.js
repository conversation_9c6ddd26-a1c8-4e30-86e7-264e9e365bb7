// 商品类型
export const goodsType = [
  {
    name: '自营好物',
    value: 'SELF_ENTITY_PRODUCT'
  },
  {
    name: '虚拟产品',
    value: 'SELF_VIRTUAL_PRODUCT'
  },
  {
    name: '京东百货',
    value: 'JD_PRODUCT'
  }
];

// 商品状态
export const goodsStatus = [
  {
    name: '进行中',
    value: 1
  },
  {
    name: '即将开始',
    value: 2
  },
  {
    name: '已结束',
    value: 3
  }
];

// 京东百货商品类型
export const jdGoodsType = [
  {
    name: '京东实物',
    value: 'JD_ENTITY_PRODUCT'
  },
  {
    name: '京东E卡',
    value: 'JD_CARD_PRODUCT'
  }
];

// 跳转页面类型
export const jumpPageType = [
  {
    name: '商品详情页',
    value: 'PRODUCT'
  },
  {
    name: '商品活动页',
    value: 'ACTIVITY'
  }
];

// 物流类型
export const logisticsStatus = [
  {
    name: '待发货',
    value: 'WAIT_SEND'
  },
  {
    name: '待收货',
    value: 'WAIT_RECEIVE'
  },
  {
    name: '已签收',
    value: 'RECEIVED'
  },
  {
    name: '已拒收',
    value: 'REJECT'
  },
  {
    name: '已退款',
    value: 'REFUND'
  }
];

// 展示状态
export const showTimeStatus = [
  {
    name: '永久生效',
    value: 'PERMANENT_EFFECTIVE'
  },
  {
    name: '结束时间',
    value: 'TIME_EFFECTIVE'
  }
];

// 快递公司
export const logisticsType = [
  {
    name: '顺丰',
    value: 'SF'
  },
  {
    name: '百世',
    value: 'HTKY'
  },
  {
    name: '中通',
    value: 'ZTO'
  },
  {
    name: '申通',
    value: 'STO'
  },
  {
    name: '圆通',
    value: 'YTO'
  },
  {
    name: '韵达',
    value: 'YD'
  },
  {
    name: '邮政',
    value: 'YZPY'
  },
  {
    name: 'EMS',
    value: 'EMS'
  },
  {
    name: '天天',
    value: 'HHTT'
  },
  {
    name: '京东',
    value: 'JD'
  },
  {
    name: '优速',
    value: 'UC'
  },
  {
    name: '德邦',
    value: 'DBL'
  }
];

// 退款状态
export const refundStatus = [
  {
    name: '未退款',
    value: '0'
  },
  {
    name: '退款成功',
    value: '1'
  },
  {
    name: '退款失败',
    value: '2'
  }
];

// 退款审核状态
export const refundExamineStatus = [
  {
    name: '未审核',
    value: '2'
  },
  {
    name: '审核通过',
    value: '3'
  },
  {
    name: '审核驳回',
    value: '4'
  }
];

// 优惠券类型
export const couponType = [
  {
    name: '满减券',
    value: 1
  },
  {
    name: '折扣券',
    value: 2
  }
];

// 优惠券状态
export const couponStatus = [
  {
    name: '未使用',
    value: 1
  },
  {
    name: '已使用',
    value: 3
  },
  {
    name: '已过期',
    value: 4
  }
];

// 领券方式
export const couponNeckType = [
  {
    name: '详情页领取',
    value: 1
  },
  {
    name: '后台赠送',
    value: 2
  },
  {
    name: '领券中心',
    value: 3
  }
];

// 优惠券上架状态
export const couponShelfStatus = [
  {
    name: '上架',
    value: 1
  },
  {
    name: '下架',
    value: 0
  }
];

// 优惠券适用用户
export const couponUserType = [
  {
    name: '全部用户',
    value: 1
  },
  {
    name: '未下单用户',
    value: 2
  },
  {
    name: '已下单用户',
    value: 3
  }
];

// 优惠券详情页是否展示
export const couponDetailShowStatus = [
  {
    name: '是',
    value: 1
  },
  {
    name: '否',
    value: 0
  }
];

// 赠送状态
export const giveStatus = [
  {
    name: '赠送异常',
    value: 0
  },
  {
    name: '已赠送',
    value: 1
  },
  {
    name: '赠送中',
    value: 2
  }
];
