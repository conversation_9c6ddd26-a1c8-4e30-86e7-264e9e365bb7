<template>
    <div>
        <el-dialog title="导入" :visible.sync="showInfo" width="30%" :before-close="handleClose"  top="5vh">
        <el-form label-width="80px">
            <el-form-item label="模板：">
                <el-button @click="downFile">下载模板</el-button>
                <div>说明：</div>
                <div>（关键列：学员姓名，学籍编码）</div>
                <div>请严格按照模板填写，否则可能导致无法准确导入</div>
            </el-form-item>
            <el-form-item label="导入：">
                <div style="display: flex;">
                    <el-upload class="upload-demo" ref="upload" action="/msgManage/import" :on-change="handleChange"
                        :data="uploadFileParams" :file-list="fileList" :auto-upload="false" :on-success="uploadSuccess"
                        @on-remove="handleRemove">
                        <el-button slot="trigger" size="small" type="primary">浏览文件</el-button>
                    </el-upload>
                    <el-input v-model="fileName" :disabled="true"></el-input>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="submitUpload">开始导入</el-button>
                <el-button type="primary" @click="handleClose">取消</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="showErr" width="30%" :before-close="handleClose1"  top="5vh">
            <h3 v-if="total >= 0">一共{{ total }}条数据：格式正确{{ succeedCount }},格式错误{{ failedCount }}条</h3>
            <el-table :data="tableData" style="width: 100%" border  v-loading="loading">
                <el-table-column prop="stuName" label="姓名" align="center">
                </el-table-column>
                <el-table-column prop="learnId" label="学业编码" align="center">
                </el-table-column>
            </el-table>
            <div v-if="failedCount > 10" style="text-align: center;margin-top: 10px;font-size: 25px;">...</div>
            <div v-if="failedCount > 0" style="text-align: center;margin-top: 10px;font-size: 15px;">请修改后重新导入</div>
        </el-dialog>

    </div>
</template>

<script>
export default {
    props: {
        showInfo: {
            type: Boolean,
            default: false
        },
        id: {
            type: Number,
        }
    },
    data() {
        return {
            fileName: '',
            fileList: [],
            action: '',
            uploadFileParams: {
                mpMsgId: '',

            },
            tableData: [],
            succeedCount: 0,
            failedCount: 0,
            total: 0,
            showErr:false,
            loading:true

        };
    },
    methods: {
        handleRemove(file, fileList) {
            console.log(file, fileList,'file, fileList');


        },
        handleClose1(){
            this.tableData = []
            this.showErr = false
            this.fileName = ''
            this.fileList = []

        },
        handleClose() {
            console.log(this.fileList,'this.fileList');
            this.fileName = ''
            this.fileList = []
            this.$emit('showInfo', false)
            console.log(this.fileList,'this.fileList');

        },

        handleChange(file, fileList) {
            this.fileName = file.name
        },
        downFile() {
            window.open('https://yzims.oss-cn-shenzhen.aliyuncs.com/import_student.xlsx')
        },

        submitUpload() {
            if(this.fileName){
            this.loading = true
            this.showErr = true
            this.uploadFileParams.mpMsgId = this.id
            this.$refs.upload.submit();
            this.$emit('showInfo', false)
            }else{
                this.$message({ type: 'error', message: '请先选择上传文件' });
            }

        },
        uploadSuccess(response, file, fileList) {
            if (response.ok) {
                this.loading = false
                this.succeedCount = response.body.succeedCount
                this.failedCount = response.body.failedCount
                this.total = response.body.total
                this.tableData = response.body.failedList
                if(this.succeedCount){
                    this.$parent.getTaskImportList()
                    this.$parent.getSelectedCount()
                }
            } else {
                this.loading = false
                console.log(response, 'response, file, fileList');
                this.succeedCount = 0
                this.failedCount = 0
                this.total = 0
                this.tableData = []
                this.$message({ type: 'error', message: response.code });
            }
            console.log(this.succeedCount, this.failedCount, this.total, this.tableData);


        }
    }
}
</script>

<style></style>