<template>
  <div>
    <el-dialog
      title="设置分支负责人"
      :visible.sync="dialogVisible"
      width="65%"
      :before-close="handleClose"
      center
      top="6vh"
    >
      <div class="dialog-content">
        <!-- 表单 -->
        <el-form
          ref="searchForm"
          label-width="100px"
          class="yz-search-form"
          :model="form"
          @submit.native.prevent="search"
        >
          <el-form-item label="员工姓名:" prop="stdName">
            <el-input v-model="form.realName" placeholder="请输入员工姓名" />
          </el-form-item>
          <el-form-item label="校区:">
            <el-select v-model="form.campusName" placeholder="请选择校区" @change="schoolName">
              <el-option v-for="item in schoolInfo" :key="item.id" :label="item.campusName" :value="item.campusId" />
            </el-select>
          </el-form-item>
          <el-form-item label="部门:">
            <el-select v-model="form.dpName" placeholder="请选择部门">
              <el-option v-for="item in departmentInfo" :key="item.dpId" :label="item.dpName" :value="item.dpId" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否在职:">
            <el-select v-model="form.isStaff" placeholder="请选择是否在职">
              <el-option label="是" value="1" />
              <el-option label="否" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否负责人:">
            <el-select v-model="form.isResponsible" placeholder="请选择是否负责人">
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
          <div class="search-reset-box">
            <el-button type="primary" icon="el-icon-search" native-type="submit">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="search(0)" />
          </div>
        </el-form>
        <div style="float:right;margin-bottom:10px">
          <el-button type="primary" @click="addPerson()">添加负责人</el-button>
        </div>
        <!-- 表格 -->
        <el-table ref="multipleTable" v-loading="loading" max-height="450" :data="tableData" style="width: 100%; margin: 25px 0" border @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="realName" label="员工姓名" width="180" align="center" />
          <el-table-column prop="campusName" label="所在校区" width="180" align="center" />
          <el-table-column prop="dpName" label="所在部门" align="center" />
          <el-table-column prop="isStaff" label="是否在职" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isStaff ==1">
                <span style="color:#00B46D">是</span>
              </span>
              <span v-else>
                <span style="color:#D75C89">否</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="isResponsible" label="是否负责人" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isResponsible ==0">
                <span style="color:#D75C89">
                  <i class="el-icon-close"></i>
                </span>
              </span>
              <span v-else>
                <span style="color:#00B46D">
                  <i class="el-icon-check"></i>
                </span>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="pageClass">
          <el-pagination
            :current-page.sync="page.currentPage"
            :page-size="page.pageSize"
            :total="page.total"
            :page-sizes="[10, 20, 30, 40]"
            layout="total, prev, pager, next, sizes,jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    addBranchs: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {},
      currentPage3: 1,
      tableData: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 1
      },
      multipleSelection: [],
      schoolInfo: [],
      departmentInfo: [],
      baranchsInfo: {},
      loading: true
    };
  },
  watch: {
    addBranchs(newVal, old) {
      this.baranchsInfo = newVal;
      if (newVal) {
        this.problemInfoList(newVal);
      }
    }
  },
  created() {
    this.schoolList();
  },
  methods: {
    // 列表数据
    problemInfoList() {
      this.loading = true;
      const params = {
        // branchId: this.addBranchs.id,
        responsibleBranch: this.addBranchs.id,
        realName: this.form.realName,
        campusId: this.form.campusName,
        dpId: this.form.dpName,
        isStaff: this.form.isStaff,
        isResponsible: this.form.isResponsible,
        start: this.page.currentPage,
        length: this.page.pageSize
      };
      this.$http.get('/user/selectAllEmployeeUser.do', { params }).then(res => {
        if (res.ok) {
          this.tableData = res.body.page.data;
          this.page.total = res.body.page.recordsFiltered;
          this.loading = false;
        }
      });
    },
    // 关闭
    handleClose() {
      this.$emit('addChargeDialogVisible', false);
    },
    // 查询
    search(val) {
      if (!val) {
        this.form = {};
      }
      this.problemInfoList();
    },
    // 分页
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.page.pageSize = val;
      this.problemInfoList();
    },
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.problemInfoList();

      console.log(`当前页: ${val}`);
    },
    // 列表选中状态
    handleSelectionChange(rows) {
      this.multipleSelection = rows;
    },
    // 添加负责人
    addPerson() {
      if (this.multipleSelection.length <= 0) {
        this.$message.error('请添加负责人！');
        return false;
      }
      let uid = '';
      this.multipleSelection.forEach(item => {
        console.log(item, 'item');
        uid += item.userId + ',';
      });
      const params = {
        branchId: this.addBranchs.id,
        userId: uid
      };
      this.$http.post('/question/responsible/addBatchQuestionResponsible.do', params, { json: true }).then(res => {
        if (res.ok) {
          this.problemInfoList();
          this.$parent.problemInfoList(this.addBranchs.id);
          this.$message.success('添加成功');
        } else {
          this.$message.error('添加失败');
        }
      });
    },
    // 下拉框校区数据
    schoolList() {
      const params = {};
      this.$http.post('/campus/selectAllList.do', params).then(res => {
        if (res.ok) {
          this.schoolInfo = res.body;
        }
      });
    },
    // 选择状态调用部门接口
    schoolName(value) {
      this.departmentList(value);
    },
    // 部门列表接口
    departmentList(value) {
      const params = {
        // campusName: this.form.campusName
        campusId: value
      };
      this.$http.post('/dep/selectAllList.do', params).then(res => {
        if (res.ok) {
          this.departmentInfo = res.body;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.pageClass{
  float: right;
  margin-top: -15px;
}
.el-dialog__body {
  padding-bottom: 50px;
}
.dialog-content{
  // max-height: 70vh;
  // overflow-x: hidden;
}
</style>
