<template>

  <div>
    <common-dialog
      class="common-dialog"
      width="1000px"
      title="完成详情"
      :visible.sync="show"
      :remindId='remindId'
      @open="open"
      @close="close"
    >
      <div class="dialog-main"></div>
      <!-- 表单 -->
      <el-form
        ref="searchForm"
        size="mini"
        label-width="120px"
        class="yz-search-form"
        :model="form"
        @submit.native.prevent="search"
      >
        <el-form-item v-if="type === 1 || type === 0" label="校区" prop="campusId">
          <el-select v-model="form.campusId" clearable placeholder="请选择" filterable>
            <el-option
              v-for="item in campusList"
              :key="item.campusId"
              :label="item.campusName"
              :value="item.campusId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门" prop="dpId">
          <el-select v-model="form.dpId" clearable placeholder="请选择" filterable>
            <el-option
              v-for="item in depList"
              :key="item.dpId"
              :label="item.dpName"
              :value="item.dpId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门负责人名称" prop="dpManagerName">
          <el-input v-model="form.dpManagerName" placeholder="请输入" />
        </el-form-item>
        <el-form-item v-if="type === 2" label="辅导员姓名" prop="empName">
          <el-input v-model="form.empName" placeholder="请输入" />
        </el-form-item>
        <el-form-item v-if="type === 3" label="协作人姓名" prop="empName">
          <el-input v-model="form.empName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="是否完成" prop="isAllFinish">
          <el-select v-model="form.isAllFinish" clearable placeholder="请选择" filterable>
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <div class="search-reset-box " style="margin-right: 10px">
          <el-button
            type="primary"
            icon="el-icon-search"
            native-type="submit"
            size="mini"
          >搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
        </div>
      </el-form>
      <!-- 按钮区 -->
      <div v-if="isSelf" class="yz-table-btnbox" style="margin-right: 10px;">
        <el-button
          icon="el-icon-upload2"
          type="warning"
          size="small"
          @click="exportBtn('exportStuInfo')"
        >Excel导出学员详情</el-button>
        <el-button
          icon="el-icon-upload2"
          type="warning"
          size="small"
          @click="exportBtn('exportPlanInfo')"
        >Excel导出完成情况</el-button>
        <el-button
          v-if="type === 1 || type === 0"
          icon="el-icon-s-custom"
          type="success"
          size="small"
          @click="remindPersons"
        >提醒负责人</el-button>
        <el-button
          v-else
          icon="el-icon-s-custom"
          type="success"
          size="small"
          @click="remindPersonsOther"
        >{{ type === 2 ? '提醒辅导员' : '提醒协作人' }}</el-button>
      </div>
      <div v-else class="yz-table-btnbox" style="margin-right: 10px; margin-bottom: 10px;"></div>
      <!-- 表格 -->
      <el-table
        ref="table"
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%;"
        header-cell-class-name="table-cell-header"
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          prop="stdName"
          align="center"
          type="selection"
          width="50"
        />
        <el-table-column v-if="type === 2" prop="empName" label="辅导员姓名" align="center" />
        <el-table-column v-if="type === 3" prop="empName" label="协作人姓名" align="center" />
        <el-table-column v-if="type === 1 || type === 0" prop="campusName" label="校区" align="center" />
        <el-table-column prop="dpName" label="部门" align="center" />
        <el-table-column prop="dpManagerEmpName" label="部门负责人" align="center" />
        <el-table-column v-if="type === 2 || type === 3" key="isFinish" prop="isFinish" label="是否完成" align="center">
          <template v-slot="scope">{{ scope.row.isFinish ? '已完成' : '未完成' }}</template>
        </el-table-column>
        <el-table-column
          v-if="type === 1 || type === 0"
          key="finishStatus"
          label="完成情况"
          width="220"
          align="center"
        >
          <template v-slot="scope">
            <div>
              <p>总数: {{ scope.row.finishCount + scope.row.notFinishCount }}</p>
              <p>已完成: {{ scope.row.finishCount }}</p>
              <p>未完成: {{ scope.row.notFinishCount }}</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="type === 1 || type === 0" key="completionRate" prop="completionRate" label="完成率" align="center" />
        <el-table-column align="center" label="操作">
          <template v-slot="scope">
            <el-button
              v-if="type === 1 || type === 0"
              :disabled="!isSelf"
              size="small"
              type="primary"
              @click="remindPersons(scope.row.dpId)"
            >提醒负责人</el-button>
            <el-button
              v-else
              :disabled="!isSelf"
              size="small"
              type="primary"
              @click="remindPersonsOther(scope.row.empId)"
            >{{ type === 2 ? '提醒辅导员' : '提醒协作人' }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination" style="margin-bottom: 10px">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getFinishList"
        />

      </div>
    </common-dialog>
    <confirm-dialog :visible.sync="exportShow" :remindId="remindId" :tipsTitle="tipsTitle" @exportExcel="exportExcel" />
  </div>
</template>

<script>
const form = {
  campusId: '', // 校区Id
  dpId: '', // 部门Id
  dpManagerName: '', // 部门负责人
  sysRemindType: '', // 类型（1：通知 2：任务）
  isAllFinish: '', // 是否全部完成（0：否 1：是)
  remindId: '', // 消息Id
  empType: '',
  empName: ''
};
import { exportExcel } from '@/utils';
import confirmDialog from '../indexDialog/confirmDialog.vue';

export default {
  components: {
    confirmDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    remindId: {
      type: Number,
      default: 0
    },
    isSelf: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      tipsTitle: '',
      exportShow: false,
      show: false,
      form: form,
      tableLoading: false,
      tableData: [],
      campusList: [],
      depList: [],
      dpIds: '', // 部门ids
      empIds: '', // 负责人
      exprotUrl: '',
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    'form.campusId'(val) {
      this.form.dpId = '';
      this.getDepList();
    }
  },
  methods: {
    open() {
      this.form.remindId = this.remindId;
      this.form.empType = this.type;
      if (this.type === 1 || this.type === 0) {
        this.getCampusList();
      } else {
        this.getDepList();
      }
      this.getFinishList();
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
      this.$refs['searchForm'].resetFields();
    },
    handleQUeryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    getCampusList() {
      this.$post('getCampusList').then((res) => {
        if (res.code === '00') {
          this.campusList = res.body;
        }
      });
    },
    getDepList() {
      this.$post('getDepList', { campusId: this.form.campusId }).then((res) => {
        if (res.code === '00') {
          this.depList = res.body;
        }
      });
    },
    getFinishList() {
      this.tableLoading = true;
      const params = this.handleQUeryParams();

      if (this.type === 1 || this.type === 0) {
        this.$post('planFinishList', params).then((res) => {
          const { fail, body } = res;
          if (!fail) {
            this.tableLoading = false;
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
            this.tableData.forEach(item => {
              // 计算完成率
              item.completionRate = (item.finishCount / (item.finishCount + item.notFinishCount) * 100).toString();
              if (item.completionRate.indexOf('.') !== -1) {
                item.completionRate = item.completionRate.substring(0, item.completionRate.indexOf('.')) + '%';
              } else {
                item.completionRate = item.completionRate + '%';
              }
            });
          }
        });
      } else {
        this.$post('empFinishList', params).then((res) => {
          const { fail, body } = res;
          if (!fail) {
            this.tableLoading = false;
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
          }
        });
      }
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getFinishList();
      }
    },
    exportBtn(exprotUrl) {
      this.exprotUrl = exprotUrl;
      this.tipsTitle = '确认要执行导出Excel操作吗？该操作需要等待一定时间';
      this.exportShow = true;
    },
    exportExcel() {
      this.$message({
        message: '正在导出，请稍后！',
        type: 'success'
      });
      exportExcel(this.exprotUrl, this.form);
    },
    handleSelectionChange(val) {
      const rowData = val;
      const personsList = [];
      if (this.type === 1 || this.type === 0) {
        rowData.forEach((item) => {
          personsList.push(item.dpId);
        });
        this.dpIds = '';
        for (let i = 0; i < personsList.length; i++) {
          if (i === 0) {
            this.dpIds = personsList[i].toString();
          } else {
            this.dpIds = this.dpIds + ',' + personsList[i].toString();
          }
        }
      } else {
        rowData.forEach((item) => {
          personsList.push(item.empId);
        });
        this.empIds = '';
        this.empIds = personsList.join(',');
      }
    },
    remindPersons(dpId) {
      const datas = {
        dpIds: this.dpIds.toString(),
        remindId: this.remindId
      };
      // 单人提醒
      if (dpId.length) {
        datas.dpIds = dpId;
      } else {
        // 多人提醒
        if (!this.dpIds) {
          this.$message({
            message: '请选择需要提醒的负责人',
            type: 'warning'
          });
          return;
        }
      }
      this.$post('remindManagerPerson', datas).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.$message({
            message: '提醒成功',
            type: 'success'
          });
          this.getFinishList();
        }
      });
    },
    remindPersonsOther(empId) {
      const datas = {
        empIds: this.empIds.toString(),
        remindId: this.remindId
      };
      // 单人提醒
      if (empId.length) {
        datas.empIds = empId;
      } else {
        // 多人提醒
        if (!this.empIds) {
          this.$message({
            message: `请选择需要提醒的${this.type === 2 ? '辅导员' : '协作人'}`,
            type: 'warning'
          });
          return;
        }
      }
      this.$post('remindEmployeePerson', datas).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.$message({
            message: '提醒成功',
            type: 'success'
          });
          this.getFinishList();
        }
      });
    }

  }
};
</script>
