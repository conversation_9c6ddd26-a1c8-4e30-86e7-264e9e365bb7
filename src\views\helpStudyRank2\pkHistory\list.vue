<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='活动名称' prop='pkName'>
        <el-input v-model="form.pkName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='活动起止时间' prop='activityTime'>
        <el-date-picker
          v-model="form.activityTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column label="活动编码" prop="pkNumber" align="center" />
      <el-table-column label="活动名称" prop="pkName" align="center" />
      <el-table-column
        prop="activityTime"
        label="活动起止时间"
        align="center"
        width="265px"
      >
        <template v-slot="scope">
          <span>{{ scope.row.startTime | transformTimeStamp }} 至 {{ scope.row.endTime | transformTimeStamp }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="listChildActivity"
        label="PK内容"
        width="250px"
      >
        <template v-slot="scope">
          <p v-for="(item,index) in scope.row.listChildActivity" :key="index">
            <el-link class="font-12" type="primary" @click.prevent="openReport(scope.row, item)">{{ item.pkChildName }}</el-link>
          </p>
        </template>
      </el-table-column>
      <el-table-column width="100px" label="活动文件" align="center">
        <template v-slot="scope">
          <div v-if="scope.row.pkAttr">
            <el-button type="text" size="mini" @click.prevent="previewClick(scope.row.pkAttr)">预览</el-button>
            <el-button type="text" size="mini" @click.prevent="actDownload(scope.row.pkAttr)">下载</el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="活动状态" align="center">
        <template v-slot="scope">
          {{ scope.row.status | status }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

  </div>
</template>

<script>
import { ossUri } from '@/config/request';
import downFile from '@/utils/downFile';
export default {
  filters: {
    status(value) {
      if (!value) return;
      const data = {
        '1': '全部',
        '2': '未开始',
        '3': '进行中',
        '4': '已结束'
      };
      return data[value];
    }
  },
  data() {
    return {
      form: {
        pkName: '',
        activityTime: []

      },
      tableLoading: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    openReport(row, current) {
      const { pkActId, pkChildId, pkChildType, pkChildName } = current;
      const pageUrl = '/school-paper/index.html#/helpStudyRank/pkHistory/report';
      const queryStr = `?pkActId=${pkActId}&pkChildId=${pkChildId}&pkChildType=${pkChildType}&title=${pkChildName}&only=1`;
      window.open(pageUrl + queryStr);
    },
    previewClick(file) {
      window.open(ossUri + file);
    },
    actDownload(file) {
      downFile(ossUri + file, file);
    },
    handleQueryParams() {
      const query = JSON.parse(JSON.stringify(this.form));
      query.startTimeStr = '';
      query.endTimeStr = '';

      if (Array.isArray(query.activityTime)) {
        query.startTimeStr = query.activityTime[0];
        query.endTimeStr = query.activityTime[1];
      }

      delete query.activityTime;
      return query;
    },
    getTableList() {
      this.tableLoading = true;
      const query = this.handleQueryParams();
      const params = {
        ...query,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('pkActivityHistoryList', params).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>

<style scoped>
.yz-search-form {
  margin-bottom: 20px;
}
</style>
