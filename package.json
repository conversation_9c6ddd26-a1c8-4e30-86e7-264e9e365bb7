{"name": "postgraduate", "version": "1.0.0", "description": "", "author": "cen", "scripts": {"dev": "vue-cli-service serve", "dev:pre": "vue-cli-service serve --mode pre", "start": "npm run dev", "build:prod": "vue-cli-service build", "build:dev": "vue-cli-service build --mode development", "build:test": "vue-cli-service build --mode testing", "build:pre": "vue-cli-service build --mode pre", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --fix --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@cell-x/el-table-sticky": "^1.0.6", "axios": "0.18.1", "core-js": "^3.29.1", "driver.js": "^0.9.8", "element-ui": "^2.15.6", "file-saver": "^2.0.5", "js-base64": "^3.7.7", "js-cookie": "2.2.0", "jspdf": "^2.5.1", "jszip": "^3.6.0", "moment": "^2.27.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "sortablejs": "^1.15.2", "tiny-cookie": "^2.3.2", "vue": "2.6.10", "vue-router": "3.0.6", "vuex": "3.1.0", "wangeditor": "^4.7.4", "xgplayer": "^2.24.3", "xlsx": "^0.18.5", "xss": "^1.0.9"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT", "volta": {"node": "14.9.0", "yarn": "1.22.19"}}