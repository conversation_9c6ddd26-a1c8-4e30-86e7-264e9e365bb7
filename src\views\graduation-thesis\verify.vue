<template>
  <div class="container">
    <div class="main">
      <div class="left" :style="leftStyle">
        <iframe
          :style="leftStyle"
          class="left"
          :src="wordUrl"
        ></iframe>
      </div>
      <div class="right">
        <div class="susp" @click="openSidebar">
          <div><i :class="rightShow ? 'el-icon-caret-left': 'el-icon-caret-right'"></i></div>
        </div>
        <div v-show="rightShow" class="form">
          <div class="form-item">
            <div class="label">教师上传论文附件</div>
            <div class="value">
              <el-row type="flex" class="mt18">
                <el-col :span="12" class="fs14">
                  <span class="gray">附件：{{ filaName }}</span>
                </el-col>
                <el-col :span="12" class="text-right">
                  <el-upload
                    ref="upload"
                    class="inline-block"
                    list-type="text"
                    :action="action"
                    :show-file-list="false"
                    :max-limit="1"
                    :file-list="fileList"
                    name="teacherAttachmentUrtl"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                  >
                    <el-tooltip effect="dark" content="上传" placement="top">
                      <i class="yz-icon-upload"></i>
                    </el-tooltip>
                  </el-upload>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="form-item">
            <div class="flex">
              <div class="label">审核结果</div>
              <div>
                <el-select
                  v-model="checkStatus"
                  v-width="100"
                  size="small"
                  placeholder="请选择"
                  @change="handleChange"
                >
                  <el-option label="通过审核" value="1" />
                  <el-option label="不通过审核" value="2" />
                </el-select>
              </div>
            </div>
          </div>
          <div class="form-item">
            <div class="flex">
              <div class="label">点评内容</div>
              <div>
                <el-button
                  type="primary"
                  size="small"
                  plain
                  @click="editPapaer"
                >在线修改学生论文</el-button>
              </div>
            </div>
            <div class="comment">
              <wang-editor
                ref="editor"
                v-model="commentContent"
                :height="428"
              />
            </div>
          </div>
          <div class="bottom">
            <el-button
              v-preventReClick="2000"
              size="small"
              type="primary"
              plain
              @click="submit"
            >提交审核</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ossUri } from '@/config/request';

export default {
  data() {
    return {
      uploadFileUrl: '/graduatePaper/webuploaderNew.do',
      commentContent: '',
      checkStatus: '1',
      fileList: [],
      wordUrl: '', // 毕业论文文件地址
      action: '',
      office: 'https://view.officeapps.live.com/op/view.aspx?src=',
      fileInfo: {},
      passContent: '',
      faileContent: '',
      leftStyle: {
        width: '768px'
      },
      rightShow: true,
      studentId: null,
      learnId: null,
      attachmentId: null,
      filaName: ''
    };
  },
  mounted() {
    this.learnId = this.$route.query.learnId;
    this.attachmentId = this.$route.query.attachmentId;
    this.studentId = this.$route.query.studentId;
    this.getLatestFile();
    this.getFileInfo();
  },
  methods: {
    // 获取最后一份的修改论文附件id和路径
    getLatestFile() {
      const params = {
        attachmentId: this.attachmentId
      };
      this.$post('getAttachmentById', params)
        .then(res => {
          const { code, body } = res;
          if (code === '00') {
            this.wordUrl = this.office + ossUri + body.attachmentUrl + '?v=' + new Date().getTime();
            // this.fileInfo = body;
            // console.log(body);
          }
        });
    },
    openSidebar() {
      this.rightShow = !this.rightShow;
      if (this.rightShow) {
        this.leftStyle.width = '768px';
      } else {
        this.leftStyle.width = '1200px';
      }
    },
    handleChange(value) {
      if (value === '1') {
        this.$refs['editor'].setContent(this.passContent);
      } else {
        this.$refs['editor'].setContent(this.faileContent);
      }
    },
    getFileInfo() {
      const params = {
        attachmentId: this.attachmentId,
        checkStatus: this.checkStatus
      };
      this.$post('paperAttachmentNew', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            // this.wordUrl = this.office + ossUri + body.attachments.attachmentUrl + '?v=' + new Date().getTime();
            this.fileInfo = body.attachments;
            body.commentContent.forEach(item => {
              if (item.attrName === 'pass') {
                this.passContent = item.attrValue;
              }
              if (item.attrName === 'faile') {
                this.faileContent = item.attrValue;
              }
            });
            this.$refs['editor'].setContent(this.passContent);
          }
        });
    },
    editPapaer() {
      this.$router.push({
        path: '/editor',
        query: {
          taskId: this.fileInfo.taskId,
          learnId: this.learnId,
          attachmentId: this.studentId,
          paperUploadType: '9',
          project: 'bms',
          unvsCode: this.$route.query.unvsCode
        }
      });
    },
    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        this.$nextTick(() => {
          const url = '/graduatePaper/webuploaderNew.do?learnId=' + this.learnId + '&attachmentId=' + this.attachmentId;
          this.action = url;
          resolve();
        });
      });
    },
    uploadSuccess(response, file, fileList) {
      this.filaName = file.raw.name;
      this.$message({
        message: '附件上传成功',
        type: 'success'
      });
      this.$refs.upload.clearFiles();
    },
    submit() {
      const content = this.$refs['editor'].getContent('text').replace(/&nbsp;/ig, '');
      if (content.trim().length > 10000) {
        this.$message.error('点评内容不能超过10000万字');
        return;
      }

      if (content.trim() !== '') {
        const params = {
          attachmentName: this.fileInfo.attachmentName,
          attachmentId: this.studentId,
          learnId: this.learnId,
          checkStatus: this.checkStatus,
          commentContent: this.commentContent
        };
        this.$post('updateAttachment', params)
          .then(res => {
            const { fail, body } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });

              setTimeout(() => {
                window.close();
              }, 3000);
            }
          });
      } else {
        this.$message.error('请填写点评内容');
      }
    }
  }
};
</script>

<style lang='scss' scoped>
.main {
  margin: 0 auto;
  min-width: 1200px;
  max-width: 1200px;
  padding-top: 52px;
  overflow: hidden;

  .text-right {
    text-align: right;
  }

  .inline-block {
    display: inline-block;
  }

  .mt18 {
    margin-top: 18px;
  }

  .fs14 {
    font-size: 14px;
    align-items: center;
  }

  .gray {
    color: #C0C4CC;
  }

  .comment {
    padding-top: 15px;
  }

  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .left {
    width: 768px;
    min-height: 90vh;
    display: inline-block;
    vertical-align: top;
    border: 1px solid #c9d8db;

    iframe {
      border: none;
    }
  }

  .right {
    width: 425px;
    height: 90vh;
    margin-left: 5px;
    display: inline-block;
    background: #ffffff;
    position: fixed;

    .form {
      width: 408px;
      float: right;
      border: 1px solid #c9d8db;
      border-left: none;
      padding-top: 10px;
    }

    .form-item {
      border-bottom: 1px solid #D8D8D8;
      padding: 12px;
      .label {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .bottom {
      padding: 16px 0;
      text-align: center;
    }

    .susp {
      width: 17px;
      height: 90vh;
      display: inline-block;
      position: relative;
      border-right: 1px solid #c9d8db;

      div {
        width: 17px;
        height: 102px;
        line-height: 102px;
        text-align: center;
        background: url("../../assets/imgs/susp.png") no-repeat;
        background-size: 100% 100%;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%) rotate(180deg);
        cursor: pointer;
        user-select: none;
        &:hover {
          background: url("../../assets/imgs/susp_h.png") no-repeat;
          background-size: 100% 100%;
          color: #fff;
        }
      }
    }

  }

}
</style>
