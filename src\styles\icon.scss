

@mixin icon($name) {
  $class: 'yz-icon-' + $name;
  $url: '../assets/imgs/icon/icon-' + $name + '.png';

  .#{$class} {
    width: 24px;
    height: 24px;
    display: inline-block;
    background: url($url) no-repeat;
    background-size: 100% 100%;
  }
}

@include icon('look');
@include icon('down');
@include icon('upload');
@include icon('plus');
@include icon('child');
@include icon('edit');
@include icon('minus');
@include icon('preview');
@include icon('computer');
@include icon('tip');
@include icon('right1');
@include icon('setting');

