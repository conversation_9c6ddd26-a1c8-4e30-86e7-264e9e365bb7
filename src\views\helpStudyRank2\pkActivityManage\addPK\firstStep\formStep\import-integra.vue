<template>
  <common-dialog width="800px" :show-footer="false" title="excel导入积分" :visible.sync='visible' @open="init" @close="close">
    <el-form ref='searchForm' class="dialogs-main" size='mini' :model='form' label-width='120px'>
      <el-form-item label='模板：'>
        <a :href="templateUrl">
          <el-button type="primary" plain>下载模板</el-button>
        </a>
        <div class="el-upload__tip">
          <p>说明：</p>
          <p>关键必填列：商品ID、商品名称，渠道，积分</p>
        </div>
      </el-form-item>
      <el-form-item label='导入：'>
        <el-upload
          ref="upload"
          class="upload-demo"
          action=""
          accept=".xlsx"
          multiple
          :auto-upload="false"
          :file-list="fileList"
          :on-change="uploadData"
          :show-file-list="false"
        >
          <div class="upload-dis">
            <span><i class="el-icon-document"></i> 浏览文件</span>
            <p>{{ path }}</p>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<script>
import { downUri } from '@/config/request';

export default {
  props: {
    visible: { type: Boolean, default: false },
    name: { type: String, default: '' }
  },
  data() {
    return {
      path: '',
      form: {},
      fileList: [],
      templateUrl: downUri + '/excel/pkScoreTemplate.xlsx'
    };
  },
  methods: {
    init() {
      this.path = this.name || '';
    },
    uploadData(file, fileList) {
      const that = this;
      this.fileList = [fileList[fileList.length - 1]];
      this.path = file.name || '';
      const reader = new FileReader();
      reader.readAsArrayBuffer(file.raw);
      reader.onload = function() {
        const buffer = reader.result;
        const bytes = new Uint8Array(buffer);
        const length = bytes.byteLength;
        let binary = '';
        for (let i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i]);
        }
        const XLSX = require('xlsx');
        const wb = XLSX.read(binary, { type: 'binary' });
        const data = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
        that.$emit('close', { data, name: that.path });
      };
    },
    close() {
      if (this.visible) this.$emit('close');
    }
  }
};
</script>

<style lang='scss' >
.dialogs-main {
  margin: 20px;
  .el-upload {
    width: 100%;
  }
  .el-upload__tip p {
    color: #ff4040;
  }
 .upload-dis {
    display: flex;
    align-items: center;
    span {
      padding: 2px 8px;
      color: #ffffff;
      background-color: #409EFF;
    }
    p {
      width: 40%;
      height: 30px;
      color: #8a8a8a;
      border: 1px solid #cdcddd;
    }
  }
}
</style>
