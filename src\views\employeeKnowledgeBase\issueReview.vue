<template>
  <div class="yz-base-container">
    <el-row>
      <el-col :span="24">
        <el-form ref="form" :model="form" label-width="110px" class="yz-search-form">
          <el-form-item label="问题状态：">
            <el-select v-model="form.questionStatus" placeholder="请选择问题状态" clearable>
              <el-option label="有效" value="0" />
              <el-option label="永久有效" value="1" />
              <el-option label="失效" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建人：">
            <el-input v-model="form.createUser" placeholder="请输入创建人" clearable />
          </el-form-item>
          <el-form-item label="所属部门：">
            <el-select v-model="form.departmentName" filterable placeholder="请选择所属部门" clearable>
              <el-option v-for="item in departmentInfo" :key="item.dpId" :label="item.dpName" :value="item.dpId" />
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态：">
            <el-select v-model="form.auditStatus" placeholder="请选择审核状态" clearable>
              <el-option label="待审核" value="0" />
              <el-option label="通过" value="1" />
              <el-option label="不通过" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="问题：">
            <el-input v-model="form.question" placeholder="请输入问题" clearable />
          </el-form-item>
          <el-form-item label="搜索关键词：">
            <el-input v-model="form.searchKeyword" placeholder="请输入搜索关键词" clearable />
          </el-form-item>
          <div class="search-reset-box">
            <el-button type="primary" @click="query">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </el-form>
        <div style="float:right;margin:15px 25px 15px 0;">
          <el-button type="primary" @click="batchAudit()">批量审核</el-button>
        </div>
        <el-table ref="multipleTable" :data="tableData" style="width: 100%; margin: 25px 0" border @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="39" />
          <el-table-column prop="question" label="问题" align="center" width="200" />
          <el-table-column prop="answer" label="答案" align="center" width="200">
            <template slot-scope="scope">
              <span>{{ scope.row.answer.substring(0,10) }}<a @click="mode(scope.row)">更多</a></span>
            </template>
          </el-table-column>
          <el-table-column prop="searchKeyword" label="搜索关键字" align="center" />
          <el-table-column prop="createUser" label="创建人" align="center" width="80" />
          <el-table-column prop="departmentName" label="所属部门" align="center" />
          <el-table-column prop="updateTime" label="修改日期" align="center" :formatter="dateFormat" width="95" />
          <el-table-column prop="createTime" label="创建日期" align="center" :formatter="dateFormat" width="95" />
          <el-table-column prop="questionStatus" label="问题状态" align="center" width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.questionStatus ==0">
                <span>有效</span>
              </span>
              <span v-else-if="scope.row.questionStatus ==1">
                <span>永久有效</span>
              </span>
              <span v-else>
                <span>失效</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="auditStatus" label="审核状态" align="center" width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.auditStatus == 0">
                <span style="color:#666666">待审核</span>
              </span>
              <span v-else-if="scope.row.auditStatus == 1">
                <span style="color:#008000">通过</span>
              </span>
              <span v-else>
                <span style="color:#FF0000">不通过</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="审核说明" align="center" />
          <el-table-column prop="usableStartDate" label="可用时间" align="center" width="175">
            <template slot-scope="scope">
              <span>{{ scope.row.startDate+'~'+scope.row.endDate || '' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button v-if="scope.row.auditStatus == 0" type="primary" size="small" @click="examineBtn(scope.row)">审核</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pageClass">
          <el-pagination
            :current-page.sync="page.currentPage"
            :page-size="page.pageSize"
            :total="page.total"
            :page-sizes="[10, 20, 30, 40]"
            layout="total, prev, pager, next, sizes,jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
    </el-row>
    <examine :visible.sync="remindShow" :optionsType="optionsType" :infoList="infoList" @dialogVisible="remindShow=false" />
    <examineNotification :examineShow="examineShow" :examineInfoList="examineInfoList" @show="examineShow=false" />
  </div>
</template>

<script>
import moment from 'moment';
import examine from './examine';
import examineNotification from './examineNotification.vue';
export default {
  components: { examine, examineNotification },
  data() {
    return {
      tableData: [],
      currentPage3: 1,
      form: { auditStatus: '0' },
      remindShow: false,
      usableStartDate: '',
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 1
      },
      departmentInfo: [],
      optionsType: '',
      infoList: {},
      examineShow: false,
      examineInfoList: [],
      multipleSelection: []

    };
  },
  created() {
    this.loadList();
    this.departmentList();
  },
  methods: {
    query() {
      this.loadList();
    },
    reset() {
      this.form = {};
      this.loadList();
    },
    loadList() {
      const data = {
        questionStatus: this.form.questionStatus,
        createUser: this.form.createUser,
        departmentName: this.form.departmentName,
        auditStatus: this.form.auditStatus,
        question: this.form.question,
        searchKeyword: this.form.searchKeyword,
        start: this.page.currentPage,
        length: this.page.pageSize,
        type: 2
      };
      this.$http.post('/question/getAllQuestion.do', data, {
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(res => {
        if (res.ok) {
          res.body.data.forEach(item => {
            if (item.usableStartDate) {
              item.startDate = moment(item.usableStartDate).format('YYYY-MM-DD');
              item.endDate = moment(item.usableEndDate).format('YYYY-MM-DD');
            } else {
              item.startDate = '';
              item.endDate = '';
            }
            item.crumbs = JSON.parse(item.crumbs);
          });
          this.tableData = res.body.data;
          this.page.total = res.body.recordsFiltered;
        }
      });
    },
    batchAudit() {
      if (this.multipleSelection.length <= 0) {
        this.$message.error('请勾选审核问题！');
        return false;
      }
      // 选中的问题是否包含通过和拒绝
      const result = this.multipleSelection.some((item) => {
        return item.auditStatus === 1 || item.auditStatus === 2;
      });

      if (result) {
        this.$message.error('审核问题不能包含通过和拒绝状态!');
        return;
      }
      this.examineInfoList = this.multipleSelection;
      this.examineShow = true;
      // const id = [];
      // this.multipleSelection.forEach(item => {
      //   id.push(item.questionId);
      // });
      // const params = {
      //   questionIds: id,
      //   remark: this.form.remark,
      //   auditStatus: this.form.auditStatus
      // };
      // this.$http.post('/question/updateAuditBatchBdQuestion.do', params, { json: true }).then(res => {
      // });
    },
    // 列表选中状态
    handleSelectionChange(rows) {
      this.multipleSelection = rows;
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      console.log(`每页 ${val} 条`);
      this.loadList();
    },
    handleCurrentChange(val) {
      this.page.currentPage = val;
      console.log(`当前页: ${val}`);
      this.loadList();
    },
    handleEdit(index, row) {
      this.dialogVisible = true;
      console.log(index, row);
    },
    handleDelete(index, row) {
      console.log(index, row);
    },
    dateFormat(row, column) {
      var date = row[column.property];
      if (date === undefined) {
        return '';
      }
      return moment(date).format('YYYY-MM-DD');
    },
    // 部门列表接口
    departmentList(value) {
      const params = {
        // campusName: this.form.campusName
        campusId: value
      };
      this.$http.post('/dep/selectAllList.do', params).then(res => {
        if (res.ok) {
          this.departmentInfo = res.body;
        }
      });
    },
    mode(data) {
      this.infoList = data;
      this.optionsType = '问题详情';
      this.remindShow = true;
    },
    examineBtn(data) {
      const objNew = [];
      objNew.push(data);
      this.examineInfoList = objNew;
      this.examineShow = true;
    }
  }
};
</script>

<style lang = "scss" scoped>
.yz-base-container {
  padding: 30px 40px 20px;
}
  .custom-tree-node {
    display: flex;
    /* align-items: center; */
  }
  .pageClass{
  float: right;
  margin-top: -15px;
}
.iconStyle{
  display: flex;
  justify-content: space-evenly;
  font-size:25px;
  /* cursor:pointer; */
  /* text-align:center */
  div{
    cursor:pointer;
  }
}
</style>
