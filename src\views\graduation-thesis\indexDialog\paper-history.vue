<template>
  <common-dialog
    is-full
    width="1200px"
    title="历史提交记录"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="attrDesc" label="资料模板名称" align="center" width="200px" />
        <el-table-column
          prop="attachmentName"
          label="学员论文资料"
          width="250"
        >
          <template slot-scope="scope">
            <div class="space-between">
              <div v-width="180" class="left">
                <span class="gray">资料：</span>
                <span>{{ scope.row.attachmentName }}</span>
              </div>
              <div class="right">
                <el-tooltip effect="dark" content="下载" placement="top">
                  <a target="_Blank" :href="encodeURIComponent(scope.row.attachmentUrl) | downOssUrl">
                    <i class="yz-icon-down"></i>
                  </a>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="上传时间" align="center" width="150" />
        <el-table-column prop="checkStatus" label="学生论文审核状态" align="center" width="150">
          <template slot-scope="scope">
            <span :class="'file-status-' + scope.row.checkStatus">
              {{ scope.row.checkStatus | fileStatus }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="commentContent" label="点评内容" width="300" align="center">
          <template slot-scope="scope">
            <div v-html="scope.row.commentContent"></div>
          </template>
        </el-table-column>
        <el-table-column prop="commentTime" label="点评时间" align="center" />
        <el-table-column prop="pit" label="教师上传论文附件" width="250">
          <template slot-scope="scope">
            <div v-if="scope.row.teacherAttachmentUrtl" class="space-between">
              <div v-width="180" class="left">
                <span class="gray">附件：</span>
                <span>{{ scope.row.teacherAttachmentUrtl.attachmentName }}</span>
              </div>
              <div class="right">
                <el-tooltip effect="dark" content="下载" placement="top">
                  <a
                    target="_Blank"
                    :href="encodeURIComponent(scope.row.teacherAttachmentUrtl.attachmentUrl) | downOssUrl"
                    :download="scope.row.teacherAttachmentUrtl.attachmentName"
                  >
                    <i class="yz-icon-down"></i>
                  </a>
                </el-tooltip>
              </div>
            </div>
            <div v-else class="center">-</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="teachUploadTime"
          label="教师上传资料时间"
          align="center"
          width="150px"
        />
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  filters: {
    fileStatus(val) {
      if (!val) return;
      const data = {
        '0': '待审核',
        '1': '已通过',
        '2': '已驳回'
      };
      return data[val];
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {
      console.log(this.row.attrInfoList[this.row.currentFileIndex], 'ss');
      this.getTableList();
    },
    getTableList() {
      this.tableLoading = true;
      const params = {
        learnId: this.row.learnId,
        paperUploadType: this.row.attrInfoList[this.row.currentFileIndex].paperUploadType,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('lookPaperHistory', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableLoading = false;
            if (Array.isArray(body.data)) {
              body.data.forEach(item => {
                if (!item.teachUploadTime) {
                  item.teachUploadTime = '-';
                }
                item.attrDesc = this.row.attrInfoList[this.row.currentFileIndex].attrDesc;
                this.tableData = body.data;
                this.pagination.total = body.recordsTotal;
              });
            }
          }
        });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.gray {
  color: #C0C4CC;
}

.space-between {
  height: 40px;
  line-height: 40px;

  .left {
    float: left;
    width: 230px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .right {
    float: right;
    padding: 7px;
    box-sizing: border-box;
    height: 40px;
    text-align: right;
  }
}

.file-status-1 {
  color: #6EC543;
}

.file-status-2 {
  color: #F57373;
}
.center {
  width: 100%;
  text-align: center;
}
</style>
