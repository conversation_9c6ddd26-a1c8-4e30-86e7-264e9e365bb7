<template>
  <div class="yz-base-container">
    <el-form ref="formRef" size="medium" :model="formRef" :rules="basicRules" label-width="182px">
      <el-form-item label="项目标题：" prop="paymentItem">
        <el-input v-model="formRef.paymentItem" :disabled="payTypeDisabled" placeholder="请输入项目标题" maxlength="20" />
      </el-form-item>
      <el-form-item label="项目分类：" prop="itemType">
        <el-col :span="6">
          <el-select v-model="formRef.itemType" placeholder="请选择">
            <el-option label="缴费订单" :value="0" />
          </el-select>
        </el-col>
      </el-form-item>
      <el-form-item label="解锁模式：" prop="payMode">
        <el-col :span="6">
          <el-select v-model="formRef.payMode" :disabled="payTypeDisabled" placeholder="请选择">
            <el-option label="有锁模式" :value="2" />
            <el-option label="无锁模式" :value="1" />
          </el-select>
        </el-col>
      </el-form-item>
      <el-form-item label="项目详情：" prop="itemDetails">
        <wang-editor ref="itemDetailsRef" v-model="formRef.itemDetails" :height="600" />
      </el-form-item>
      <el-form-item v-if="unlockContentShow" label="解锁内容：" prop="unlockContent">
        <wang-editor ref="unlockContentRef" v-model="formRef.unlockContent" placeholder="以下内容为学员支付后可见" :height="600" />
      </el-form-item>
      <el-form-item label="项目价格：" prop="itemPrice">
        <el-input v-model="formRef.itemPrice" placeholder="请输入项目价格" maxlength="20" />
      </el-form-item>
      <el-form-item label="抵扣方式：" prop="paymentType">
        <el-checkbox-group v-model="formRef.paymentType">
          <el-checkbox label="智米" name="paymentType" />
          <el-checkbox label="滞留金" name="paymentType" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="是否启用：" prop="status">
        <el-radio v-model="formRef.status" :label="1">启用</el-radio>
        <el-radio v-model="formRef.status" :label="0">停用</el-radio>
      </el-form-item>
      <el-form-item>
        <div style="text-align: center;">
          <el-button type="primary" @click="submitForm('formRef')">{{ btnTitble }}</el-button>
          <!-- <el-button @click="resetForm('formRef')">取 消</el-button> -->
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>

export default {

  data() {
    return {
      formRef: {
        paymentItem: '',
        itemDetails: '',
        itemType: 0,
        itemPrice: '',
        status: 1,
        paymentType: [],
        payMode: 1,
        unlockContent: ''
      },
      basicRules: {
        paymentItem: [
          { required: true, message: '这是必填字段', trigger: 'blur' }
        ],
        itemType: [
          { required: true, message: '这是必填字段', trigger: 'blur' }
        ],
        payMode: [
          { required: true, message: '这是必填字段', trigger: 'blur' }
        ],
        itemPrice: [
          { required: true, message: '这是必填字段', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '这是必填字段', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    btnTitble() {
      const { exType } = this.$route.query;
      return exType === 'EDIT' ? '保 存' : '创 建';
    },
    unlockContentShow() {
      return this.formRef.payMode === 2;
    },
    payTypeDisabled() {
      const { exType } = this.$route.query;
      return exType === 'EDIT';
    }
  },
  mounted() {
    // exType：ADD（新增），EDIT（修改）
    const { exType, paymentId } = this.$route.query;
    if (exType === 'EDIT') {
      this.init(paymentId);
    }
  },
  methods: {
    init(paymentId) {
      const params = {
        paymentId: paymentId
      };
      this.$post('getEditPaymentList', params).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          const p1 = body.paymentType && body.paymentType.includes('1');
          const p2 = body.paymentType && body.paymentType.includes('2');
          let paymentType = [];
          if (p1 && p2) {
            paymentType = ['智米', '滞留金'];
          } else if (p1) {
            paymentType = ['智米'];
          } else if (p2) {
            paymentType = ['滞留金'];
          }
          this.formRef = {
            paymentId: body.paymentId,
            paymentItem: body.paymentItem,
            itemDetails: body.itemDetails,
            itemType: Number(body.itemType),
            itemPrice: body.itemPrice,
            status: Number(body.status),
            payMode: body.payMode,
            unlockContent: body.unlockContent,
            paymentType: paymentType
          };

          this.$nextTick(() => {
            this.$refs['itemDetailsRef'].setContent(body.itemDetails);
            body.payMode === 2 && this.$refs['unlockContentRef'].setContent(body.unlockContent);
          });
        }
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { exType } = this.$route.query;
          const { paymentType, unlockContent, payMode } = this.formRef;
          let paymentTypeStr = '';
          if (paymentType.length > 1) {
            paymentTypeStr = '1,2';
          } else if (paymentType.length === 1 && paymentType.includes('智米')) {
            paymentTypeStr = 1;
          } else if (paymentType.length === 1 && paymentType.includes('滞留金')) {
            paymentTypeStr = 2;
          }
          const params = {
            ...this.formRef,
            exType: exType,
            paymentType: paymentTypeStr,
            unlockContent: payMode === 2 ? unlockContent : ''
          };
          this.$post('addEditPaymentItem', params).then((res) => {
            const { fail, body } = res;
            if (!fail) {
              this.$message.success('操作成功');
              /* eslint-disable */
              setTimeout(() => {
                // 关闭父页面窗口
                layer_close();
              }, 1500);
            }
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.$refs['itemDetailsRef'].setContent('');
      this.$refs['unlockContentRef'].setContent('');
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-base-container {
  margin: 0 auto;
  padding-top: 33px;
  color: #000;
  // ::v-deep .el-form-item--mini.el-form-item{
  //   display: flex;
  //   justify-content:flex-start;
  //   align-items: center;
  // }

  // ::v-deep .el-form-item {
  //   .el-input__inner,.el-textarea__inner{
  //     width: 862px;
  //   }
  // }
}
</style>
