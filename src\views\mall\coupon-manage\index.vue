<template>
  <div class="yz-base-container">
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane label="优惠券管理" name="coupon">
        <coupon />
      </el-tab-pane>
      <el-tab-pane label="赠送优惠券" name="giveCoupon" lazy>
        <giveCoupon />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import coupon from './coupon';
import giveCoupon from './give-coupon';
export default {
  components: {
    coupon,
    giveCoupon
  },
  data() {
    return {
      activeName: 'coupon'
    };
  },
  methods: {
    handleUpdateList() {
      if (this.$refs.examine) {
        this.$refs.examine.getTableList();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
