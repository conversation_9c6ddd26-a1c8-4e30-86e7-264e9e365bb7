<template>
  <!-- 第二步：添加编辑表格 -->
  <div v-show="activeInx==2&&addType==1">
    <!-- 按钮区（第二步） -->
    <div class='yz-table-btnbox'>
      <span>个人PK人员名单：</span>
      <el-button class="yz-btns" type="success" size="small" @click="exportPersonPK">
        导出PK人员名单
      </el-button>
      <el-button type="primary" plain size="small" @click="staffVisible = true">
        添加助学老师
      </el-button>
      <el-button type="primary" size="small" @click="importData">导入PK人员名单</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column type="index" label="序号" align="center" />
      <el-table-column prop="dpEmpName" label="分校长" align="center" />
      <el-table-column prop="dpName" label="所属部门" align="center" />
      <el-table-column prop="empName" label="姓名" align="center" />
      <el-table-column prop="staffNo" label="员工号" align="center" />
      <el-table-column prop="jobTitle" label="职位" align="center" />
      <el-table-column prop="date" label="操作" align="center" width="150px">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <i class="el-icon-delete" @click="delectTeacher(scope.row)"></i>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 导入PK人员名单 -->
    <import-achieve-dialog type='1' :params="addParams" :title="reduceDialogTitle" :visible.sync="raVisible" @success="importSuccess" />
    <!-- 添加员工pk人员弹窗 -->
    <staff-dialog :visible.sync="staffVisible" :pk-child-id="pkChildId" @refresh="getTableList" />
  </div>
</template>

<script>
import importAchieveDialog from '../../import-achieve-dialog';
import staffDialog from '../staff-dialog';
import { httpPostDownFile } from '@/utils/downExcelFile';

export default {
  components: { importAchieveDialog, staffDialog },
  props: {
    activeInx: { type: Number, default: 0 },
    visible: { type: Boolean, default: false },
    addType: { type: String, default: '1' }, // 1:个人, 2:部门, 3:战队
    addParams: { type: Object, default: () => {} }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      raVisible: false,
      reduceDialogTitle: '',
      staffVisible: false,
      teamId: '',
      tableData: []
    };
  },
  inject: ['newRow'],
  computed: {
    row() {
      return this.newRow();
    },
    pkChildId() {
      const isEdit = this.row?.isEdit;
      return isEdit ? this.row?.pkChildId : this.addParams?.childActivityId;
    }
  },
  methods: {
    // 初始化
    secondInit() {
      this.getTableList();
    },
    // 获取个人列表数据
    getTableList() {
      this.tableLoading = true;
      const params = {
        pkChildId: this.pkChildId,
        start: 0,
        length: 100000
      };
      this.$post('personalList', params).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
        }
        this.tableLoading = false;
      });
    },
    // 导出个人PK名单
    exportPersonPK() {
      httpPostDownFile({
        url: '/pkTeam/exportPkUserList',
        params: { pkChildId: this.pkChildId },
        name: '导出PK人员名单',
        config: { json: false }
      });
    },
    // 导入PK人员名单
    importData() {
      this.raVisible = true;
      this.reduceDialogTitle = '导入PK人员名单';
    },
    importSuccess() {
      this.getTableList();
    },
    // 删除PK人员
    delectTeacher(row) {
      this.teamId = row.teamId;
      if (this.tableData.length === 1) {
        this.$message({ showClose: true, message: '最后一个PK人员不能删除哦!', type: 'warning' });
        return;
      }
      this.$confirm('您确定从该个人PK中移除该老师吗？移除后，该个人PK榜将不统计该老师的排名情况', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除个人
        this.$post('personDelete', { teamId: this.teamId }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({ type: 'success', message: '删除成功!' });
            this.secondInit();
          }
        });
      });
    },
    // 确认结果
    submitBtn() { },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
      this.$emit('showParent', false);
      this.$emit('refreshParent', true);
    }
  }
};
</script>

<style lang="scss" scoped>
.yz-table-btnbox{
    span {
      float: left;
    }
}
.header {
  text-align: center;
  margin-bottom: 20px;
  h1 {
    margin-bottom: 20px;
  }
}
.yz-btns a {
  color: #fff !important;
}
</style>

