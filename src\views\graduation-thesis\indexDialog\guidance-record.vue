<template>
  <el-dialog
    width="402px"
    top="30vh"
    :visible.sync='show'
    :tipsTitle='tipsTitle'
    :tipsContent='tipsContent'
    :destroy-on-close="true"
    :show-close="false"
    @close='close'
  >
    <div class="tips">
      <div class="img-box">
        <img class="dialog-img" src="../../../assets/imgs/warning.png" alt="" />
      </div>
      <div class="tips-title">{{ tipsTitle }}</div>
      <div class="tips-content">{{ tipsContent }}</div>
    </div>
    <div class="tips-confirm" @click="confirm()">确认</div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tipsTitle: {
      type: String,
      require: true,
      default: ''
    },
    tipsContent: {
      type: String,
      require: true,
      default: ''
    }
  },
  data() {
    return {
      show: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    confirm() {
      this.close();
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  display: flex;
  flex-direction: column;
  // margin: 0 !important;
  position: absolute;
  // top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  border-radius: 10px;
  .el-dialog__header {
    display: none;
  }
  .dj-dialog-content {
    padding: 0;
    overflow: unset;
  }
  .el-dialog__body {
    padding: 0;
    .tips{
       padding: 43px 78px 0 78px;
      .img-box {
        width: 100%;
        height: 36px;
        margin-bottom: 18px;
        align-items: center;
        text-align: center;
        .dialog-img {
          width: 36px;
          height: 36px;
        }
      }
      .tips-title {
        height: 25px;
        margin-bottom: 11px;
        font-size: 18px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #1A1B1D;
        line-height: 25px;
        text-align: center;
      }
      .tips-content {
        font-size: 14px;
        margin-bottom: 41px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
        text-align: center;
      }
    }
    .tips-confirm {
      width: 100%;
      height: 52px;
      text-align: center;
      font-size: 16px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FF6445;
      line-height: 52px;
      border-top:1px solid #f0eeee;
    }
  }
}
</style>
