<template>

  <el-table
    v-loading="tableLoading"
    v-sticky-scroller
    border
    size="small"
    style="width: 100%"
    header-cell-class-name='table-cell-header'
    :data="tableData"
  >
    <el-table-column label="序号" type="index" align="center" width="50" />
    <el-table-column prop="dpName" label="部门" align="center" />
    <el-table-column prop="dpEmpName" label="分校长" align="center" />
    <el-table-column prop="teamName" label="姓名" align="center" />
    <el-table-column prop="jobTitle" label="职位" align="center" />

    <template v-if="pkRange.includes('1')">
      <el-table-column prop="adultEduToday" label="今日成教" align="center" />
      <el-table-column prop="adultEduTotal" label="活动成教" align="center" />
    </template>
    <template v-if="pkRange.includes('2')">
      <el-table-column prop="nationalOpenToday" label="今日国开" align="center" />
      <el-table-column prop="nationalOpenTotal" label="活动国开" align="center" />
    </template>
    <template v-if="pkRange.includes('3')">
      <el-table-column prop="fullTimeToday" label="今日全日制" align="center" />
      <el-table-column prop="fullTimeTotal" label="活动全日制" align="center" />
    </template>
    <template v-if="pkRange.includes('4')">
      <el-table-column prop="selfStudyToday" label="今日自考" align="center" />
      <el-table-column prop="selfStudyTotal" label="活动自考" align="center" />
    </template>
    <template v-if="pkRange.includes('5')">
      <el-table-column prop="postgraduateToday" label="今日研究生" align="center" />
      <el-table-column prop="postgraduateTotal" label="活动研究生" align="center" />
    </template>
    <template v-if="pkRange.includes('6')">
      <el-table-column prop="vocationalEduToday" label="今日职业教育" align="center" />
      <el-table-column prop="vocationalEduTotal" label="活动职业教育" align="center" />
    </template>
    <template v-if="pkRange.includes('7')">
      <el-table-column prop="overseasEduToday" label="今日海外教育" align="center" />
      <el-table-column prop="overseasEduTotal" label="活动海外教育" align="center" />
    </template>
    <el-table-column prop="performanceDeduction" label="调整业绩" align="center" />
    <el-table-column prop="totalOrders" label="活动合计" align="center" />

    <el-table-column v-if="coefficientSwitch" prop="coefficient" label="个人招生系数" />

    <!-- 普通目标 普通目标可以存在多个-->
    <template v-if="targetType === 1 && targetConfigList.length">
      <el-table-column v-for="(item,index) in targetConfigList" :key="index" :label="item" align="center">
        <template slot-scope="scope">
          <span>{{ handleTargetText(scope.row, index) }}</span>
        </template>
      </el-table-column>
    </template>

    <!-- 助学积分、助学人数、活动人均 -->
    <el-table-column prop="totalScore" align="center">
      <template slot="header">
        {{ pkType | pkTypeToText }}
      </template>

      <template slot-scope="scope">
        <span>{{ scope.row.totalScore }}</span>
      </template>
    </el-table-column>

    <!-- 奖励目标 奖励目标只存在1个-->
    <template v-if="targetType === 2">
      <el-table-column label="达成奖励目标" align="center">
        <template>
          <span>{{ rewardTarget }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="achievementTime" label="达成奖励时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.achievementTime | transformTimeStamp }}</span>
        </template>
      </el-table-column>
    </template>
  </el-table>
</template>

<script>
import { StickyScroller } from '@cell-x/el-table-sticky';
export default {
  directives: {
    StickyScroller: new StickyScroller({ offsetBottom: '15px' }).init()
  },
  filters: {
    pkTypeToText(val) {
      const pkTypeEnum = {
        1: '助学积分',
        2: '助学人数',
        3: '活动人均'
      };
      return pkTypeEnum[val] || '/';
    }
  },
  props: {
    tableLoading: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: () => []
    },
    // 1：助学积分 2：助学人数 3：活动人均
    pkType: {
      type: Number,
      default: 0
    },
    // pk范围，1：成教 2：国开 3：全日制 4：自考 5：研究生 6：职业教育 7：海外教育
    pkRange: {
      type: Array,
      default: () => []
    },
    // 1: 普通目标, 2: 奖励目标
    targetType: {
      type: Number,
      default: 0
    },
    // 普通目标的配置
    targetConfigList: {
      type: Array,
      default: () => []
    },
    // 个人招生系数开关
    coefficientSwitch: {
      type: Boolean,
      default: false
    },
    // 达成奖励目标
    rewardTarget: {
      type: Number,
      default: 0
    }
  },
  methods: {
    // 处理普通目标的文本
    handleTargetText(row, index) {
      if (row.targetConfigList && Array.isArray(row.targetConfigList)) {
        const text = row.targetConfigList[index];
        return text !== 'null' ? text : '';
      }
    }
  }
};
</script>

