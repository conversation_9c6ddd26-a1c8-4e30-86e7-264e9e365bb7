
// 获取时间的秒数（参数：d(天)，h(小时),m(分钟),(秒)） 12m
const getSec = function(str) {
  const str1 = str.substr(0, str.length - 1); // 时间数值
  const str2 = str.substr(str.length - 1, 1); // 时间单位
  if (str2 === 's') {
    return str1 * 1000;
  } else if (str2 === 'm') {
    return str1 * 60 * 1000;
  } else if (str2 === 'h') {
    return str1 * 60 * 60 * 1000;
  } else if (str2 === 'd') {
    return str1 * 24 * 60 * 60 * 1000;
  }
};

const cookie = {
  getItem(key) {
    // 获取当前所有cookie
    const strCookies = document.cookie;
    // 截取变成cookie数组
    const array = strCookies.split(';');
    // 循环每个cookie
    for (let i = 0; i < array.length; i++) {
      // 将cookie截取成两部分
      const item = array[i].split('=');
      // 判断cookie的name 是否相等
      if (item[0] === key) {
        return item[1];
      }
    }
    return null;
  },
  setItem(key, value, time) {
    let cookitStr = key + '=' + value;
    if (time) {
      const sec = getSec(time);
      const date = new Date();
      date.setTime(date.getTime() + sec * 1);
      cookitStr += ';expires=' + date.toGMTString();
    }
    document.cookie = cookitStr;
  },
  // 删出 cookie
  removeItem(key) {
    const exp = new Date();
    exp.setTime(exp.getTime() - 1);
    // 获取cookie是否存在
    const value = cookie.getItem(key);
    if (value != null) {
      document.cookie = key + '=' + value + ';expires=' + exp.toUTCString();
    }
  }
};

export default cookie;
