<template>
    <el-dialog title="变更应缴" :visible.sync="showChangeMethodPop" width="50%" :before-close="handleClose" class="dialog">
        <div style="text-align: center;">学员：{{ changeInfo.stdName + ' - ' + changeInfo.grade + '级 - ' +
        changeInfo.unvsName + ' - ' + changeInfo.pfsnName + ' - ' + changeInfo.pfsnLevelName }}</div>
        <el-table ref="multipleTable" :data="tableData" style="width: 100%; margin: 25px 0" border
            :row-style="{ height: '1px' }">
            <el-table-column prop="idNumber" label="科目" align="center">
                <template slot-scope="scope">
                    {{ scope.row.itemCode + ':' + scope.row.itemName }}
                </template>
            </el-table-column>
            <el-table-column prop="payable" label="应缴" align="center"></el-table-column>
            <el-table-column prop="subOrderStatus" label="缴费状态" align="center">
                <template slot-scope="scope">
                    <div :style="scope.row.subOrderStatus == '2' ? {} : { color: 'red' }">{{ scope.row.subOrderStatus ==
        '1' ? '待缴费' : '已缴费' }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="phone" label="收取方式" align="center">
                <template slot-scope="scope">
                    <div v-if="scope.row.subOrderStatus == '2'">{{ scope.row.feeType == '1' ? '远智收' : scope.row.feeType
        == '1' ? '高校收' : '组合收' }}</div>
                    <el-select v-model="scope.row.feeType" placeholder="请选择" v-else
                        @change="(e) => changePaymet(scope.row, e)">
                        <el-option v-for="option in paymentOptions" :key="option.value" :label="option.label"
                            :value="option.value">
                        </el-option>
                    </el-select>
                </template>
            </el-table-column>
        </el-table>
        <div style="margin-bottom: 25px;">（注意：已缴清的科目，无法修改收取方式）</div>
        <el-row>
            <el-col :span="2"><span style="color:red">*</span> 备注：</el-col>
            <el-col :span="20">
                <el-input type="textarea" :rows="2" placeholder="请输入内容" maxlength="100" show-word-limit
                    v-model="remark"></el-input>
            </el-col>
        </el-row>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleOk">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        showChangeMethodPop: {
            type: Boolean,
            default: false
        },
        changeInfo: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            dynamicValidateForm: {
                domains: [{
                    remark: ''
                }],
            },
            paymentOptions: [
                { value: '1', label: '远智收' },
                { value: '2', label: '高校收' },
                // { value: '3', label: '组合收' },
            ],
            feeType: '',
            tableData: [],
            remark: '',
        }
    },
    watch: {
        showChangeMethodPop(newVal) {
            if (newVal) {
                this.getRecordList();
            }
        }
    },

    methods: {
        getRecordList() {
            this.$http.post('/feeTypeChange/getSubOrderList', { learnId: this.changeInfo.learnId }, { json: true }).then((res) => {
                const { fail, body } = res;
                if (!fail) {
                    this.tableData = body;
                }
            });
        },
        changePaymet(order, e) {
        },
        handleClose() {
            this.$emit('showChangeMethodPop', false)
        },
        handleOk() {
            if (!this.remark) {
                this.$message({ message: '请填写备注', type: 'error' });
                return
            }
            let subOrderNoList = []
            let orderObj = {}
            this.tableData.forEach(item => {
                if (item.subOrderStatus == 1) {
                    orderObj = {
                        subOrderNo: item.subOrderNo,
                        feeType: item.feeType
                    }
                    subOrderNoList.push(orderObj)
                }
            })
            let data = {
                learnId: this.changeInfo.learnId,
                remark: this.remark,
                list: subOrderNoList
            }
            this.$http.post('/feeTypeChange/changeFeeType', data, { json: true }).then((res) => {
                if (res.code == '00' && res.ok) {
                    this.$message({ message: '修改成功', type: 'success' });
                    this.$parent.getIndexList()
                }
            })
            this.remark = ''
            this.$emit('showChangeMethodPop', false)
        }
    }
}
</script>

<style lang="scss" scoped>
.dialog {
    
    ::v-deep.el-table th.el-table__cell {

        background-color: rgb(243, 243, 243);
    }

    ::v-deep .el-table tr {
        padding: 3px 0 !important;
    }



    ::v-deep .el-table__row .el-table__cell {
        padding: 0 !important;

    }
}
</style>
