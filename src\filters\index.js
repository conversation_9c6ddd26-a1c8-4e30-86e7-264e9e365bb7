import moment from 'moment';
import { splitOssImgUrl, getTextFromDict, transformSecond } from '@/utils';
import { courseType } from '@/config/constant';
import { ossUri } from '@/config/request';

export const splitOssUrl = splitOssImgUrl;

export const getDictVal = getTextFromDict;

// 转换上课类型
export const tansformCourseType = (val) => {
  return courseType[val] || '';
};

// 转换时间戳
export const transformTimeStamp = (val, format = 'YYYY-MM-DD HH:mm:ss') => {
  return !val ? '' : moment(val).format(format);
};

export const timestampToSecond = transformSecond;

export const downOssUrl = (val) => {
  if (!val) return;
  return ossUri + val + '?response-content-type=application/octet-stream';
};

/**
 * 拼接oss服务器上的图片链接 带版本号
 * @param {String} url 相对地址
 */
export const formatOssImgUrl = (url) => {
  return ossUri + url + '?v=' + new Date().getTime();
};
