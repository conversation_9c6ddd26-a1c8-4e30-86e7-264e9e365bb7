<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    title="查看订单信息"
    :visible.sync="show"
    :show-footer="false"
    confirm-text="立即发货"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <el-descriptions>
        <el-descriptions-item label="订单号">{{ form.orderId }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">{{ form.status | logisticsStatusEnum }}</el-descriptions-item>
        <el-descriptions-item label="下单人姓名">{{ form.realName }}</el-descriptions-item>
        <el-descriptions-item label="下单人远智编号">{{ form.yzCode }}</el-descriptions-item>
        <el-descriptions-item label="下单人时间">{{ form.createTime }}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="商品信息" direction="vertical" :column="7" border>
        <el-descriptions-item label="商品名称">{{ form.productName }}</el-descriptions-item>
        <el-descriptions-item label="规格名称">{{ form.productSpecName }}</el-descriptions-item>
        <el-descriptions-item label="商品单价(元)">{{ form.marketPrice }}</el-descriptions-item>
        <el-descriptions-item label="购买数量">{{ form.amount }}</el-descriptions-item>
        <el-descriptions-item label="总价(元)">{{ form.totalPrice }}</el-descriptions-item>
        <el-descriptions-item label="运费(元)">{{ form.freightAmount }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ form.remark }}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="收货人信息" :column="1" class="mt20">
        <el-descriptions-item label="收货人">{{ form.consigneeName }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ form.consigneeMobile }}</el-descriptions-item>
        <el-descriptions-item label="收获省市">
          {{ form.consigneeProvince }} - {{ form.consigneeCity }} - {{ form.consigneeDistrict }}  {{ form.consigneeStreet ? '-' + form.consigneeStreet : '' }}
        </el-descriptions-item>
        <el-descriptions-item label="收获详细地址">{{ form.consigneeAddress }}</el-descriptions-item>
        <el-descriptions-item label="物流名称">{{ form.logisticsType | logisticsTypeEnum }}</el-descriptions-item>
        <el-descriptions-item label="物流编号"> {{ form.logisticsNo }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </common-dialog>
</template>

<script>
import { logisticsStatus, logisticsType } from './../../type';
import { arrToEnum } from '@/utils';
const logisticsStatusEnum = arrToEnum(logisticsStatus);
const logisticsTypeEnum = arrToEnum(logisticsType);
export default {
  filters: {
    logisticsStatusEnum(val) {
      return logisticsStatusEnum[val] || '/';
    },
    logisticsTypeEnum(val) {
      return logisticsTypeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      show: false,
      form: {}
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框
    open() {
      if (this.currentId) {
        this.isEdit = true;
        this.$http.get(`/mallOrder/getById/${this.currentId}`).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form = body;
          }
        });
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang = "scss" scoped>
.des-title {
  font-size: 16px;
  font-weight: 700;
  color: #303133;
  margin: 12px 0;
}
</style>
