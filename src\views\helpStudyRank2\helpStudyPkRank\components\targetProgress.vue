<template>
  <div v-if="totalValue > 0" class="progress-contain">
    <template v-if="containType === 1">
      <div class="progress-bar-box">
        <div v-if="target" class="progress" :style="{ width: percentage + '%' }">
          <div class="percentage">{{ percentage }}%</div>
        </div>
      </div>
    </template>
    <template v-else-if="containType === 2">
      <div class="target-achieve-box">
        <img class="bg" src="@/assets/imgs/helpStudyPkRank/target-achieve.png" />
        <img class="star" src="@/assets/imgs/helpStudyPkRank/target-star.png" />
      </div>
    </template>
    <div class="count-total"><span class="count">{{ targetValue }}</span>/{{ totalValue }}</div>
  </div>
</template>

<script>
export default {
  props: {
    target: {
      type: Number | String,
      default: 0
    },
    total: {
      type: Number | String,
      default: 0
    }
  },
  data() {
    return {
      percentage: 0,
      containType: null, // 1: 进度条 2: 达成目标
      targetValue: 0, // 用于计算的目标值
      totalValue: 0 // 用于计算的总值
    };
  },
  created() {
    this.initValues();
    this.handleShowContain();
  },
  methods: {
    initValues() {
      // 确保值为数字类型，非数字则默认为0
      this.targetValue = !isNaN(Number(this.target)) ? Number(this.target) : 0;
      this.totalValue = !isNaN(Number(this.total)) ? Number(this.total) : 0;
    },
    handleShowContain() {
      if (this.targetValue >= this.totalValue) {
        this.containType = 2;
      } else {
        this.containType = 1;
        this.handlePercentage();
      }
    },
    handlePercentage() {
      let percentage = 0;

      if (this.totalValue > 0) {
        percentage = this.targetValue / this.totalValue * 100;
      }

      percentage = percentage > 100 ? 100 : percentage;
      percentage = percentage < 0 ? 0 : percentage;
      percentage = percentage.toFixed(0);
      this.percentage = percentage;
    }
  }
};
</script>

<style lang='scss' scoped>
.progress-contain {
  text-align: center;
  padding-top: 6px;
  width: 130px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .progress-bar-box {
    width: 110px;
    height: 10px;
    background: #383052;
    border-radius: 5px;
    position: relative;
    box-shadow: inset 0px 1px 3px 0px rgba(255, 255, 255, 0.5);
    margin-bottom: 9px;

    .progress {
      width: 40%;
      height: 100%;
      background: linear-gradient(180deg, #FFF3C7 0%, #F65C15 100%);
      border-radius: 5px;
      position: absolute;
      top: 0;
      left: 0;

      .percentage {
        position: absolute;
        top: 50%;
        left: 100%;
        transform: translate(-50%, -50%);
        padding: 0 3px;
        line-height: 14px;
        background: linear-gradient(180deg, #FFF9D2 0%, #FAD170 100%);
        border-radius: 8px;
        border: 0.5px solid #fff;
        font-weight: 600;
        font-size: 12px;
        color: #8D3200;
        white-space: nowrap;
      }
    }
  }
  .target-achieve-box {
    position: relative;
    width: 110px;
    height: 30px;
    .bg {
      width: 100%;
      height: 100%;
    }
    .star {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: -6px;
      width: 14px;
      height: 12px;
      animation: scale-animation 2s ease-in-out infinite;

      @keyframes scale-animation {
        0% {
          transform: translateX(-50%) scale(1);
        }
        20% {
          transform: translateX(-50%) scale(0.56);
        }
        40% {
          transform: translateX(-50%) scale(0.84);
        }
        60% {
          transform: translateX(-50%) scale(0.4);
        }
        80% {
          transform: translateX(-50%) scale(1.2);
        }
        100% {
          transform: translateX(-50%) scale(1);
        }
      }
    }
  }
  .count-total {
    color: #fff;
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;

    .count {
      color: #ffc107;
    }
  }
}
</style>

