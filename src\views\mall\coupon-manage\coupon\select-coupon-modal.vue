<template>
  <common-dialog
    class="common-dialog"
    width="80%"
    title="选择优惠券"
    :visible.sync="show"
    :show-footer="true"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-wrap">
      <!-- 左边 -->
      <div class="content-wrap">
        <p>待选列表</p>
        <el-form
          ref="searchForm"
          :inline="true"
          :model="form"
          size="mini"
          label-width="90px"
          class="wrap-search-form mt10"
          @submit.native.prevent='search'
        >
          <el-form-item label="上架状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择上架状态" clearable>
              <el-option v-for="item in couponShelfStatus" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="优惠券id" prop="couponId">
            <el-input v-model="form.couponId" placeholder="请输入优惠券id" clearable />
          </el-form-item>
          <el-form-item label="优惠券名称" prop="couponName">
            <el-input v-model="form.couponName" placeholder="请输入优惠券名称" clearable />
          </el-form-item>
          <el-form-item label="适用用户" prop="applyUser">
            <el-select v-model="form.applyUser" placeholder="请选择适用用户" clearable>
              <el-option v-for="item in couponUserType" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <div class="search-reset-box">
            <el-button
              type="primary"
              native-type="submit"
              size="mini"
            >搜索</el-button>
            <el-button size="mini" @click="search(0)">重置</el-button>
          </div>
        </el-form>
        <el-table ref="tableRef" v-loading="tableLoading" size="mini" :row-key="row => row.id" :data="tableData" border style="width: 100%" @select-all="selectLeftAll" @select="selectLeft">
          <el-table-column type="selection" width="39" />
          <el-table-column prop="couponId" label="优惠券id" align="center" />
          <el-table-column prop="couponName" label="优惠券名称" align="center" />
          <el-table-column label="适用用户" align="center">
            <template slot-scope="scope">
              {{ scope.row.applyUser | couponUserTypeEnum }}
            </template>
          </el-table-column>
          <el-table-column prop="couponPrice" label="优惠券额度" align="center" width="105" />
          <el-table-column label="上架状态" align="center">
            <template slot-scope="scope">
              {{ scope.row.status | couponShelfStatusEnum }}
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页区 -->
        <div class="yz-table-pagination">
          <pagination
            size="mini"
            :total="pagination.total"
            :page.sync="pagination.page"
            :limit.sync="pagination.limit"
            @pagination="getTableList"
          />
        </div>
      </div>
      <!-- 右边 -->
      <div class="content-wrap">
        <p>已选列表</p>
        <div class="mt10">
          <el-button type="danger" size="small" @click="batchDelete">批量删除</el-button>
          <span class="ml10">已选择{{ tableRightData.length }}个优惠券</span>
        </div>
        <el-table ref="tableSelectedRef" max-height="650" size="mini" :data="tableRightData" border style="width: 100%;margin-top: 60px;">
          <el-table-column prop="couponId" label="优惠券id" align="center" />
          <el-table-column prop="couponName" label="优惠券名称" align="center" />
          <el-table-column label="适用用户" align="center">
            <template slot-scope="scope">
              {{ scope.row.applyUser | couponUserTypeEnum }}
            </template>
          </el-table-column>
          <el-table-column prop="couponPrice" label="优惠券额度" align="center" width="105" />
          <el-table-column label="上架状态" align="center">
            <template slot-scope="scope">
              {{ scope.row.status | couponShelfStatusEnum }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="150">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" @click="handleDelete(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </common-dialog>
</template>

<script>
import { couponShelfStatus, couponUserType } from './../../type';
import { arrToEnum } from '@/utils';
const couponUserTypeEnum = arrToEnum(couponUserType);
const couponShelfStatusEnum = arrToEnum(couponShelfStatus);
export default {
  filters: {
    couponUserTypeEnum(val) {
      return couponUserTypeEnum[val] || '/';
    },
    couponShelfStatusEnum(val) {
      return couponShelfStatusEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tablePropData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      couponUserType: couponUserType, // 适用用户
      couponShelfStatus: couponShelfStatus, // 优惠券上架状态
      show: false,
      tableLoading: false,
      form: {
        status: '',
        couponId: '',
        couponName: '',
        applyUser: ''
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableData: [], // 左边表格数据
      tableRightData: [] // 右边表格已选择的数据
    };
  },
  watch: {
    visible(val) {
      this.show = val;
      this.tableRightData = JSON.parse(JSON.stringify(this.tablePropData));
    }
  },
  methods: {
    // 左边表格选择全部和全不选
    selectLeftAll(rows) {
      if (rows.length) {
        // 全选
        rows.forEach(row => {
          const findFlag = this.tableRightData.find(item => item.id == row.id);
          if (!findFlag) {
            this.tableRightData.push(row);
          }
        });
      } else {
        // 全不选
        this.tableRightData = this.tableRightData.filter(item => {
          const findFlag = this.tableData.find(row => row.id == item.id);
          return !findFlag;
        });
      }
    },
    // 表左边表格选择单个
    selectLeft(rows, row) {
      const selected = rows.length && rows.indexOf(row) !== -1; // true就是选中，0或者false是取消选中
      if (selected) {
        this.tableRightData.push(row);
      } else {
        const index = this.tableRightData.findIndex(item => item.id == row.id);
        if (index >= 0) {
          this.tableRightData.splice(index, 1);
        }
      }
    },
    // 单个删除
    handleDelete(index) {
      const row = this.tableRightData[index];
      this.$refs.tableRef.toggleRowSelection(row, false);
      this.tableRightData.splice(index, 1);
    },
    // 批量删除
    batchDelete() {
      this.$confirm('是否批量删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableRightData.forEach(item => {
          this.$refs.tableRef.toggleRowSelection(item);
        });
        this.tableRightData = [];
      }).catch(() => {});
    },
    // 处理筛选参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
    },
    // 查询和重置
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQueryParams();
      this.$post('getZMproductCouponList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
          const couponIds = this.tableRightData.map(item => item.id);
          this.tableData.forEach(item => {
            if (couponIds.includes(item.id)) {
              this.$nextTick(() => {
                this.$refs.tableRef.toggleRowSelection(item);
              });
            }
          });
        }
      });
    },
    // 打开弹框
    open() {
      this.getTableList();
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.show = false;
      this.$emit('confirm', this.tableRightData);
    }
  }
};
</script>

<style lang = "scss" scoped>

::v-deep .el-table tr {
  height: 40px;
}
.dialog-wrap {
  padding: 10px 0;
  display: flex;
  .content-wrap {
    flex: 1;
    padding: 0 20px;
  }
}
.wrap-search-form {
  position: relative;
  padding-right: 120px;
  .search-reset-box {
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>
