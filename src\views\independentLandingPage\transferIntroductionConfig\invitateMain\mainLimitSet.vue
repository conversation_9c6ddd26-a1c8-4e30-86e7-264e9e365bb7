<!-- 邀约上限设置 -->
<template>
  <el-dialog :visible.sync="visible" append-to-body width="38%" :close-on-click-modal="false" @open="openInit" @close="closeBtn">
    <div class="limits">
      <div class="limits-h4">
        <p>当天邀约上限（次日生效）：</p>
        <el-input-number v-model="inputVas" :min="0" :precision="0" :controls="false" />
        <p>今日邀约上限为<span class="limits-span">{{ todayMan }}</span>人</p>
      </div>
      <p style="line-height: 24px;">邀约人当天通过【邀约有礼】渠道邀约的新注册用户，若超过设置上限，超出的新注册用户，对应的任务奖励将失效（缴费任务除外）</p>
      <h4>变更记录</h4>
      <el-table v-loading="optionLoading" border size="small" :data="optionData" header-cell-class-name="table-cell-header" height="350">
        <el-table-column prop="createEmpName" label="操作人" align="center" />
        <el-table-column prop="createTime" label="操作时间" align="center" />
        <el-table-column prop="topValue" label="变更信息" align="center" />
      </el-table>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button :size="'mini'" @click="closeBtn">取 消</el-button>
      <el-button type="primary" :size="'mini'" @click="submitBtn">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: { visible: { type: Boolean, default: false }},
  data() {
    // 这里存放数据
    return {
      inputVas: 0,
      todayMan: 0,
      optionLoading: false,
      optionData: []
    };
  },
  methods: {
    // 获取邀约上限变更记录
    openInit() {
      this.optionLoading = true;
      this.$http.post('/inviteTaskConfig/getTaskTopValue.do')
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            console.log('获取邀约上限变更记录-data', body);
            this.todayMan = Number(body || 0);
          }
        });
      this.$http.post('/inviteTaskConfig/getTaskTopValueConfig.do')
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            body?.map(item => {
              if (item?.createTime?.indexOf('.0') > -1) {
                item.createTime = item.createTime.slice(0, -2);
              }
            });
            console.log('获取邀约上限变更记录-data', body);
            if (body.length) {
              this.inputVas = Number(body[0]?.topValue || 0);
              this.optionData = body;
            }
          }
          this.optionLoading = false;
        })
        .catch((err) => {
          this.optionLoading = false;
          console.log('获取邀约上限变更记录-err', err);
        });
    },
    // 修改提交
    submitBtn() {
      this.$http.post('/inviteTaskConfig/updateTaskTopValueConfig.do', { topValue: this.inputVas })
        .then((res) => {
          const bools = res?.code === '00';
          if (bools) this.closeBtn();
          this.$message({
            message: `修改${bools ? '成功' : '失败'}！`,
            type: bools ? 'success' : 'error'
          });
        })
        .catch((err) => {
          this.$message({ message: '修改失败！', type: 'error' });
          console.log('获取邀约上限变更记录-err', err);
        });
    },
    closeBtn() {
      this.$emit('on-close');
    }
  }
};
</script>

<style lang="scss">
.limits {
  .limits-h4 {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    p {
      font-weight: 550;
    }
    .limits-span {
      margin: 0 4px;
      color: red;
      font-size: 18px;
    }
    .el-input {
      width: 76%;
    }
  }
}
.dialog-footer {
  text-align: center;
}
</style>
