<template>
  <div v-if="showCountDown" class="countdown-box">
    <span class="text">活动倒计时： </span>
    <div class="time">{{ countdown.days }}</div>
    <span class="day-text">天</span>
    <div class="time">{{ countdown.hours }}</div>
    <span class="symbol">:</span>
    <div class="time">{{ countdown.minutes }}</div>
    <span class="symbol">:</span>
    <div class="time">{{ countdown.seconds }}</div>
  </div>
</template>

<script>
export default {
  name: 'Countdown',
  props: {
    endTime: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showCountDown: false, // 是否显示倒计时
      interval: null, // 定时器
      countdown: {
        days: '00',
        hours: '00',
        minutes: '00',
        seconds: '00'
      }
    };
  },
  // watch: {
  //   endTime(newVal, oldVal) {
  //     if (newVal !== oldVal) {
  //       this.resetCountdown();
  //     }
  //   }
  // },
  created() {
    this.countDown();
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
  },
  methods: {
    // resetCountdown() {
    //   if (this.interval) {
    //     clearInterval(this.interval);
    //     this.interval = null;
    //   }
    //   this.countDown();
    // },
    // 倒计时
    countDown() {
      this.interval = setInterval(() => {
        const nowTimestamp = new Date().getTime(); // 当前时间戳
        const diff = this.endTime - nowTimestamp; // 计算时间差
        if (diff <= 0) { // 如果时间差小于等于0，则倒计时结束
          clearInterval(this.interval); // 清除定时器
          this.interval = null;
          this.countdown.days = '00';
          this.countdown.hours = '00';
          this.countdown.minutes = '00';
          this.countdown.seconds = '00';
          this.showCountDown = false;
          return;
        }
        this.showCountDown = true;
        const days = Math.floor(diff / (1000 * 60 * 60 * 24)); // 计算天数
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)); // 计算小时数
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60)); // 计算分钟数
        const seconds = Math.floor((diff % (1000 * 60)) / 1000); // 计算秒数
        // 更新倒计时对象中的数据，并进行格式化
        this.countdown.days = days < 10 ? '0' + days : days.toString();
        this.countdown.hours = hours < 10 ? '0' + hours : hours.toString();
        this.countdown.minutes = minutes < 10 ? '0' + minutes : minutes.toString();
        this.countdown.seconds = seconds < 10 ? '0' + seconds : seconds.toString();
      }, 1000); // 每秒执行一次
    }
  }
};
</script>

<style lang="scss" scoped>
.countdown-box {
  display: flex;
  justify-content: center;
  align-items: center;
  .text {
    font-size: 18px;
    color: #FFFFFF;
    line-height: 26px;
  }
  .time {
    padding: 0 5px;
    min-width: 38px;
    height: 38px;
    background: url('../../../assets/imgs/helpStudyPkRank/count-down-bg.png') center/100% 100% no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Arial, Arial;
    font-size: 24px;
    color: #FFFFFF;
  }
  .day-text {
    font-size: 16px;
    color: #FFFFFF;
    line-height: 22px;
    margin: 0 5px;
  }
  .symbol {
    font-weight: 600;
    font-size: 24px;
    color: #FFFFFF;
    line-height: 33px;
    margin: 0 5px;
  }
}
</style>
