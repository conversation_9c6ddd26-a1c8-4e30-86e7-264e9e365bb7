<!-- 邀约有礼用户名单-弹窗-邀约明细 -->
<template>
  <div>
    <common-dialog width="60%" title="查看邀约明细" :show-footer="true" :visible.sync="visible" @open="getTableList" @close="closeBtn">
      <div class="invitate_detailed">
        <el-form class="messageAggregation-forms" :model="querys" :size="'mini'" label-width="120px" @submit.native.prevent="searchBtn(1)">
          <el-col :span="22">
            <el-form-item label="真实姓名：">
              <el-input v-model.trim="querys.name" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="远智编号：">
              <el-input v-model.trim="querys.yzCode" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="手机号码：">
              <el-input v-model.trim="querys.mobile" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="是否已报读：">
              <el-select v-model="querys.isBaodu" filterable clearable placeholder="请选择">
                <el-option label="是" value="1" />
                <el-option label="否" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否已缴费：">
              <el-select v-model="querys.subOrderStatus" filterable clearable placeholder="请选择">
                <el-option label="是" value="1" />
                <el-option label="否" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="注册时间：">
              <el-date-picker v-model="querys.newsTime" value-format="yyyy-MM-dd" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" />
            </el-form-item>
          </el-col>
          <div class="forms-btn">
            <el-button type="primary" icon="el-icon-search" native-type="submit" :size="'mini'">搜索</el-button>
          </div>
        </el-form>
        <el-table v-loading="tableLoading" border :size="'small'" :data="tableData" header-cell-class-name="table-cell-header" height="520">
          <el-table-column prop="invitateText" label="被邀约人信息" align="center" width="180px">
            <template slot-scope="scope">
              <span v-html="scope.row.invitateText"></span>
            </template>
          </el-table-column>
          <el-table-column prop="regTime" label="注册时间" align="center" width="140px" />
          <el-table-column prop="isReadText" label="是否已报读" align="center" />
          <el-table-column prop="isPayText" label="是否已缴费" align="center" />
          <el-table-column prop="totalZhimiCount" label="合计奖励智米数" align="center" />
          <el-table-column prop="finishCount" label="已领取" align="center">
            <template slot-scope="scope">
              <div class="invitate_des" @click="updateRemark(2, scope.row)">{{ scope.row.finishCount }}个 ＞</div>
            </template>
          </el-table-column>
          <el-table-column prop="noFinishCount" label="未完成" align="center">
            <template slot-scope="scope">
              <div class="invitate_des" @click="updateRemark(1, scope.row)">{{ scope.row.noFinishCount }}个 ＞</div>
            </template>
          </el-table-column>
          <el-table-column prop="invilidCount" label="已失效" align="center">
            <template slot-scope="scope">
              <div class="invitate_des" @click="updateRemark(0, scope.row)">{{ scope.row.invilidCount }}个 ＞</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="yz-table-pagination">
        <pagination :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.rows" @pagination="getTableList" />
      </div>
    </common-dialog>
    <!-- 弹窗-查看任务状态记录 -->
    <el-dialog :visible.sync="taskeShow" append-to-body title="查看任务状态">
      <el-table v-loading="taskeLoading" border size="small" :data="taskeList" header-cell-class-name="table-cell-header">
        <el-table-column prop="id" label="任务id" align="center" />
        <el-table-column prop="typeText" label="任务类型" align="center" />
        <el-table-column prop="title" label="任务标题" align="center" />
        <el-table-column prop="detail" label="任务明细" align="center" />
        <el-table-column v-if="taskeType==2" prop="giveZhimi" label="已获得智米" align="center" />
        <el-table-column v-if="taskeType==2" prop="completeTime" label="完成时间" align="center" />
        <el-table-column v-if="taskeType==0" prop="remark" label="失效原因" align="center" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    visible: { type: Boolean, default: false },
    userIds: { type: String, default: '' }
  },
  data() {
    // 这里存放数据
    return {
      show: false,
      querys: {},
      tableLoading: false,
      tableData: [],
      taskeShow: false,
      taskeLoading: false,
      taskeList: [],
      taskeType: 1,
      pagination: {
        total: 0,
        page: 1,
        rows: 10
      }
    };
  },
  methods: {
    // 查询
    searchBtn() {
      if (this.querys.newsTime) {
        this.querys['regTimeStart'] = this.querys.newsTime[0];
        this.querys['regTimeEnd'] = this.querys.newsTime[1];
      } else {
        this.querys['regTimeStart'] = '';
        this.querys['regTimeEnd'] = '';
      }
      console.log('查询', { ...this.querys });
      this.getTableList();
    },
    // 关闭
    closeBtn() {
      this.querys = {};
      this.$emit('on-close');
    },
    // 查看任务明细
    updateRemark(status, rows) {
      this.taskeType = status;
      this.taskeShow = true;
      // 接口
      this.taskeLoading = true;
      this.$http
        .post('/inviteTaskConfig/getInviteDetailStatus.do', { userId: rows.userId, pId: this.userIds, status })
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            body?.map((item) => {
              item.typeText = '';
              switch (Number(item.type)) {
                case 1:
                  item.typeText = '注册';
                  break;
                case 2:
                  item.typeText = '测评';
                  break;
                case 3:
                  item.typeText = '报名活动';
                  break;
                case 4:
                  item.typeText = '首次发帖';
                  break;
                case 5:
                  item.typeText = '添加老师';
                  break;
                case 6:
                  item.typeText = '报读';
                  break;
                case 7:
                  item.typeText = '缴费';
                  break;
                default:
                  break;
              }
              if (item.completeTime?.indexOf('.0') > -1) {
                item.completeTime = item.completeTime.slice(0, -2);
              }
            });
            this.taskeList = body;
          }
          this.taskeLoading = false;
        })
        .catch((err) => {
          this.taskeLoading = false;
          console.log('表格数00据-err', err);
        });
    },
    // 获取邀约明细记录数据
    getTableList() {
      this.tableLoading = true;
      const obs = JSON.parse(JSON.stringify(this.querys));
      for (const key in obs) {
        if (obs[key] == '' || obs[key] == null) delete obs[key];
      }
      delete obs.newsTime;
      this.$http
        .post('/inviteTaskConfig/getInviteDetail.do', { ...this.pagination, userId: this.userIds, ...obs })
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            const data = body?.data || [];
            data.map((item) => {
              item.invitateText = `昵称：${item.nickname || ''}<br />远智编号：${item.yzCode}<br />真实姓名：${item.realname}<br />手机号码：${item.mobile}`;
              item.totalZhimiCount = Number(item.totalZhimiCount || 0);
              item.finishCount = Number(item.finishCount || 0);
              item.noFinishCount = Number(item.noFinishCount || 0);
              item.invilidCount = Number(item.invilidCount || 0);
              item.isReadText = Number(item.isRead || 0) >= 1 ? '是' : '否';
              item.isPayText = Number(item.isPay || 0) >= 1 ? '是' : '否';
              if (item.regTime?.indexOf('.0') > -1) {
                item.regTime = item.regTime.slice(0, -2);
              }
            });
            this.tableData = data;
            this.pagination.total = body?.recordsTotal || 0;
          }
          this.tableLoading = false;
        })
        .catch((err) => {
          this.tableLoading = false;
          console.log('表格数据333-err', err);
        });
    }
  }
};
</script>

<style lang="scss" scoped>
// @import url(); 引入公共css类
.invitate_detailed {
  margin: 20px;
  .invitate_des {
    color: #05b0ff;
    position: relative;
    padding-bottom: 4px;
  }
  .invitate_des::after {
    position: absolute;
    left: calc(50% - 24px);
    bottom: 0px;
    content: '';
    width: 40px;
    height: 1px;
    background-color: #05b0ff;
  }
  .invitate_des:hover {
    cursor: pointer;
    color: red;
    font-weight: 550;
  }
}
</style>
