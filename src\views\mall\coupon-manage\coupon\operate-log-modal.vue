<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    title="操作记录"
    :visible.sync="show"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <el-table ref="table" v-loading="tableLoading" size="small" :data="tableData" style="width: 100%;" border>
        <el-table-column prop="createTime" label="操作时间" align="center" />
        <el-table-column prop="createBy" label="操作人" align="center" />
        <el-table-column prop="operate" label="变更信息" align="center" />
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentRow: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      show: false,
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableLoading: false,
      tableData: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框
    open() {
      if (this.currentRow.id) this.getTableList();
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = {
        businessId: this.currentRow.couponId,
        businessType: 2, // 业务类型 [1:商品 2:优惠券]
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
      this.$http.post('/mallOperateRecord/page', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>
