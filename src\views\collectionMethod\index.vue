<template>
    <div class="yz-base-container">
        <el-form ref="form" :model="form" label-width="150px" @submit.prevent="handleSubmit" inline>
            <el-form-item label="学员姓名：">
                <el-input v-model="form.stdName" style="width: 202px;"></el-input>
            </el-form-item>
            <el-form-item label="证件号码：">
                <el-input v-model="form.idCard" style="width: 202px;"></el-input>
            </el-form-item>
            <el-form-item label="手机号：">
                <el-input v-model="form.mobile" style="width: 202px;"></el-input>
            </el-form-item>
            <el-form-item label="远智编码：">
                <el-input v-model="form.yzCode" style="width: 202px;"></el-input>
            </el-form-item>
            <el-form-item label="招生类型：" prop="recruitType">
                <el-select v-model="form.recruitType" clearable placeholder="请选择" filterable>
                    <el-option v-for="item in recruitTypeList" :key="item.value" :label="item.key"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="层次：" prop="pfsnLevel">
                <el-select v-model="form.pfsnLevel" clearable placeholder="请选择" filterable>
                    <el-option v-for="item in pfsnLevelList" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="年级：" prop="grade">
                <el-select v-model="form.grade" clearable placeholder="请选择" filterable>
                    <el-option v-for="item in gradeList" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="院校：" prop="unvsId">
                <el-select v-model="form.unvsId" clearable placeholder="请选择" filterable @change="changePfsnsList">
                    <el-option v-for="item in unvsList" :key="item.unvsId" :label="item.unvsName"
                        :value="item.unvsId" />
                </el-select>
            </el-form-item>
            <el-form-item label="专业：" prop="pfsnId">
                <el-select v-model="form.pfsnId" clearable placeholder="请选择" filterable >
                    <el-option v-for="item in pfsnsList" :key="item.pfsnId" :label="'(' + item.pfsnCode+ ')' + item.pfsnName"
                        :value="item.pfsnId" />
                </el-select>
            </el-form-item>
            <el-form-item label="收取方式：">
                <el-select v-model="form.feeType" placeholder="请选择" clearable>
                    <el-option value="1" label="远智收"></el-option>
                    <el-option value="2" label="高校收"></el-option>
                    <el-option value="3" label="组合收"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item style="float:right">
              <el-button type="primary" @click="search('0')">查询</el-button>
              <el-button @click="search()">重置</el-button>
            </el-form-item>
        </el-form>
        <div style="float: right;">
            <el-button type="primary" @click="batchChange">批量变更</el-button>
        </div>
        <el-table ref="multipleTable" :data="tableData" style="width: 100%; margin: 50px 0 20px" border v-loading="loading" :row-style="{height:'1px'}">
            <el-table-column prop="stdName" label="学员姓名" align="center" width="100"></el-table-column>
            <el-table-column prop="grade" label="年级" align="center" width="100"></el-table-column>
            <el-table-column prop="unvsName" label="院校及专业" align="center" width="300">
                <template slot-scope="scope">
                   <div> {{ '[' + scope.row.recruitTypeName + ']'+ ' ' + scope.row.unvsName }}</div>
                    <div>{{ '[' + scope.row.pfsnLevelName + ']'+ ' ' + scope.row.pfsnName }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="stdStageName" label="学员阶段" align="center" width="100"></el-table-column>
            <el-table-column prop="pfsnName" label="缴费科目" align="center">
                <template slot-scope="scope">
                    <el-table :data="scope.row.orderList" border :row-style="{height:'1px'}">
                        <el-table-column prop="idNumber" label="科目" align="center">
                            <template slot-scope="scope">
                                {{ scope.row.itemCode + ':' + scope.row.itemName }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="payable" label="应缴" align="center"></el-table-column>
                        <el-table-column prop="subOrderStatus" label="缴费状态" align="center">
                            <template slot-scope="scope">
                                {{ scope.row.subOrderStatus == '1' ?'待缴费':'已缴费' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="feeType" label="收取方式" align="center">
                            <template slot-scope="scope">
                                <span style="color: red;" v-if="scope.row.feeType == '1'">远智收</span>
                                <!-- <el-tag type="danger" effect="dark" v-else-if="scope.row.feeType == '2'">高校收</el-tag> -->
                                <span style="color: blue;" v-else>高校收</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="280">
                <template slot-scope="scope">
                    <el-button type="primary" size="small" @click="vary(scope.row)" :disabled="scope.row.disableBtn">变更收取方式</el-button>
                    <el-button size="small" @click="record(scope.row)">操作记录</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="block">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page.sync="page.pageNum" layout="prev, pager, next, sizes,jumper"
                :page-sizes="[10, 20, 30, 40]" :total="page.total">
            </el-pagination>
        </div>
        <recordPop :showRecordPop="showRecordPop" @showRecordPop="showRecordPop = false" :recordId="recordId"></recordPop>
        <ImportStudents :showInfo="showInfo" @showInfo="showInfo = false"></ImportStudents>
        <changeMethodPop :showChangeMethodPop="showChangeMethodPop" @showChangeMethodPop="showChangeMethodPop = false" :changeInfo="changeInfo">
        </changeMethodPop>
    </div>
</template>

<script>
import recordPop from './recordPop'
import ImportStudents from './ImportStudents'
import changeMethodPop from './changeMethodPop'
export default {
    components: {
        recordPop, ImportStudents, changeMethodPop
    },
    data() {
        return {
            unvsList: [], // 院校
            pfsnsList: [], // 专业
            gradeList: [],
            pfsnLevelList: [], // 报考层次
            recruitTypeList: [], // 招生类型
            pfsnsList: [], // 专业
            studentStatusList:[],
            page: {
                pageNum: 1,
                pageSize: 10,
                total: 0
            },
            form: {
                stdName: '',
                idCard: '',
                mobile: '',
                yzCode: '',
                unvsId: '',
                grade: '',
                pfsnLevel: '',
                feeType: '',
                recruitType: '',
                pfsnId:''
            },
            tableData: [],
            showRecordPop: false,
            showInfo: false,
            showChangeMethodPop: false,
            loading:true,
            changeInfo:{},
            disableBtn:false,
            recordId:''
        };
    },
    created() {
        this.studentStatusList = this.$dictJson.stdStage;
        this.initData()
    },
    methods: {
        getIndexList() {
            this.loading = true
            let data = {
                ...this.form,
                page: this.page.pageNum,
                rows: this.page.pageSize
            }
            this.$http.post('/feeTypeChange/list', data,{json:true}).then((res) => {
                const { ok, body,code } = res;
                if (code=='00' && ok) {
                    body.data.forEach(element => {
                        this.recruitTypeList.forEach(item => {
                            if (element.recruitType == item.value) {
                                element.recruitTypeName = item.key
                            }
                        })
                        this.pfsnLevelList.forEach(item => {
                            if (element.pfsnLevel == item.value) {
                                element.pfsnLevelName = item.key
                            }
                        })
                        this.studentStatusList.forEach(val=>{
                            if(element.stdStage == val.dictValue){
                                element.stdStageName = val.dictName
                            }
                        })
                        element.disableBtn = !element.orderList.some(item => item.subOrderStatus == 1)
                    });
                    this.tableData = body.data;
                    this.page.total = body.recordsTotal;
                    this.loading = false
                }else{
                    this.loading = false
                }
            });
        },
        search(num) {
            this.page.pageSize = 10
            this.page.pageNum = 1
            if (num) {
                this.getIndexList()
            } else {
                this.form = {}
                this.getIndexList()
            }
        },
        initData() {
            this.getGradeList();
            this.getPfsnLevelList();
            this.getUnvsList();
            this.getRecruitTypeList()
            this.getPfsnsList()
            this.getIndexList()
        },
        changePfsnsList(e){
            this.getPfsnsList(e)
        },
        getGradeList() {
            this.$post('getDictInfoList', { name: 'grade' }).then((res) => {
                const { fail, body } = res;
                if (!fail) {
                    this.gradeList = body;
                }
            });
        },
        getPfsnLevelList() {
            this.$post('getDictInfoList', { name: 'pfsnLevel' }).then((res) => {
                const { fail, body } = res;
                if (!fail) {
                    this.pfsnLevelList = body;
                }
            });
        },
        getUnvsList() {
            this.$post('getUnvsList', { page: 1, rows: 999 }).then((res) => {
                const { fail, body } = res;
                if (!fail) {
                    this.unvsList = body.data;
                }
            });
        },
        getRecruitTypeList() {
            this.$post('getDictInfoList', { name: 'recruitType' }).then((res) => {
                const { fail, body } = res;
                if (!fail) {
                    this.recruitTypeList = body;
                }
            });
        },
        getPfsnsList(e) {
            this.$post('getPfsnsList', { page: 1, rows: 999, sId: e }).then((res) => {
                const { fail, body } = res;
                if (!fail) {
                    this.pfsnsList = body.data;
                }
            });
        },

        batchChange() {
            this.showInfo = true
        },
        vary(row) {
            this.changeInfo = row
            this.showChangeMethodPop = true
        },
        record(row) {
            this.recordId = row.learnId
            this.showRecordPop = true
        },
        handleSizeChange(val) {
            this.page.pageSize = val
            this.getIndexList()
        },
        handleCurrentChange(val) {
            this.page.pageNum = val
            this.getIndexList()
        },
    }

};
</script>

<style lang="scss" scoped>
.block {
    margin-top: 10px;
    text-align: right;
}
::v-deep.el-table th.el-table__cell {

    background-color: rgb(243, 243, 243);
}
::v-deep .el-table__row .el-table__cell {
   padding: 3px 0 !important;
}
</style>