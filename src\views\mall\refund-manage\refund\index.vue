<template>
  <div>
    <!-- 顶部筛选 -->
    <el-form
      ref="searchForm"
      :model="form"
      label-width="120px"
      class="yz-search-form"
      size="mini"
      @submit.native.prevent='search'
    >
      <el-form-item label="下单人远智编号:" prop="yzCode">
        <el-input v-model="form.yzCode" placeholder="请输入下单人远智编号" clearable />
      </el-form-item>
      <el-form-item label="下单人手机号码:" prop="mobile">
        <el-input v-model="form.mobile" placeholder="请输入下单人手机号码" clearable />
      </el-form-item>
      <el-form-item label="下单人真实姓名:" prop="realName">
        <el-input v-model="form.realName" placeholder="请输入下单人真实姓名" clearable />
      </el-form-item>
      <el-form-item label="订单号:" prop="orderId">
        <el-input v-model="form.orderId" placeholder="请输入订单号" clearable />
      </el-form-item>
      <el-form-item label="退款状态:" prop="refundStatus">
        <el-select v-model="form.refundStatus" placeholder="请选择退款状态" clearable>
          <el-option v-for="item in refundStatus" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态:" prop="refundApprovalStatus">
        <el-select v-model="form.refundApprovalStatus" placeholder="请选择审核状态" clearable>
          <el-option v-for="item in refundExamineStatus" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          native-type="submit"
          size="mini"
        >查询</el-button>
        <el-button size="mini" @click="search(0)">重置</el-button>
      </div>
    </el-form>

    <!-- 新增栏 -->
    <div style="float:right;margin:15px 25px 15px 0;">
      <el-button type="primary" size="mini" @click="applyVisible = true">新增退款申请</el-button>
    </div>

    <el-table ref="table" v-loading="tableLoading" size="small" :data="tableData" style="width: 100%; margin: 25px 0" border>
      <el-table-column prop="orderId" label="订单号" align="center" />
      <el-table-column prop="productId" label="商品id" align="center" />
      <el-table-column prop="productName" label="商品名称" align="center" />
      <el-table-column label="商品类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.productType | goodsTypeEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="productSpecName" label="规格名称" align="center" />
      <el-table-column prop="marketPrice" label="商品单价（元）" align="center" />
      <el-table-column prop="amount" label="购买数量" align="center" />
      <el-table-column prop="totalPrice" label="商品总价（元）" align="center" />
      <el-table-column prop="freightAmount" label="运费（元）" align="center" />
      <el-table-column prop="couponDiscount" label="优惠券抵扣（元）" align="center">
        <template slot-scope="scope">
          <p>-{{ scope.row.couponDiscount }}</p>
        </template>
      </el-table-column>
      <el-table-column label="下单用户信息" align="center">
        <template slot-scope="scope">
          <p>远智编号: {{ scope.row.yzCode }}</p>
          <p>真实姓名: {{ scope.row.realName }}</p>
          <p>手机号: {{ scope.row.mobile }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="orderTime" label="下单时间" align="center" />
      <el-table-column prop="refundTime" label="退款申请时间" align="center" />
      <el-table-column prop="refundReason" label="退款原因" align="center" />
      <el-table-column prop="refundZm" label="智米退款金额（元）" align="center" />
      <el-table-column prop="refundBalance" label="余额退款金额（元）" align="center" />
      <el-table-column prop="refundCash" label="现金退款金额（元）" align="center" />
      <el-table-column label="退款审批状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.refundApprovalStatus | refundExamineStatusEnum }}
        </template>
      </el-table-column>
      <el-table-column label="退款状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.refundStatus | refundStatusEnum }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button class="mt10" type="primary" size="small" @click="handleLook(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <look-modal :visible.sync="lookVisible" :currentId="currentId" />
    <apply-modal :visible.sync="applyVisible" @updateList="handleUpdateList" />
  </div>
</template>

<script>
import lookModal from './look-modal';
import applyModal from './../apply';
import { arrToEnum } from '@/utils';
import { goodsType, refundStatus, refundExamineStatus } from './../../type';
const goodsTypeEnum = arrToEnum(goodsType);
const refundStatusEnum = arrToEnum(refundStatus);
const refundExamineStatusEnum = arrToEnum(refundExamineStatus);
export default {
  components: {
    lookModal,
    applyModal
  },
  filters: {
    goodsTypeEnum(val) {
      return goodsTypeEnum[val] || '/';
    },
    refundStatusEnum(val) {
      return refundStatusEnum[val] || '/';
    },
    refundExamineStatusEnum(val) {
      return refundExamineStatusEnum[val] || '/';
    }
  },
  data() {
    return {
      refundStatus: refundStatus, // 退款状态
      refundExamineStatus: refundExamineStatus, // 退款审核状态
      lookVisible: false, // 查看弹框
      applyVisible: false, // 新增退款申请弹框
      currentId: '',
      form: {
        yzCode: '',
        mobile: '',
        realName: '',
        orderId: '',
        refundStatus: '',
        refundApprovalStatus: ''
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableLoading: false,
      tableData: []
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    handleUpdateList() {
      this.getTableList();
      this.$emit('updateList');
    },
    // 查看
    handleLook(row) {
      this.currentId = row.id;
      this.lookVisible = true;
    },
    // 查询和重置
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 处理筛选参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQueryParams();
      this.$post('getRefundList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>

<style lang = "scss" scoped>
.option-box {
  margin: 15px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    color:red;
  }
}
</style>
