<template>
  <common-dialog
    :show-footer="true"
    width="600px"
    title="备注"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        size='mini'
        :model='form'
        label-width='40px'
      >
        <el-form-item label='备注' prop='goodsBaseId' style="display: block">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="6"
            placeholder="请输入"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      form: {
        remark: ''
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      if (this.type === 'master') {
        this.form.remark = this.row.remark;
      } else {
        this.form.remark = this.row.remarkTwo;
      }
    },
    submit() {
      var params = {};
      var api = '';
      if (this.type === 'master') {
        var params1 = {
          gpId: this.row.gpId,
          addRemark: this.form.remark
        };
        api = 'paperMasterRemark';
        params = params1;
      } else {
        var params2 = {
          gpId: this.row.gpId,
          addRemarkTwo: this.form.remark
        };
        api = 'paperTeacherRemark';
        params = params2;
      }
      this.$post(api, params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.show = false;
            this.$parent.getTableList();
          }
        });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
