<template>
  <div class="yz-base-container">
    <!-- 顶部筛选 -->
    <el-form
      ref="searchForm"
      :model="form"
      label-width="110px"
      class="yz-search-form"
      size="mini"
      @submit.native.prevent='search'
    >
      <el-form-item label="商品id:" prop="id">
        <el-input v-model="form.id" placeholder="请输入商品id" clearable />
      </el-form-item>
      <el-form-item label="商品名称:" prop="productName">
        <el-input v-model="form.productName" placeholder="请输入商品名称" clearable />
      </el-form-item>
      <el-form-item label="商品类型:" prop="productType">
        <el-select v-model="form.productType" placeholder="请选择商品类型:" clearable>
          <el-option v-for="item in goodsType" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="商品状态:" prop="productStatus">
        <el-select v-model="form.productStatus" placeholder="请选择商品状态" clearable>
          <el-option v-for="item in goodsStatus" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否上架:" prop="status">
        <el-select v-model="form.status" placeholder="请选择类型:" clearable>
          <el-option label="上架" :value="1" />
          <el-option label="下架" :value="0" />
        </el-select>
      </el-form-item>
      <div class="search-reset-box">
        <el-button
          type="primary"
          native-type="submit"
          size="mini"
        >查询</el-button>
        <el-button size="mini" @click="search(0)">重置</el-button>
      </div>
    </el-form>
    <!-- 新增栏 -->
    <div style="float:right;margin:15px 25px 15px 0;">
      <el-button type="primary" size="mini" @click="openAddModal('autarky')">新增自营好物</el-button>
      <el-button type="primary" size="mini" @click="openAddModal('virtually')">新增虚拟礼品</el-button>
      <el-button type="primary" size="mini" @click="openAddModal('jdStore')">新增京东百货</el-button>
      <el-button type="danger" size="mini" @click="batchDown">批量下架</el-button>
      <el-button type="warning" size="mini" @click="exportList">EXCEL导出商品</el-button>
    </div>
    <el-table ref="table" v-loading="tableLoading" size="small" :data="tableData" style="width: 100%; margin: 25px 0" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="39" />
      <el-table-column prop="id" label="商品id" align="center" />
      <el-table-column label="商品类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.productType | goodsTypeEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="productName" label="商品名称" align="center" />
      <el-table-column label="商品图片" align="center">
        <template slot-scope="scope">
          <el-image
            :src="ossUri + scope.row.urlList[0]"
            style="height: 60px"
          />
        </template>
      </el-table-column>
      <el-table-column prop="exchangeAmount" label="可兑换数量" align="center" />
      <el-table-column prop="retailPrice" label="成本价（元）" align="center" />
      <el-table-column prop="marketPrice" label="售价（元）" align="center" />
      <el-table-column prop="freightAmount" label="运费（元）" align="center" />
      <el-table-column label="兑换起止时间" align="center">
        <template slot-scope="scope">
          <p>起：{{ scope.row.sellingStartTime }}</p>
          <p>止：{{ scope.row.sellingEndTime || '永久有效' }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="weight" label="商品权重" align="center" />
      <el-table-column label="商品状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.productStatus | goodsStatusEnum }}
        </template>
      </el-table-column>
      <el-table-column label="是否上架" align="center">
        <template slot-scope="scope">
          {{ scope.row.status | yesOrNoEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="updateBy" label="最后操作人" align="center" />
      <el-table-column prop="updateTime" label="最后操作时间" align="center" />
      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button :type="scope.row.status == 1 ? 'info': 'primary'" size="small" @click="handleUpOrDown(scope.row)">{{ scope.row.status == 1 ? '下架': '上架' }}</el-button>
          <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <autarky-modal :visible.sync="autarkyVisible" :currentId="currentId" />
    <virtually-modal :visible.sync="virtuallyVisible" :currentId="currentId" />
    <jd-store-modal :visible.sync="jdStoreVisible" :currentId="currentId" />
  </div>
</template>

<script>
import { ossUri, downUri } from '@/config/request';
import { goodsType, goodsStatus } from './../type';
import autarkyModal from './autarky-modal';
import virtuallyModal from './virtually-modal';
import jdStoreModal from './jd-store-modal';
import { arrToEnum, SplicingParams } from '@/utils';
const goodsTypeEnum = arrToEnum(goodsType);
const goodsStatusEnum = arrToEnum(goodsStatus);
export default {
  components: {
    autarkyModal,
    virtuallyModal,
    jdStoreModal
  },
  filters: {
    goodsTypeEnum(val) {
      return goodsTypeEnum[val] || '/';
    },
    goodsStatusEnum(val) {
      return goodsStatusEnum[val] || '/';
    },
    yesOrNoEnum(val) {
      const JudgeEnum = { 0: '下架', 1: '上架' };
      return JudgeEnum[val] || '/';
    }
  },
  data() {
    return {
      ossUri: ossUri,
      autarkyVisible: false, // 自营好物弹框
      virtuallyVisible: false, // 虚拟礼品弹框
      jdStoreVisible: false, // 京东百货弹框
      goodsType: goodsType,
      goodsStatus: goodsStatus,
      currentRows: [], // 批量选择的id
      currentId: '',
      form: {
        id: '',
        productName: '',
        productType: '',
        productStatus: '',
        status: ''
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableLoading: false,
      tableData: []
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    openAddModal(name) {
      this.currentId = '';
      switch (name) {
        case 'autarky':
          this.autarkyVisible = true;
          break;
        case 'virtually':
          this.virtuallyVisible = true;
          break;
        case 'jdStore':
          this.jdStoreVisible = true;
          break;
        default:
          break;
      }
    },
    // 批量下架
    batchDown() {
      if (this.currentRows.length <= 0) {
        this.$message.error('请勾选！');
        return false;
      }
      this.$confirm('是否批量下架', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          ids: this.currentRows.map(item => item.id),
          status: 0
        };
        this.$post('updateZMProductStatus', params, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message.success('操作成功');
            this.getTableList();
          }
        });
      });
    },
    // 表格选择
    handleSelectionChange(arr) {
      this.currentRows = arr;
    },
    // 导出数据
    exportList() {
      const params = SplicingParams(this.handleQueryParams());
      const exportUrl = downUri + '/product/exportProduct?' + params;
      window.location.href = exportUrl;
    },
    handleExportParams(value) {
      return value == undefined ? '' : value;
    },
    // 上架或者下架
    handleUpOrDown(row) {
      const params = {
        ids: [row.id],
        status: row.status == 1 ? 0 : 1
      };
      this.$post('updateZMProductStatus', params, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message.success('操作成功');
          this.getTableList();
        }
      });
    },
    // 编辑
    handleEdit(row) {
      this.currentId = row.id;
      switch (row.productType) {
        case 'SELF_ENTITY_PRODUCT':
          this.autarkyVisible = true;
          break;
        case 'SELF_VIRTUAL_PRODUCT':
          this.virtuallyVisible = true;
          break;
        case 'JD_PRODUCT':
          this.jdStoreVisible = true;
          break;
        default:
          break;
      }
    },
    // 查询和重置
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 处理筛选参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQueryParams();
      this.$post('getZMProductList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>

<style lang = "scss" scoped>

</style>
