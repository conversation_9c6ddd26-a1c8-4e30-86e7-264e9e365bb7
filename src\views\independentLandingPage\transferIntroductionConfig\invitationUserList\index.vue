<!-- 邀约有礼用户名单 -->
<template>
  <div class="messageAggregation">
    <el-form class="messageAggregation-forms" :model="querys" :size="'mini'" label-width="120px" @submit.native.prevent="searchBtn(1)">
      <el-col :span="22">
        <el-form-item label="真实姓名：">
          <el-input v-model.trim="querys.name" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="远智编号：">
          <el-input v-model.trim="querys.yzCode" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="手机号码：">
          <el-input v-model.trim="querys.mobile" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="是否黑名单：">
          <el-select v-model="querys.isBlacklist" filterable clearable placeholder="请选择">
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否已报读：">
          <el-select v-model="querys.isBaodu" filterable clearable placeholder="请选择">
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否已缴费：">
          <el-select v-model="querys.subOrderStatus" filterable clearable placeholder="请选择">
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <div class="forms-btn">
        <el-button type="primary" icon="el-icon-search" native-type="submit" :size="'mini'" v-text="'搜索'" />
        <el-button icon="el-icon-refresh" :size="'mini'" @click="searchBtn(0)" />
      </div>
    </el-form>
    <div style="display: flex;justify-content: space-between;align-items: center;">
      <div style="color: red;">展示【邀约有礼】渠道邀约注册>0 人的邀约人名单</div>
      <el-button style="float: right; margin: 15px 25px 15px 0" type="primary" :size="'mini'" icon="el-icon-download" @click="exportBtn">
        导出数据
      </el-button>
    </div>
    <el-table v-loading="tableLoading" border :size="'small'" :data="tableData" header-cell-class-name="table-cell-header">
      <el-table-column prop="realname" label="真实姓名" align="center" />
      <el-table-column prop="yzCode" label="远智编号" align="center" />
      <el-table-column prop="mobile" label="手机号码" align="center" />
      <el-table-column prop="isReadText" label="是否已报读" align="center" />
      <el-table-column prop="isPayText" label="是否已缴费" align="center" />
      <el-table-column prop="inviteGiftRegCount" label="【邀约有礼】渠道邀约注册人数" align="center" />
      <el-table-column prop="inviteGiftPayCount" label="【邀约有礼】渠道缴费人数" align="center" />
      <el-table-column prop="totalZhimiCount" label="累计奖励智米数" align="center" />
      <el-table-column prop="isBlackListText" label="是否黑名单" align="center" />
      <el-table-column label="操作" align="center" width="320px">
        <template slot-scope="scope">
          <el-button :size="'mini'" style="margin-bottom: 6px;" type="primary" @click="lookDetails(scope.row)">
            查看邀约明细
          </el-button>
          <el-button :size="'mini'" style="margin-bottom: 6px;" type="primary" @click="lookRecord(scope.row)">
            操作记录
          </el-button>
          <el-button :size="'mini'" :type="scope.row.isBlackList==1?'success':'info'" @click="onBlackList(scope.row)">
            {{ scope.row.isBlackList==1?'取消':'加入' }}黑名单
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="yz-table-pagination">
      <pagination :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.rows" @pagination="getTableList" />
    </div>
    <!-- 弹窗-查看邀约明细 -->
    <invitateDetailedList :visible="detaileVisible" :userIds="userIds" @on-close="detaileVisible = false" />
    <!-- 弹窗-查看操作记录 -->
    <el-dialog :visible.sync="recordVisible" append-to-body title="查看操作记录">
      <el-table v-loading="recordLoading" border size="small" :data="recordData" header-cell-class-name="table-cell-header">
        <el-table-column prop="createTime" label="操作时间" align="center" />
        <el-table-column prop="createEmp" label="操作人" align="center" />
        <el-table-column prop="remark" label="变更信息" align="center" />
      </el-table>
    </el-dialog>
    <!-- 弹窗-确认弹窗 -->
    <el-dialog :visible.sync="lackListVisible" append-to-body width="30%">
      <h3 slot="title" class="dialog-title">【{{ blackNums ? '取消' : '加入' }}黑名单】确认弹窗</h3>
      <div class="dialog-content">
        <p>是否将用户<span>{{ blackNums ? '取消' : '加入' }}</span>黑名单，<span>{{ blackNums ? '取消' : '加入' }}</span>后：</p>
        <p>1、<span>{{ !blackNums ? ' 将无法' : '可正常' }}</span>访问【邀约有礼】页</p>
        <p>2、通过【邀约有礼】渠道新注册的被邀约人，完成任务<span>{{ !blackNums ? '将不再赠送' : '可获得' }}</span>智米奖励</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :size="'mini'" @click="lackListVisible = false">取 消</el-button>
        <el-button type="primary" :size="'mini'" @click="onLackListBtn">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import invitateDetailedList from './invitateDetailedList';
import { downUri } from '@/config/request';
export default {
  components: { invitateDetailedList },
  data() {
    // 这里存放数据
    return {
      querys: {},
      pagination: {
        total: 0,
        page: 1,
        rows: 10
      },
      tableLoading: false,
      tableData: [],
      detaileVisible: false,
      recordData: [],
      recordLoading: false,
      recordVisible: false,
      lackListVisible: false,
      blackNums: null,
      userIds: ''
    };
  },
  created() {
    this.getTableList();
  },
  // 方法集合
  methods: {
    // 查询-重置
    searchBtn(type) {
      if (type === 1) this.pagination.page = 1;
      else this.querys = {};
      this.getTableList();
    },
    // 导出
    exportBtn() {
      console.log('导出文---querys', this.querys);
      let urs = '';
      for (const key in this.querys) {
        urs += `${key}=${this.querys[key] || ''}&`;
      }
      if (urs) urs = urs.slice(0, -1);
      const exportUrl = `${downUri}/inviteTaskConfig/export.do?${urs}`;
      console.log('导出文---exportUrl', exportUrl);
      // 导出文件
      window.location.href = exportUrl;
      this.$message({ message: '导出成功！', type: 'success' });
    },
    // 查看邀约明细
    lookDetails({ userId }) {
      this.userIds = userId;
      this.detaileVisible = true;
    },
    // 查看操作记录
    lookRecord({ userId }) {
      this.recordLoading = true;
      this.recordVisible = true;
      this.$http.post('/inviteTaskConfig/selectInviteBlackList.do', { userId })
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            body?.map(item => {
              item.remark = Number(item.isBlackList || 0) ? '加入黑名单' : '取消黑名单';
              if (item.createTime?.indexOf('.0') > -1) {
                item.createTime = item.createTime.slice(0, -2);
              }
            });
            this.recordData = body;
          }
          this.recordLoading = false;
        })
        .catch((err) => {
          this.recordLoading = false;
          console.log('表格数据111-err', err);
        });
    },
    // 取消-加入黑名单
    onBlackList({ isBlackList, userId }) {
      this.userIds = userId;
      this.blackNums = Number(isBlackList);
      this.lackListVisible = true;
    },
    // 确定提交：取消-加入黑名单
    onLackListBtn() {
      this.$http.post('/inviteTaskConfig/insertBlackList.do', {
        userId: this.userIds,
        isBlackList: this.blackNums ? 0 : 1
      })
        .then((res) => {
          const bools = res?.code === '00';
          if (bools) {
            this.lackListVisible = false;
            this.userIds = '';
            this.blackNums = null;
            this.getTableList();
          }
          this.$message({
            message: `操作${bools ? '成功' : '失败'}！`,
            type: bools ? 'success' : 'error'
          });
        });
    },
    // 获取用户名单列表
    getTableList() {
      this.tableLoading = true;
      const params = { ...this.pagination, ...this.querys };
      for (const key in params) {
        if (!params[key]) delete params[key];
      }
      // params.start = params.page;
      this.$http
        .post('/inviteTaskConfig/getInviteList.do', params)
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            const data = body?.data || [];
            data.map((item) => {
              item.isRead = Number(item.isRead || 0);
              item.isPay = Number(item.isPay || 0);
              if (item.isBlacklist == null || item.isBlacklist == 'null' || item.isBlacklist == '') {
                item.isBlackList = 0;
              } else item.isBlackList = Number(item.isBlacklist || 0);
              delete item.isBlacklist;
              item.inviteGiftRegCount = Number(item.inviteGiftRegCount || 0);
              item.inviteGiftPayCount = Number(item.inviteGiftPayCount || 0);
              item.totalZhimiCount = Number(item.totalZhimiCount || 0);
              item.isReadText = Number(item.isRead) >= 1 ? '是' : '否';
              item.isPayText = Number(item.isPay) >= 1 ? '是' : '否';
              item.isBlackListText = Number(item.isBlackList) == 1 ? '是' : '否';
            });
            this.tableData = data;
            this.pagination.total = body?.recordsTotal || 0;
          }
          this.tableLoading = false;
        })
        .catch((err) => {
          this.tableLoading = false;
          console.log('表格数据000-err', err);
        });
    }
  }
};
</script>

<style lang="scss">
// @import url(); 引入公共css类
.messageAggregation {
  padding: 40px 30px;

  &-forms {
    margin-bottom: 30px;

    .el-form-item {
      width: 32%;
      display: inline-block;
    }

    .forms-btn {
      padding-top: 40px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .popclass {
    height: 200px;
    overflow: hidden;
    overflow-y: scroll;
  }
}
.dialog-content {
  p {
    line-height: 28px;
  }
  span {
    color: #ee2b2b;
  }
}
.el-dialog__header {
  padding-bottom: 0;
  .dialog-title {
    line-height: 24px;
    text-align: center;
  }
}
</style>
