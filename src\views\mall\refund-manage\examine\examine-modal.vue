<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    title="发起退款"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-descriptions title="订单明细:" direction="vertical" :column="11" border>
        <el-descriptions-item label="订单号">{{ form.orderId }}</el-descriptions-item>
        <el-descriptions-item label="商品id">{{ form.productId }}</el-descriptions-item>
        <el-descriptions-item label="商品名称">{{ form.productName }}</el-descriptions-item>
        <el-descriptions-item label="商品类型">{{ form.productType | goodsTypeEnum }}</el-descriptions-item>
        <el-descriptions-item label="规格名称">{{ form.productSpecName }}</el-descriptions-item>
        <el-descriptions-item label="商品单价（元）">{{ form.marketPrice }}</el-descriptions-item>
        <el-descriptions-item label="购买数量">{{ form.amount }}</el-descriptions-item>
        <el-descriptions-item label="商品总价（元）">{{ form.totalPrice }}</el-descriptions-item>
        <el-descriptions-item label="优惠券抵扣（元）">-{{ form.couponDiscount }}</el-descriptions-item>
        <el-descriptions-item label="运费（元）">{{ form.freightAmount }}</el-descriptions-item>
        <el-descriptions-item label="下单用户信息">
          <p>远智编号: {{ form.yzCode }}</p>
          <p>真实姓名: {{ form.realName }}</p>
          <p>手机号: {{ form.mobile }}</p>
        </el-descriptions-item>
        <el-descriptions-item label="下单时间">{{ form.orderTime }}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="退款详情:" class="mt10" direction="vertical" :column="6" border>
        <el-descriptions-item label="智米抵扣（元）">{{ form.zmDiscount }}</el-descriptions-item>
        <el-descriptions-item label="余额抵扣（元）">{{ form.balanceDiscount }}</el-descriptions-item>
        <el-descriptions-item label="现金支付（元）">{{ form.paymentPrice }}</el-descriptions-item>
        <el-descriptions-item label="智米退款金额（元）">{{ form.refundZm }}</el-descriptions-item>
        <el-descriptions-item label="余额退款金额（元）">{{ form.refundBalance }}</el-descriptions-item>
        <el-descriptions-item label="现金退款金额（元）">{{ form.refundCash }}</el-descriptions-item>
      </el-descriptions>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="90px"
        size="small"
      >
        <el-form-item label="退款方式:" class="mt10">
          <span>原路退回</span>
        </el-form-item>

        <el-form-item label="退款原因:">
          <span>{{ form.refundReason }}</span>
        </el-form-item>

        <el-form-item label="审核意见:" required>
          <div class="examine-bid">
            <el-form-item prop="crStatus">
              <el-radio-group v-model="form.crStatus">
                <el-radio label="3">审核通过</el-radio>
                <el-radio label="4">审核驳回，驳回理由</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="form.crStatus == '4'" prop="reason" class="examine-bid-input-box ml10">
              <el-input v-model="form.reason" class="bid-input" placeholder="请输入驳回理由" />
            </el-form-item>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { goodsType } from '../../type';
import { arrToEnum } from '@/utils';
const goodsTypeEnum = arrToEnum(goodsType);
export default {
  filters: {
    goodsTypeEnum(val) {
      return goodsTypeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      confirmLoading: false,
      show: false,
      form: {},
      rules: {
        crStatus: [
          { required: true, message: '请选择审核意见', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入驳回理由', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框
    open() {
      if (this.currentId) {
        this.$http.get(`/mallRefund/getById/${this.currentId}`).then(res => {
          const { fail, body } = res;
          if (!fail) {
            const form = body;

            let steps = null;
            if (form.checkRecords.length == 1) {
              steps = 2;
            } else {
              steps = 3;
            }
            form.steps = steps;
            this.form = form;
          }
        });
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },

    // 提交
    submit() {
      this.$refs.formRef.validate((valid) => {
        if (!valid) return false;

        this.confirmLoading = true;

        const form = JSON.parse(JSON.stringify(this.form));
        const params = {
          refundId: this.currentId,
          crStatus: form.crStatus,
          reason: form.reason
        };
        this.$post('zMRefundAudit', params, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>

<style lang = "scss" scoped>

.examine-bid {
  display: flex;
  flex-direction: row;

  .el-radio {
    display: block;
    line-height: 32px;
  }
  .examine-bid-input-box {
    flex: 1;
  }
  .bid-input {
    margin-top: 30px;
  }
}
</style>
