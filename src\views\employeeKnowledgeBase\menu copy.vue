<template>
  <el-col :span="4">
    <el-tree
      ref="elTree"
      :data="data"
      node-key="id"
      :props="props"
      :highlight-current="true"
      default-expand-all
      :expand-on-click-node="false"
      :render-content="renderContent"
      :draggable="draggable"
      :allow-drop="allowDrop"
      @node-drop="nodeDrop"
    />
  </el-col>
</template>

<script>
export default {
  props: {
    isShowMore: {
      type: Boolean,
      default: false
    },
    draggable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      data: [],
      props: {
        label: 'name'
      },
      breadList: []
    };
  },
  created() {
    this.getProblemTreeList();
  },
  methods: {
    getProblemTreeList() {
      this.$http.get('/question/branch/selectQuestionBranchListGroupParentIdInclusionParent.do')
        .then(res => {
          if (res.ok) {
            this.data = res.body;
            // 默认选中第一个节点
            this.handleSetCurrentKey(res.body[0].id);
            this.$emit('problemInfoList', res.body[0].id);
            this.breadList = [];
            this.$nextTick(() => {
              this.getTreeNode(this.$refs.elTree.getNode(res.body[0].id));
              this.$emit('handleCrumbs', this.breadList);
            });
          }
        });
    },
    renderContent(h, { node, data, store }) {
      return (
        <div class='custom-tree-node'>
          <div class='label' onClick={() => this.handleMenu(data.id)}>{node.label}</div>
          {
            this.isShowMore ? (
              <el-button
                size='mini'
                type='text'
                onClick={() => this.moreHandle(node)}
                style='margin-top:-6px;width:20px'
              >···</el-button>
            ) : ''
          }
        </div>
      );
    },
    moreHandle(node) {
      this.$emit('append', node);
    },
    handleMenu(id) {
      this.$emit('problemInfoList', id);
      this.breadList = [];
      this.getTreeNode(this.$refs.elTree.getNode(id));
      this.$nextTick(() => {
        this.$emit('handleCrumbs', this.breadList);
      });
    },
    handleSetCurrentKey(key) {
      this.$nextTick(() => {
        this.$refs.elTree.setCurrentKey(key);
      });
    },
    getTreeNode(node) {
      // 获取当前树节点和其父级节点
      if (node) {
        if (node.label !== undefined) {
          this.breadList.unshift({
            name: node.label,
            id: node.key,
            isLeaf: node.isLeaf
          });
          this.getTreeNode(node.parent); // 递归
        }
      }
    },
    allowDrop(draggingNode, dropNode, type) {
      if (draggingNode.level !== dropNode.level) {
        return;
      }
      if (type === 'inner') {
        return;
      }
      return type !== 'inner';
    },
    nodeDrop(before, after) {
      const currentId = before.data.id;
      const currentSort = before.data.sort;
      const afterId = after.data.id;
      const afterSort = after.data.sort;
      const params = [
        {
          id: currentId,
          sort: afterSort
        },
        {
          id: afterId,
          sort: currentSort
        }
      ];
      this.$http.post('/question/branch/updateBatchQuestionBranchById.do', params, { json: true }).then(res => {
        if (res.ok) {
          this.$message.success('移动成功');
          this.getProblemTreeList();
          this.$emit('dialogVisible', false);
        } else {
          this.$message.error('移动失败');
          this.$emit('dialogVisible', false);
        }
      });
    }
  }
};
</script>

<style lang = 'scss' scoped>
::v-deep .el-tree {
  border-radius: 9px;
  overflow: hidden;
  padding: 10px;
  .el-tree-node__content {
    height: auto;
    padding-top: 6px;
    padding-bottom: 6px;
  }
}
::v-deep .custom-tree-node {
  display: flex;
  /* align-items: center; */
  justify-content: space-between;
  width: calc(100% - 24px);
  .label {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 90%;

    /* overflow: hidden;
      white-space: normal;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical; */
  }
}
.pageClass {
  float: right;
  margin-top: -15px;
  margin-bottom: 35px;
}
.dialog {
  div {
    width: 100px;
    height: 25px;
    line-height: 25px;
    border: 1px solid #ccc;
    cursor: pointer;
  }
}
</style>
