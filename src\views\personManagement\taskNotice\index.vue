<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref="searchForm"
      size="mini"
      label-width="130px"
      class="yz-search-form"
      :model="form"
      @submit.native.prevent="search"
    >
      <el-form-item label="名称" prop="sysRemindName">
        <el-input v-model="form.sysRemindName" placeholder="输入名称" />
      </el-form-item>
      <el-form-item label="创建人" prop="createTmpName">
        <el-input v-model="form.createTmpName" placeholder="输入创建人" />
      </el-form-item>
      <el-form-item label="启用状态" prop="isStart">
        <el-select v-model="form.isStart" clearable placeholder="请选择" filterable>
          <el-option label="否" value="0" />
          <el-option label="是" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="sysRemindType">
        <el-select v-model="form.sysRemindType" clearable placeholder="请选择" filterable>
          <el-option label="通知" value="1" />
          <el-option label="任务" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="录入类型" prop="inputType">
        <el-select v-model="form.inputType" clearable placeholder="请选择" filterable>
          <el-option label="系统生成" value="1" />
          <el-option label="人工录入" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="timeValue">
        <el-date-picker
          v-model="timeValue"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeTime"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
      </div>
    </el-form>
    <!-- 按钮区 -->
    <div class="yz-table-btnbox">
      <el-button
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="addRemind"
      >添加</el-button>
      <el-button
        icon="el-icon-delete"
        type="danger"
        size="small"
        @click="showRemove"
      >删除</el-button>
    </div>
    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="50" />
      <el-table-column
        prop="sysRemindName"
        label="名称"
        width="220"
        align="center"
      />
      <el-table-column
        prop="sysRemindType"
        label="类型"
        width="80"
        align="center"
      >
        <template v-slot="scope">
          <span v-if="scope.row.sysRemindType == '1'">通知</span>
          <span v-if="scope.row.sysRemindType == '2'">任务</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="sysRemindSketch"
        label="简述"
        align="center"
      />
      <el-table-column
        label="跟进人完成情况"
        width="120"
        align="center"
      >
        <template v-slot="scope">
          <div v-if="(scope.row.employeeFinishCount + scope.row.employeeNotFinishCount ) > 0">
            <p>总数: {{ scope.row.employeeFinishCount + scope.row.employeeNotFinishCount }}</p>
            <p>已完成: {{ scope.row.employeeFinishCount }}</p>
            <p>未完成: {{ scope.row.employeeNotFinishCount }}</p>
            <el-button
              size="small"
              type="primary"
              @click="checkDetails(scope.row, 0)"
            >详情</el-button>
          </div>
          <div v-else>
            <p>总数: {{ scope.row.followerFinishCount + scope.row.followerNotFinishCount }}</p>
            <p>已完成: {{ scope.row.followerFinishCount }}</p>
            <p>未完成: {{ scope.row.followerNotFinishCount }}</p>
            <el-button
              size="small"
              type="primary"
              @click="checkDetails(scope.row, 1)"
            >详情</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="辅导员完成情况"
        width="120"
        align="center"
      >
        <template v-slot="scope">
          <div>
            <p>总数: {{ scope.row.tutorFinishCount + scope.row.tutorNotFinishCount }}</p>
            <p>已完成: {{ scope.row.tutorFinishCount }}</p>
            <p>未完成: {{ scope.row.tutorNotFinishCount }}</p>
            <el-button
              size="small"
              type="primary"
              @click="checkDetails(scope.row, 2)"
            >详情</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="协作人完成情况"
        width="120"
        align="center"
      >
        <template v-slot="scope">
          <div>
            <p>总数: {{ scope.row.coordinatorFinishCount + scope.row.coordinatorNotFinishCount }}</p>
            <p>已完成: {{ scope.row.coordinatorFinishCount }}</p>
            <p>未完成: {{ scope.row.coordinatorNotFinishCount }}</p>
            <el-button
              size="small"
              type="primary"
              @click="checkDetails(scope.row, 3)"
            >详情</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="endTime"
        label="截止时间"
        align="center"
        width="135"
      >
        <template v-slot="scope">
          {{ scope.row.endTime ? scope.row.endTime : '无' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createEmpName"
        label="创建人"
        align="center"
        width="80"
      />
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="135"
      />
      <el-table-column
        prop="isStart"
        label="是否启用"
        align="center"
        width="80"
      >
        <template v-slot="scope">
          <el-tag
            v-if="scope.row.isStart == '0'"
            class="tag"
            type="info"
          >否</el-tag>
          <el-tag
            v-if="scope.row.isStart == '1'"
            class="tag"
          >是</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="230">
        <template v-slot="scope">
          <el-button
            v-if="scope.row.isStart == '0'"
            size="small"
            type="primary"
            :disabled="scope.row.disabled"
            @click="showConfirm(scope.row.id,'1')"
          >启用</el-button>
          <el-button
            v-else
            size="small"
            type="primary"
            :disabled="scope.row.disabled"
            @click="showConfirm(scope.row.id,'0')"
          >禁用</el-button>
          <el-button
            v-if="!scope.row.isSend && !scope.row.disabled"
            size="small"
            type="primary"
            @click="editRemind(scope.row)"
          >编辑</el-button>
          <el-button
            v-else
            size="small"
            type="primary"
            @click="checkRemind(scope.row)"
          >查看</el-button>
          <!-- 本人的记录 -->
          <template v-if="!scope.row.disabled">
            <el-button
              v-if="scope.row.isStart == '1' || scope.row.isSend"
              size="small"
              type="primary"
              :disabled="scope.row.disabled || scope.row.isSend"
              @click="showConfirm(scope.row.id)"
            >发送</el-button>
          </template>
          <!-- 非本人的记录 -->
          <template v-else>
            <el-button
              v-if="scope.row.isSend"
              size="small"
              type="primary"
              :disabled="scope.row.disabled || !scope.row.isSend"
              @click="sendRemind(scope.row)"
            >发送</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 确认状态弹窗 -->
    <confirm-dialog :visible.sync="confirmShow" :remindId="remindId" :tipsTitle="tipsTitle" @updateRemindStatus="updateRemindStatus" @sendRemind="sendRemind" @removeRemind="removeRemind" />
    <!-- 完成情况详情 -->
    <complete-situation :visible.sync="situationShow" :remindId="remindId" :isSelf="isSelf" :type="type" />
    <!-- 新增/查看/编辑 任务详情 -->
    <remind-details :visible.sync="remindShow" :remindId="remindId" :optionsType="optionsType" @getTableList="getTableList" @changeRemindId="changeRemindId" />
  </div>
</template>

<script>
import moment from 'moment';
import { getCookie } from 'tiny-cookie';
import confirmDialog from './indexDialog/confirmDialog.vue';
import completeSituation from './components/completeSituation.vue';
import remindDetails from './components/remindDetails.vue';

const form = {
  sysRemindName: '', // 名称
  createTmpName: '', // 创建人
  isStart: '', // 启用状态
  sysRemindType: '', // 类型
  inputType: '2',
  startTime: '',
  endTime: ''
};
export default {
  components: {
    confirmDialog,
    completeSituation,
    remindDetails
  },
  data() {
    return {
      timeValue: [],
      empId: '',
      isSelf: false,
      form: form,
      tableData: [],
      tableLoading: false,
      confirmShow: false,
      situationShow: false,
      remindShow: false,
      tipsTitle: '',
      optionsType: '',
      remindId: 0,
      status: '',
      selectRemindList: [],
      ids: '', // 删除时使用的id，用逗号隔开
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      type: 1
    };
  },
  created() {
    this.empId = getCookie('COOKIE_USER_EMP');
    this.getDefaultTime();
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    getDefaultTime() {
      const end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1);
      const start = new Date();
      start.setTime(end.getTime() - (3600 * 1000 * 24 * 7 - 1000));
      this.timeValue = [start, end];
      this.form.startTime = this.formatDate(Date.parse(start), 'yyyy-MM-dd');
      this.form.endTime = this.formatDate(Date.parse(end), 'yyyy-MM-dd');
    },
    // 时间戳转换方法    date:时间戳数字
    formatDate(item) {
      var date = new Date(item);
      var YY = date.getFullYear();
      var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);
      var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
      var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours());
      var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes());
      var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
      return YY + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss;
    },
    changeTime(e) {
      if (e) {
        this.form.startTime = e[0];
        this.form.endTime = e[1];
      } else {
        this.form.startTime = '';
        this.form.endTime = '';
      }
    },
    search(type) {
      if (type === 0) {
        this.timeValue = [];
        this.form.startTime = '';
        this.form.endTime = '';
        this.form = {};
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    handleQUeryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQUeryParams();
      this.$post('getSysRemindList', params).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.tableData.forEach(item => {
            item.createTime = moment(item.createTime).format('YYYY-MM-DD HH:mm:ss');
            if (item.endTime) {
              item.endTime = moment(item.endTime).format('YYYY-MM-DD HH:mm:ss');
            }
            this.currentDate = moment().format('HH:mm');
            // 判断是否是自己创建的任务
            if (item.createEmpId === this.empId) {
              item.disabled = false;
            } else {
              item.disabled = true;
            }
            // 判断是否已发送
            if (item.isSend === 1) {
              item.isSend = true;
            } else {
              item.isSend = false;
            }
          });
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    showConfirm(remindId, status) {
      this.remindId = remindId;
      this.status = status;
      if (status === '1') {
        this.tipsTitle = '确认要启用吗？';
      } else if (status === '0') {
        this.tipsTitle = '确认要禁用吗？';
      } else {
        this.tipsTitle = '确认要发送吗？';
      }
      this.confirmShow = true;
    },
    showRemove() {
      if (this.ids.length > 0) {
        this.tipsTitle = '确认要删除选中的提醒吗？';
        this.confirmShow = true;
      } else {
        this.$message({
          message: '请选择需要删除的提醒',
          type: 'warning'
        });
      }
    },
    removeRemind() {
      const loading = this.$loading();
      this.$post('removeRemind', { ids: this.ids }).then((res) => {
        const { fail, body } = res;
        loading.close();
        if (!fail) {
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    updateRemindStatus() {
      const loading = this.$loading();
      const datas = {
        remindId: this.remindId,
        status: this.status
      };
      this.$post('updateRemindStatus', datas).then((res) => {
        const { fail, body } = res;
        loading.close();
        if (!fail) {
          if (this.status === '0') {
            this.$message({
              message: '禁用成功',
              type: 'success'
            });
          } else {
            this.$message({
              message: '启用成功',
              type: 'success'
            });
          }
          this.getTableList();
        }
      });
    },
    checkDetails(row, type) {
      this.type = type;
      this.remindId = row.id;
      this.isSelf = !row.disabled;
      this.situationShow = true;
    },
    checkRemind(row) {
      this.optionsType = '查看';
      this.remindId = row.id;
      this.remindShow = true;
    },
    sendRemind() {
      const loading = this.$loading();
      this.$post('sendRemind', { remindId: this.remindId }).then((res) => {
        const { fail, body } = res;
        loading.close();
        if (!fail) {
          this.$message({
            message: '发送成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    editRemind(row) {
      this.optionsType = '编辑';
      this.remindId = row.id;
      this.remindShow = true;
    },
    addRemind(row) {
      this.optionsType = '添加';
      this.remindId = 0;
      this.remindShow = true;
    },
    changeRemindId(remindeId) {
      this.remindId = Number(remindeId);
    },
    handleSelectionChange(val) {
      const rowData = val;
      const selectRemindList = [];
      this.ids = '';
      rowData.forEach((item) => {
        selectRemindList.push(item.id);
      });
      for (let i = 0; i < selectRemindList.length; i++) {
        if (i === 0) {
          this.ids = selectRemindList[i].toString();
        } else {
          this.ids = this.ids + ',' + selectRemindList[i].toString();
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped></style>
