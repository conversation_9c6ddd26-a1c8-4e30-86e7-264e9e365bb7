<template>
  <section class="page-box">
    <img style="width: 100%" src="@/assets/imgs/annex/paper-classdata-top-bg.png" />
    <div v-if="!mIsLogin">
      <el-empty description="请前往远智学堂登录" />
    </div>
    <div v-else v-loading="mLoading" class="yz-base-container">
      <div class="con-grid">
        <el-tabs v-model="mActiveName" @tab-click="handleClick">
          <el-tab-pane label="课程资料" :name="DTab.COURSE" />
          <el-tab-pane label="考试复习资料" :name="DTab.EXAM" />
        </el-tabs>
        <div class="change-grid">
          <div
            v-if="mBtnList.length > 0 || mCrumbArr.length > 1 || CK_QRZ_NotTop"
            :class="['header-container', mFileList.length > 0 ? '':'bg']"
          >
            <!-- 按钮-tab -->
            <div v-show="mCrumbArr.length <= 1" class="btn-tabs">
              <div
                v-for="{ id, name } in mBtnList"
                :key="id"
                class="btn"
                :class="{ ac: mSubTabSelectType === id }"
                @click="handleBtnSel(id)"
              >
                {{ name }}
              </div>
            </div>

            <!-- 面包屑 -->
            <div v-show="mCrumbArr.length > 1" class="crumb">
              <span
                v-for="(item, index) in mCrumbArr"
                :key="index"
                :class="[index === mCrumbArr.length - 1 ? 'cru-ac' : 'cru-ac2']"
                @click="handleCrumbClick(item, index)"
              >
                {{ item.name }}
                <span v-show="index < mCrumbArr.length - 1"> > </span>
              </span>
            </div>

            <!-- 学期选择 -->
            <div v-if="CK_QRZ_NotTop" class="select-grid">
              <el-select
                v-model="mTerm"
                placeholder="请选择"
                style="width: 100px"
                size="small"
                @change="getList()"
              >
                <el-option
                  v-for="item in mOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>

          <div class="grid-down">
            <div v-if="!mFolderList.length && !mFileList.length" class="no-data-b">
              <img src="@/assets/imgs/annex/paper-no-data.png" />
              <p>暂无学习资料</p>
            </div>

            <!-- 文件夹列表 -->
            <div v-else-if="mFolderList.length">
              <div class="d-b" :class="{ 't-c-pd': mFolderList.length }">
                <div class="dl-box">
                  <dl v-for="item in mFolderList" :key="item.id" @dblclick="handleSingleDbClick(item)">
                    <dt v-if="mServiceExpire">
                      <!-- 文件夹-下载 -->
                      <span class="icon-d-b" @click="handleFolderDownload(item)">
                        <img src="@/assets/imgs/icon/icon-dl.png" />
                      </span>
                    </dt>
                    <dd class="dd-m">
                      <img src="@/assets/imgs/icon-file.png" />
                    </dd>
                    <dd class="txt">
                      <el-tooltip :content="item.resourceName" :disabled="item.resourceName.length <= 10">
                        <span class="txt-elp">{{ item.resourceName }}</span>
                      </el-tooltip>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <!-- 文件列表 -->
            <el-table
              v-else-if="mFileList.length"
              :data="mFileList"
              height="100%"
              style="width: 100%;"
              header-cell-class-name="table-header"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="45" align="center" />
              <el-table-column prop="resourceName" label="文件名称" show-overflow-tooltip />
              <el-table-column v-if="!gkHideTime" prop="createTime" label="上传时间" width="160" align="center" />
              <el-table-column prop="resourceSize" label="文件大小" width="100" align="center" />
              <el-table-column v-if="mServiceExpire" width="120" align="center">
                <template #default="{row}">
                  <el-button type="text" size="small" @click="openFile(row)">预览</el-button>
                  <el-button type="text" size="small" @click="singleFileDownload(row)">下载</el-button>
                  <!-- 最新更新-标识 -->
                  <img v-if="row.ifShowNew == 1" class="flag-new" src="@/assets/imgs/icon/icon-new.png" />
                </template>

                <template #header>
                  <el-button class="btn-batch" size="small" type="danger" @click="handleFileDownloadAll">
                    批量下载
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

    </div>

    <!-- 文件下载进度 -->
    <el-drawer
      size="40%"
      :visible.sync="mDrawerSta"
      :before-close="()=> mDrawerSta = false"
    >
      <template #title>
        <div>
          文件下载状态
          <el-progress :percentage="totalPercentage" />
        </div>
      </template>
      <ul class="drawer-c">
        <li v-for="(item, index) in mGetDfs" :key="index">
          <span>
            {{ item.resourceName || '-' }}
          </span>
          <span>
            <el-progress :percentage="item.percentage" :status="item.pcStatus" />
          </span>
        </li>
      </ul>
    </el-drawer>

  </section>
</template>

<script>
import { downOssUri, ossUri } from '@/config/request';
import JSZip from 'jszip';
import FileSaver from 'file-saver';
import axios from 'axios';
import moment from 'moment';
import { Base64 } from 'js-base64';

/**
 * 课程类型
 * 成考1 国开2 全日制3 自考4 研究生5 中专6
 * -------不在此例-------
 * 学历教育
 * 职业教育6 海外教育7
 */
const DRecruitType = { CK: '1', GK: '2', QRZ: '3', ZK: '4', YJS: '5', ZZ: '6' };

/**
 * tab 类型
 * COURSE 课程资料，EXAM 考试复习资料
 */
const DTab = { COURSE: '1', EXAM: '2' };

/** 按钮 id */
const DBtnId = {
  RULE: '1', // 常规课程
  SC: '2', // 自学中心
  ALL: 'All',
  RU: 'Recent Updates'
};

export default {
  data() {
    return {
      DTab,
      DRecruitType,
      mLoading: false,
      mCrumbArr: [], // 面包屑
      mGetDfs: [], // 需要下载的文件数据
      mDrawerSta: false,
      mIsLogin: false,
      mSelectFileAllSta: false,
      mFolderList: [], // 文件夹列表
      mFileList: [], // 文件列表
      mRecruitType: this.$route.query.recruitType || DRecruitType.GK,
      mLearnId: this.$route.query.learnId || '164662208609836456', // 学业id
      mId: '', // 资源文件id(首层文件夹资源文件id就按learnId传递就行)
      mSubTabSelectType: 1, // 自考学籍一级目录必须传（自考课程类型 1: 常规课 2: 自学中心课程）
      mTerm: Number(this.$route.query.term) || '', // 学期（成教全日制的学期选择时的参数），否则不用传
      mActiveName: DTab.COURSE, // 成教全日制必传（每一级目录都需要传该参数）：资料类型 1：课程资料 2：考试复习
      mOptions: [
        { label: '第一学期', value: 1 },
        { label: '第二学期', value: 2 },
        { label: '第三学期', value: 3 },
        { label: '第四学期', value: 4 },
        { label: '第五学期', value: 5 },
        { label: '第六学期', value: 6 }
      ],
      mIsTop: '0', // 0 文件夹，1 文件
      isEnableAuth: 0,
      multipleSelection: []
    };
  },
  computed: {
    // 有效期
    mServiceExpire() {
      return this.$route.query.serviceExpire !== 'Y'; // Y已过期 N未过期
    },
    // 成考或全日制
    CK_QRZ_NotTop() {
      return [this.mRecruitType.CK, this.mRecruitType.QRZ].includes(this.mRecruitType) && this.mIsTop != '1';
    },
    // 按钮列表
    mBtnList() {
      let list = [];
      switch (this.mRecruitType) {
        case this.DRecruitType.ZK:
          list.push({ id: DBtnId.RULE, name: '常规课程' }, { id: DBtnId.SC, name: '自学中心' });
          break;
        default:
          list.push({ id: DBtnId.ALL, name: '全部' });
      }

      if (this.DRecruitType.YJS == this.mRecruitType && this.mActiveName == DTab.EXAM) {
        // 研究生类型，在考试复习资料中不显示按钮
        list = [];
      } else if (!(this.DRecruitType.ZK == this.mRecruitType && this.mActiveName == DTab.EXAM)) {
        // 除了自考类型，其他类型在考试复习资料中都显示最近更新
        list.push({ id: DBtnId.RU, name: '最近更新' });
      }

      return list;
    },
    // 国开的考试复习资料隐藏上传时间
    gkHideTime() {
      return this.mActiveName == DTab.EXAM && this.mRecruitType == this.DRecruitType.GK;
    },
    // 下载总进度
    totalPercentage() {
      return parseInt(this.mGetDfs.reduce((total, item) => item.pcStatus == 'exception'
        ? total : total + (item.percentage || 0), 0) / (this.mGetDfs.length || 1));
    }
  },
  async created() {
    // 按钮默认值
    this.mSubTabSelectType = this.mRecruitType === this.DRecruitType.ZK ? DBtnId.RULE : DBtnId.ALL;

    this.resetCrumb();
    this.getIsEnableAuth();
    this.getLoginSta();
  },
  methods: {
    // 写入操作日志
    setPostLog(mappingId) {
      // businessType  业务类型: 1：课程咨料 2：复习咨料
      // eventType  事件类型 1：下载
      // learnId 学业id
      // mappingId 资源id (批量下载可以传多个 并用 , 隔开)
      this.$http.get(`/usDataRecord?businessType=${this.mActiveName}&eventType=1&learnId=${this.mLearnId}&mappingId=${mappingId}`);
    },
    // 移除最新的标记
    removeMark(idList) {
      this.$http.post('/courseResources/removeMark', { idList, learnId: this.mLearnId }, { json: true });
    },
    // 获取登录状态
    async getLoginSta() {
      const { body, code } = await this.$http.get('/courseResources/isLogin');
      if (code === '00') {
        this.mIsLogin = body.data;
        if (this.mIsLogin) {
          this.getList();
        }
      }
    },
    // 获取最近更新列表数据
    getRUList() {
      this.mFolderList = [];
      this.mFileList = [];
      const params = { learnId: this.mLearnId, term: this.mTerm, resourceType: this.mActiveName };
      this.$http.post('/courseResources/recentList', params, { json: true })// http://yapi.yzwill.cn/mock/46/xx
        .then(res => {
          if (res.code === '00') {
            this.mIsTop = '0';// 展示学期选择框
            this.mFileList = (res.body ?? []).map(v => ({ ...v, status: false }));
          }
        }).finally(() => {
          this.mLoading = false;
        });
    },
    // 获取页面数据
    getList(item = {}) {
      this.mLoading = true;
      this.mFolderList = [];
      this.mFileList = [];
      // 最近更新数据
      if (this.mSubTabSelectType === DBtnId.RU) {
        this.getRUList();
        return;
      }
      const params = {
        learnId: this.mLearnId,
        id: item.id ? item.id : this.mLearnId,
        currentLevel: item.currentLevel ? item.currentLevel + 1 : 1,
        courseType: this.mRecruitType == this.DRecruitType.ZK ? this.mSubTabSelectType : '',
        term: this.mTerm,
        resourceType: this.mActiveName
      };
      this.$http
        .post('/courseResources/selCourseResource.do', params, { json: true })
        .then(res => {
          if (res.code === '00') {
            this.mIsTop = '0';
            if (res.body) {
              this.mIsTop = res.body[0]?.ifTop;
              res.body.forEach(item => {
                if (item.resourceType == 1) {
                  this.mFolderList.push(item);
                } else {
                  this.mFileList.push(item);
                }
              });
            }
          }
        }).finally(() => {
          this.mLoading = false;
        });
    },
    // 面包屑重置
    resetCrumb() {
      this.mCrumbArr = this.mBtnList.filter(v => this.mSubTabSelectType == v.id);
    },
    // 资料类型-顶部 tab 点击-重新请求数据
    handleClick() {
      // 重置全选状态
      this.mSelectFileAllSta = false;
      // 自考和研究生类型，在考试复习资料中不显示最近更新，默认显示第一个按钮数据
      if (([this.DRecruitType.ZK, this.DRecruitType.YJS].includes(this.mRecruitType) && this.mActiveName == DTab.EXAM)) {
        this.mSubTabSelectType = this.mBtnList[0]?.id || DBtnId.ALL;
      }
      this.resetCrumb();
      this.getList();
    },
    // 按钮类型-切换
    handleBtnSel(id) {
      if (this.mSubTabSelectType === id) return;
      this.mSubTabSelectType = id;
      this.resetCrumb();
      this.getList();
    },
    // 所有文件-选中状态-切换
    handleSelectAllFiles() {
      this.mSelectFileAllSta = !this.mSelectFileAllSta;
      this.mFileList.forEach(item => {
        item.status = this.mSelectFileAllSta;
      });
    },
    // 双击-文件夹-进入下一级
    handleSingleDbClick(item) {
      if (item.ifTop === 1) return;
      this.getList(item);
      this.mCrumbArr.push({ name: item.resourceName, ...item });
    },
    // 面包屑-点击事件-返回某一级
    handleCrumbClick(item, index) {
      // 最后一个面包屑，即当前路径，点击不处理
      if (index === this.mCrumbArr.length - 1) return;

      // 重置全选状态
      this.mSelectFileAllSta = false;
      if (index === 0) {
        // 一级目录
        this.resetCrumb();
        this.getList();
      } else {
        // 非一级目录
        this.mCrumbArr = this.mCrumbArr.slice(0, index + 1);
        this.getList(item);
      }
    },
    // 获取下载文件地址类型
    getIsEnableAuth() {
      this.$http.post('/courseResources/isEnableAuth').then(res => {
        if (res.code === '00') {
          this.isEnablesAuth = res.body;
        }
      });
    },
    // 文件选中状态-切换
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 递归处理需要下载的数据
    getNeedDownloadData(data) {
      data.length && data.forEach(item => {
        if (item.resourceUrl) {
          this.mGetDfs.push({
            resourceName: item.resourceName,
            resourceUrl: item.resourceUrl,
            id: item.id,
            percentage: 0,
            pcStatus: undefined,
            sensResourceUrl: item.sensResourceUrl
          });
        }
        item.resourceList && this.getNeedDownloadData(item.resourceList);
      });
    },
    // 文件下载弹窗提示
    downloadTip() {
      return this.$confirm(
        '资料文件较大，下载结果可在【浏览器-下载内容】里查看',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );
    },
    // 文件夹下载
    async handleFolderDownload({ id, currentLevel, resourceName }) {
      const params = { id, currentLevel, learnId: this.mLearnId, resourceType: this.mActiveName };
      const { code, body } = await this.$http.get('/courseResources/batchDownload', { params });
      if (code == '00') {
        if (body.length) {
          const size = body[0].resourceSizeCompute / 1024 / 1024 > 500;
          if (size) {
            this.$message.error('一次性下载文件总大小不能超过500M！');
            return;
          }
          this.mGetDfs = [];
          this.getNeedDownloadData(body);

          this.downloadTip().then(() => {
            this.mDrawerSta = true;
            const name = this.mCrumbArr.filter(v => v.id != DBtnId.ALL).map(v => v.name).join(' ') + ' ' + resourceName;
            this.handleDownload(name);
          });
        }
      } else {
        this.$message.error('获取课程资源出错');
        return;
      }
    },
    // 选中文件-批量下载
    handleFileDownloadAll() {
      if (this.multipleSelection.length) {
        this.mLoading = true;
        this.mGetDfs = this.multipleSelection.map(v => ({ ...v, percentage: 0, pcStatus: undefined }));
        this.downloadTip().then(async() => {
          this.mDrawerSta = true;
          // 根据面包屑生成压缩包名称
          const zipName = this.mCrumbArr.filter(v => v.id != DBtnId.ALL).map(v => v.name).join(' ');
          await this.handleDownload(zipName);
        }).finally(() => {
          this.mLoading = false;
        });
      } else {
        this.$message.warning('请勾选要下载的文件资源');
      }
    },
    // 发起文件下载请求
    downloadFileRequest(row) {
      const url = this.isEnableAuth === '1' ? downOssUri + row.sensResourceUrl : ossUri + row.resourceUrl;
      return new Promise((resolve, reject) => {
        axios({
          url,
          method: 'get',
          responseType: 'blob',
          onDownloadProgress: e => {
            const { loaded, total } = e;
            if (total === 0) {
              row.percentage = 100;
            } else {
              const num = (loaded / total).toFixed(2) * 100;
              row.percentage = parseInt(num);
            }
            if (row.percentage == 100) {
              row.pcStatus = 'success';
            }
          }
        })
          .then(data => {
            resolve(data.data);
          })
          .catch(e => {
            row.percentage = 100;
            row.pcStatus = 'exception';
            reject(`【${row.resourceName}】下载失败`);
          });
      });
    },
    // 多个文件-打包下载文件
    handleDownload(name) {
      const zip = new JSZip();
      const ids = [];
      const promises = [];
      this.mGetDfs.forEach((item, index) => {
        // 遍历需要打包的文件
        const { resourceName, resourceUrl, resourceSize } = item;
        if (resourceUrl && (resourceSize != '0B')) {
          const promise = this.downloadFileRequest(item).then(data => { // 下载文件
            zip.file(`${index + 1}.` + resourceName, data, { binary: true }); // 逐个添加文件
            ids.push(item.id);
          }).catch(e => { console.error(e); });
          promises.push(promise);
        }
      });
      if (promises.length) {
        Promise.all(promises)
          .then(async() => {
            if (ids.length === 0) {
              this.$message.error(`下载任务创建失败，请稍后重试~`);
              return;
            }
            // 生成二进制流
            zip.generateAsync({ type: 'blob' }).then(async content => {
              // 自定义文件名
              const folderName = `【${name || '-'}】-${moment().format('YYYYMMDD')}.zip`;
              // 利用file-saver保存文件
              FileSaver.saveAs(content, folderName);
              if (ids.length === promises.length) {
                this.$message.success(`下载任务创建成功，下载结果可在【浏览器-下载内容】里查看`);
              } else {
                this.$message.success(`下载任务创建成功，存在部分文件下载失败，下载结果可在【浏览器-下载内容】里查看`);
              }
              this.setPostLog(ids);
              await this.removeMark(ids);
              if (this.mSubTabSelectType === DBtnId.RU) {
                this.getRUList();
              }
            });
          }).catch(() => {});
      } else {
        this.$message.error(`无文件可下载~`);
        return;
      }
    },
    // 触发文件下载
    downLoadFileByLink(uri, resourceName) {
      const url = window.URL.createObjectURL(uri);
      const a = document.createElement('a');
      a.href = url;
      a.target = '_blank';
      a.download = resourceName;
      a.click();
      a.remove();
    },
    // 单个文件下载
    singleFileDownload(row) {
      const { resourceName, resourceSize, id } = row;
      if (!resourceSize || resourceSize === '0B') {
        this.$message.error('文件大小有误！');
        return;
      }
      this.downloadFileRequest(row).then(data => {
        this.downLoadFileByLink(data, resourceName);
        this.removeMark([id]);
        this.setPostLog(id);
        this.$message.success(`下载任务创建成功，下载结果可在【浏览器-下载内容】里查看`);
        if (row.ifShowNew == 1) {
          this.getRUList();
        }
      }).catch(error => {
        this.$message.error(error);
      });
    },
    async openFile(row) {
      if (row.resourceUrl || row.sensResourceUrl) {
        const url = this.isEnableAuth === '1' ? downOssUri + row.sensResourceUrl : ossUri + row.resourceUrl;
        window.open('preview/onlinePreview?url=' + encodeURIComponent(Base64.encode(url)));
        await this.removeMark([row.id]);
        if (row.ifShowNew == 1) {
          this.getRUList();
        }
      }
    }
  }
};
</script>

<style scoped lang="scss">
@import './index.scss';
</style>
