/* Layout */
import Layout from '@/layout';
import helpStudypkRouters from './helpStudypk';
import classDataDownload from './classDataDownload';
import independentLandingPage from './independentLandingPage';
import messageConfiguration from './messageConfiguration';
/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

const routes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/home',
    children: [{
      path: 'home',
      name: 'home',
      component: () => import('@/views/home/<USER>'),
      meta: { title: '首页', icon: 'el-icon-s-home' }
    }]
  },
  ...helpStudypkRouters,
  ...classDataDownload,
  ...independentLandingPage,
  ...messageConfiguration,
  {
    path: '/graduation-thesis',
    component: Layout,
    redirect: '/graduation-thesis/index',
    children: [{
      path: 'index',
      name: '毕业论文及报告提交',
      component: () => import('@/views/graduation-thesis/index'),
      meta: { title: '毕业论文及报告提交', icon: 'el-icon-document' }
    }]
  },

  {
    path: '/editor',
    hidden: true,
    component: () => import('@/views/graduation-thesis/editor'),
    meta: { title: '写论文' }
  },
  {
    path: '/annex',
    component: Layout,
    // redirect: '/annex/index',
    meta: {
      title: '网报资料',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        // name: '附件资料',
        component: () => import('@/views/annex/index'),
        meta: { title: '附件资料', icon: 'el-icon-document' }
      },
      {
        path: 'mergeAnnex',
        name: '网报资料-合并',
        component: () => import('@/views/annex/mergeAnnex'),
        meta: { title: '网报资料-合并', icon: 'el-icon-document' }
      },
      {
        path: 'yzSchoolAnnex',
        name: '远智学堂-网报资料',
        component: () => import('@/views/annex/yzSchoolAnnex'),
        meta: { title: '远智学堂-附件资料', icon: 'el-icon-document' }
      }
    ]
    // component: () => import('@/views/annex/index'),
    // meta: { title: '附件资料', icon: 'el-icon-document' }
  },
  {
    path: '/personManagement',
    component: Layout,
    meta: {
      title: '人事管理',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: '/personManagement/taskNotice',
        component: () => import('@/views/personManagement/taskNotice/index'),
        meta: { title: '内部任务通知管理', icon: 'el-icon-document' }
      }
    ]
  },
  {
    path: '/studentService',
    component: Layout,
    meta: {
      title: '学员服务',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: '/assignTeacher',
        name: '分配老师',
        component: () => import('@/views/studentService/assignTeacher'),
        meta: { title: '分配老师', icon: 'el-icon-document' }
      }
    ]
  },
  {
    path: '/employeeKnowledgeBase',
    component: Layout,
    // redirect: '/annex/index',
    meta: {
      title: '员工知识库',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: 'classificationManagement',
        // name: '附件资料',
        component: () => import('@/views/employeeKnowledgeBase/classificationManagement'),
        meta: { title: '分类管理', icon: 'el-icon-document' }
      },
      {
        path: 'problemManagement',
        name: '问题管理',
        component: () => import('@/views/employeeKnowledgeBase/problemManagement'),
        meta: { title: '问题管理', icon: 'el-icon-document' }
      },
      {
        path: 'issueReview',
        name: '问题发布审核',
        component: () => import('@/views/employeeKnowledgeBase/issueReview'),
        meta: { title: '问题发布审核', icon: 'el-icon-document' }
      }
    ]
  },
  {
    path: '/topicConfiguration',
    component: Layout,
    // redirect: '/annex/index',
    meta: {
      title: '员工235知识库',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: 'zoneConfiguration',
        // name: '附件资料',
        component: () => import('@/views/topicConfiguration/zoneConfiguration'),
        meta: { title: '答疑专区配置', icon: 'el-icon-document' }
      },
      {
        path: 'Q&A-Data',
        name: '问题管理',
        component: () => import('@/views/topicConfiguration/Q&A-Data'),
        meta: { title: '答疑数据', icon: 'el-icon-document' }
      }
    ]
  },
  {
    path: '/guokai',
    component: Layout,
    meta: {
      title: '学员服务',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: '/attendClassType',
        name: '国开课程',
        component: () => import('@/views/guokai/attendClassType'),
        meta: { title: '国开课程', icon: 'el-icon-document' }
      },
      {
        path: '/yzSchoolCourseType',
        name: '远智学堂课程方式',
        component: () => import('@/views/guokai/yzSchoolCourseType'),
        meta: { title: '远智学堂课程方式', icon: 'el-icon-document' }
      }
    ]
  },

  {
    path: '/secEditor',
    hidden: true,
    component: () => import('@/views/graduation-thesis/secEditor'),
    meta: { title: '写论文' }
  },

  {
    path: '/verify',
    hidden: true,
    component: () => import('@/views/graduation-thesis/verify'),
    meta: { title: '批阅论文' }
  },

  {
    path: '/look-reviews',
    hidden: true,
    component: () => import('@/views/graduation-thesis/look-reviews'),
    meta: { title: '查看点评' }
  },

  {
    path: '/demo',
    hidden: true,
    component: () => import('@/views/demo'),
    meta: { title: '演示' }
  },
  // (新版)助学pk榜单
  {
    path: '/helpStudyRank/helpStudyPkRank/rankDetails',
    name: 'rankDetails',
    component: () => import('@/views/helpStudyRank2/helpStudyPkRank/rankDetails')
    // meta: { title: 'PK榜单数据详情' }
  },
  {
    path: '/cj/applyExamDemo',
    name: 'applyExamDemo',
    component: () => import('@/views/chengjiao/applyExamDemo'),
    meta: { title: '成教网报-demo', icon: 'el-icon-document' },
    hidden: true
  },
  {
    path: '/cj/applyExam',
    name: 'applyExam',
    component: () => import('@/views/chengjiao/applyExam'),
    meta: { title: '成教网报', icon: 'el-icon-document' },
    hidden: true
  },
  {
    path: '/cj/applyExam/iframe',
    name: 'applyExamIframe',
    component: () => import('@/views/chengjiao/iframeContainer'),
    meta: { title: '成教网报', icon: 'el-icon-document' },
    hidden: true
  },
  {
    path: '/finance',
    component: Layout,
    meta: {
      title: '财务管理',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: '/pay',
        name: '缴费项目',
        component: () => import('@/views/finance/pay'),
        meta: { title: '缴费项目', icon: 'el-icon-document' }
      }
    ]
  },
  {
    path: '/classification',
    component: Layout,
    meta: {
      title: '职业',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: '/career',
        name: '职业',
        component: () => import('@/views/classification/career'),
        meta: { title: '职业', icon: 'el-icon-document' }
      }
    ]
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true },
  {
    path: '/mall',
    component: Layout,
    meta: {
      title: '商城运营',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: '/goods-manage',
        name: 'goods-manage',
        component: () => import('@/views/mall/goods-manage'),
        meta: { title: '商品管理', icon: 'el-icon-document' }
      },
      {
        path: '/activity-link',
        name: 'activity-link',
        component: () => import('@/views/mall/activity-link'),
        meta: { title: '商城活动链接配置', icon: 'el-icon-document' }
      },
      {
        path: '/banner',
        name: 'banner',
        component: () => import('@/views/mall/banner'),
        meta: { title: '商城banner配置', icon: 'el-icon-document' }
      },
      {
        path: '/porcelain',
        name: 'porcelain',
        component: () => import('@/views/mall/porcelain'),
        meta: { title: '商城瓷片区配置', icon: 'el-icon-document' }
      },
      {
        path: '/corner-mark',
        name: 'corner-mark',
        component: () => import('@/views/mall/corner-mark'),
        meta: { title: '商品角标配置', icon: 'el-icon-document' }
      },
      {
        path: '/hot-search-words',
        name: 'hot-search-words',
        component: () => import('@/views/mall/hot-search-words'),
        meta: { title: '热门搜索词', icon: 'el-icon-document' }
      },
      {
        path: '/order-manage',
        name: 'order-manage',
        component: () => import('@/views/mall/order-manage'),
        meta: { title: '订单管理', icon: 'el-icon-document' }
      },
      {
        path: '/refund-manage',
        name: 'refund-manage',
        component: () => import('@/views/mall/refund-manage'),
        meta: { title: '退款管理', icon: 'el-icon-document' }
      },
      {
        path: '/mall/coupon-manage',
        name: 'coupon-manage',
        component: () => import('@/views/mall/coupon-manage'),
        meta: { title: '优惠券管理', icon: 'el-icon-document' }
      }
    ]
  }
];

export default routes;
