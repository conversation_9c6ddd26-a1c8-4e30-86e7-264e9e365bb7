<template>
  <div>
    <common-dialog
      is-full
      class="common-dialog"
      width="1000px"
      :title="optionsType"
      :visible.sync="show"
      @open="open"
      @close="close"
    >
      <div class="center">
        <el-row>
          <el-col :span="6"><span class="center_txt"><i>*</i> 问题分类：</span></el-col>
          <el-col :span="15"><span class="center_content">{{ nameTitel }}</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="6"><span class="center_txt"><i>*</i> 问题：</span></el-col>
          <el-col :span="15"><span class="center_content">{{ form.question }}</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="6"><span class="center_txt"><i>*</i> 是否启用：</span></el-col>
          <el-col :span="15"><span class="center_content">{{ form.auditStatus == '1'?'是':'否' }}</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="6"><span class="center_txt">搜索关键词：</span></el-col>
          <el-col :span="15"><span class="center_content">{{ form.searchKeyword }}</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="6"><span class="center_txt"><i>*</i> 回答：</span></el-col>
          <!-- <el-col :span="15"><span class="center_content">{{ infoList.answer }}</span></el-col> -->
          <el-col :span="15"><span class="center_content">
            <wang-editor ref="answer" v-model="form.answer" class="editor-color content" :content.sync="form.answer" />
          </span></el-col>
        </el-row>
      </div>
    </common-dialog>
  </div>
</template>

<script>
import wangEditor from '@/components/Editor/wang-editor';

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    optionsType: {
      type: String,
      default: ''
    },
    infoList: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      components: {
        wangEditor
      },
      show: false,
      form: {},
      nameTitel: ''
    };
  },
  watch: {
    visible(val) {
      this.show = val;
      if (val) {
        this.edit(this.infoList);
      }
    },
    infoList(val) {
      // console.log(val.crumbs, 'valcrumbs');
      // let str = '';
      // val.crumbs.forEach((item, index) => {
      //   str += item.name + '>';
      // });
      // this.nameTitel = str.substring(0, str.length - 1);
      // this.edit(val);
    }
  },
  created() {
    // this.edit(this.infoList);
  },
  methods: {
    edit(val) {
      const params = {
        questionId: val.questionId
      };
      this.$http.post('/question/questionDetail.do', params).then(async res => {
        if (res.ok) {
          console.log(res, 'res');
          if (this.$refs['answer']) {
            this.$refs['answer'].setContent(res.body.answer || '');
          }
          this.dataTime = [res.body.startDate, res.body.endDate];
          this.form = res.body;
          let str = '';
          if (res.body.crumbs) {
            const crumbs = JSON.parse(res.body.crumbs).reverse();
            crumbs.forEach(item => {
              str += item.name + '>';
            });
            this.nameTitel = str.substring(0, str.length - 1);
          }
        }
      });
    },
    open() {
      // this.$emit('dialogVisible', false);
    },
    close() {
      this.$emit('dialogVisible', false);
    }
  }
};
</script>

<style lang = "scss" scoped>
.center{
  i{
    color:red
  }
  .center_txt{
    float: right;
    height: 40px;
    line-height: 40px;
    /* text-align: right; */
  }
  .center_content{
    float: left;
    height: 40px;
    line-height: 40px;
    /* text-align:left ; */
  }
}
/* .editor-color {
  ::v-deep .w-e-text p,
  .w-e-text h1,
  .w-e-text h2,
  .w-e-text h3,
  .w-e-text h4,
  .w-e-text h5,
  .w-e-text table,
  .w-e-text pre {
    color: #606266;
  }
} */

::v-deep .w-e-text-container{
  height: auto !important;
}
</style>
