<template>
  <common-dialog
    :show-footer="true"
    width="1000px"
    title="资料审核"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='form'
        size='mini'
        :model='form'
        :rules="rules"
        label-width='140px'
      >
        <el-form-item label='文档名称' prop='attachmentName'>
          <el-input v-model="form.attachmentName" readonly />
        </el-form-item>

        <el-form-item label='审核状态' prop='checkStatus'>
          <el-select v-model="form.checkStatus" disabled>
            <el-option label="撤销审核" value="0" />
            <el-option label="通过审核" value="1" />
            <el-option label="不通过审核" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label='点评内容' prop="commentContent">
          <wang-editor ref="wangEditor" v-model="form.commentContent" />
        </el-form-item>

        <el-form-item label='上传文件'>
          <el-upload
            ref="upload"
            class="upload-demo"
            :action="action"
            :data="uploadFileParams"
            :before-upload="beforeUpload"
            :on-success="uploadSuccess"
            :file-list="fileList"
            :limit="1"
            name="file"
            :auto-upload="false"
          >
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传到服务器</el-button>
            <div slot="tip" class="el-upload__tip">只能上传文档格式文件</div>
          </el-upload>
        </el-form-item>

        <el-form-item>
          <el-alert
            title="学员已关注对应学籍公众号的情况下将会收到审核结果推送"
            type="info"
            show-icon
            :closable="false"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    },
    // 附件id
    annex: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    const validateContent = (rule, value, callback) => {
      if (this.$refs['wangEditor']) {
        const content = this.$refs['wangEditor'].getContent('text').replace(/&nbsp;/ig, '');
        if (content.trim() === '') {
          callback(new Error('请填写点评内容！'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      show: false,
      action: '',
      uploadFileParams: {},
      fileList: [],
      form: {
        attachmentName: '',
        checkStatus: '',
        commentContent: ''
      },
      rules: {
        attachmentName: [
          { required: true, message: '请输入', trigger: 'change' }
        ],
        checkStatus: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        commentContent: [
          { required: true, validator: validateContent, trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      this.getBasicInfo();
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const params = {
            attachmentId: this.annex.attachmentId,
            learnId: this.row.learnId,
            ...this.form,
            file: '',
            attrSeq: this.annex.attrSeq
          };
          this.$post('updateAttachment', params)
            .then(res => {
              const { fail, body } = res;
              if (!fail) {
                this.$parent.getTableList();
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.show = false;
              }
            });
        }
      });
    },
    getBasicInfo() {
      const params = {
        attachmentId: this.annex.attachmentId,
        checkStatus: this.annex.checkStatus
      };
      this.$post('paperAttachmentNew', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.attachmentName = body.attachments.attachmentName;
            this.form.checkStatus = this.type;
            if (this.type !== '0') {
              const isPass = this.type === '1' ? 'pass' : 'faile';
              body.commentContent.find(item => {
                if (item.attrName === isPass) {
                  this.form.commentContent = item.attrValue;
                  this.$refs['wangEditor'].setContent(this.form.commentContent);
                }
              });
            }
          }
        });
    },
    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        this.$nextTick(() => {
          const url = '/graduatePaper/webuploader.do';
          this.action = url + '?learnId=' + this.row.learnId + '&attachmentId=' + this.annex.attachmentId;
          resolve();
        });
      });
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    uploadSuccess(response, file, fileList) {
      this.$message({
        message: '附件上传成功',
        type: 'success'
      });
    },
    close() {
      // Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
