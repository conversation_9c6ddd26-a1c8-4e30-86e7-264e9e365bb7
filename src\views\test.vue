<template>
  <el-select
    ref="select"
    v-model="val"
    v-loadmore="loadMore"
    :remote-method="handleQuery"
    filterable
    remote
    clearable
    placeholder="请选择"
    @change="handleChange"
    @clear="handleClear"
    @visible-change="visibleChange"
  >
    <slot :option="option">
      <el-option
        v-for="(item,index) in option"
        :key="index"
        :label="item[props.label]"
        :value="item[props.value]"
      />
    </slot>
  </el-select>
</template>

<script>
export default {
  props: {
    value: {
      type: [String, Number, Boolean],
      default: null
    },
    // 请求的接口路径
    url: {
      type: String,
      default: ''
    },
    props: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          value: 'value',
          query: 'query'
        };
      }
    },
    // 请求附带的参数
    data: {
      type: Object,
      default: () => {}
    },
    // 请求方式
    method: {
      type: String,
      default: 'get'
    }
  },
  data() {
    return {
      val: null,
      option: [],
      query: '',
      // 分页
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    value(val) {
      this.val = val;
    }
  },
  mounted() {
    console.log(this.$slots);
  },
  methods: {
    // 重置，初始化
    reset() {
      this.query = '';
      this.option = [];
      this.pagination.page = 1;
      this.pagination.total = 0;
      this.getOption();
    },
    setDefault(value) {
      this.$refs['select'].selectedLabel = value;
    },
    // 每次展开重置一边数据
    visibleChange(status) {
      if (status) {
        this.reset();
      }
    },
    handleChange(value) {
      let obj = {};
      obj = this.option.find((item) => {
        return item[this.props.value] === value;
      });
      this.$emit('input', value);
      this.$emit('change', { value, label: obj[this.props.label], source: obj });
    },
    // 处理用户点击清空按钮时触发，清除选中的数据我们需要重置数据
    handleClear() {
      this.reset();
    },
    // 搜索
    handleQuery(query) {
      // 搜索前，重置数据
      this.option = [];
      this.pagination.page = 1;
      this.pagination.total = 0;
      this.query = query;
      this.getOption();
    },
    // 请求数据
    getOption() {
      const params = {
        [this.props.query]: this.query,
        ...this.data,
        page: this.pagination.page,
        rows: this.pagination.limit
      };

      // 根据不同请求方式，axios附带的参数名需要做处理
      const fieldName = this.method === 'get' ? 'params' : 'data';

      this.$http({
        method: this.method,
        url: this.url,
        [fieldName]: params
      })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.option = this.option.concat(body.data);
            this.pagination.total = body.recordsTotal; // 总条数
          }
        });
    },
    // 加载更多，滚动到底部自动触发此函数
    loadMore() {
      // 判断接口返回的总数，如果和现在的 option.length 相等说明 数据已经请求 完了 无效继续发起请求
      if (this.pagination.total === this.option.length) {
        return;
      }
      this.pagination.page += 1;
      this.getOption();
    }
  }
};
</script>
