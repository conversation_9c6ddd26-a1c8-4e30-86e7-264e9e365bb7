<template>
  <common-dialog
    :show-footer="true"
    is-full
    width="700px"
    :title="title+'上课方式'"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="yz-base-container">
      <el-steps direction="vertical" :active="4">
        <el-step title="上课方式名称">
          <template slot="icon">
            <i class="yz-icon-computer"></i>
          </template>
          <template slot="description">
            <el-form ref="form1" size='mini' :model="form1" :rules="basicRules" label-width="182px">
              <el-form-item label="后台上课方式名称：" prop="adminTeachTypeName">
                <el-input v-model="form1.adminTeachTypeName" placeholder="请输入上课方式名称" maxlength="20" />
              </el-form-item>
              <el-form-item label="学员端上课方式名称：" prop="clientTeachTypeName">
                <el-input v-model="form1.clientTeachTypeName" placeholder="请输入上课方式名称" maxlength="20" />
              </el-form-item>
            </el-form>
          </template>
        </el-step>
        <el-step title="课前提醒设置">
          <template slot="icon">
            <i class="yz-icon-tip"></i>
          </template>
          <template slot="description">
            <el-form ref="form2" size='mini' :model="form2" :rules="basicRules" label-width="180px">
              <el-form-item label="课前提醒文案：">
                <el-button class="options-btn" type="primary" @click="openTemplate">查看模板</el-button>
                <el-button class="options-btn" type="primary" @click="useTemplate">使用模板编辑</el-button>
              </el-form-item>
              <el-form-item label="提示文案：" prop="remindContent">
                <el-input v-model="form2.remindContent" placeholder="请输入提示文案" maxlength="30" />
              </el-form-item>
              <el-form-item label="课程名称：" class="course-info">默认安排课程名称</el-form-item>
              <el-form-item label="上课时间：" class="course-info"> 默认安排课程时间 </el-form-item>
              <el-form-item label="上课地点：" prop="teachAddress">
                <el-input v-model="form2.teachAddress" placeholder="请输入上课地点" maxlength="150" type="textarea" :rows="2" />
                <span class="remarks-tip">(可用"[NAME]"代替学员姓名,可用"[STDNO]"代替学员学号,可用"[PWD]"代替学员密码)</span>
              </el-form-item>
              <el-form-item label="联系电话：" prop="teachMobile">
                <el-input v-model="form2.teachMobile" placeholder="请输入联系电话" maxlength="20" :disabled="true" />
              </el-form-item>
              <el-form-item label="是否有跳转链接：" prop="customLinkUrl" class="radio-class">
                <el-radio v-model="form2.existGotoLink" :label="true">是</el-radio>
                <el-radio v-model="form2.existGotoLink" :label="false">否</el-radio><br />
                <div v-if="form2.existGotoLink" class="exist-url">
                  <el-radio v-model="form2.gotoType" :label="1">同操作指引</el-radio><br />
                  <el-radio v-model="form2.gotoType" :label="2">自定义链接<el-input v-model="form2.customLinkUrl" placeholder="请输入自定义链接" />
                  </el-radio>
                </div>
              </el-form-item>
            </el-form>
          </template>
        </el-step>

        <el-step title="上课跳转方式">
          <template slot="icon">
            <i class="yz-icon-right1"></i>
          </template>
          <template slot="description">
            <el-form ref="form3" :model="form3" size='mini' :rules="basicRules" label-width="180px">
              <el-form-item label="上课方式：" prop="gotoThirdTeachPlatform">
                <el-checkbox v-model="form3.gotoThirdTeachPlatform">跳转第三方上课平台</el-checkbox><br />
                <el-checkbox v-model="form3.existGuidance">操作指引</el-checkbox>
                <div v-show="form3.existGuidance" class="operation">

                  <el-form-item prop="thirdGuidanceUrl">
                    <el-radio v-model="form3.guidanceType" :label="1">
                      <span>第三方操作指引链接</span>
                      <el-input v-model="form3.thirdGuidanceUrl" class="operation-tit" placeholder="请输入自定义链接" />
                    </el-radio><br />
                  </el-form-item>
                  <el-radio v-model="form3.guidanceType" :label="2">自定义操作指引</el-radio>

                  <div v-show="form3.guidanceType === 2" class="custom-operation">
                    <el-form-item label="标题：" prop="customGuidanceTitle" label-width="80px">
                      <el-input v-model="form3.customGuidanceTitle" placeholder="请填写标题" maxlength="30" />
                    </el-form-item>
                    <el-form-item label="内容：" prop="customGuidanceContent" label-width="80px">
                      <wang-editor
                        ref="courseEditor"
                        v-model="form3.customGuidanceContent"
                        class="editor-color content"
                        :height="500"
                        :width="500"
                        :uploadUrl="uploadUrl"
                        placeholder='请输入内容'
                      />
                    </el-form-item>
                  </div>
                  <el-button v-if="form3.guidanceType === 2" class="options-tip" type="primary" @click="previewFn">预览操作指引</el-button>
                </div>
              </el-form-item>
            </el-form>
          </template>
        </el-step>
        <el-step class="last-step" title="考勤设置">
          <template slot="icon">
            <i class="yz-icon-setting"></i>
          </template>
          <template slot="description">
            <el-form size='mini' :model="form4" label-width="180px">
              <el-form-item label="是否记录打卡时间：" class="record-time">
                <el-radio v-model="form4.recordsPunchCard" :label="false">否</el-radio>
                <br />
                <el-radio v-model="form4.recordsPunchCard" :label="true">是</el-radio>
                <span class="record-time-tip">说明：已出勤（app/远智学堂已点击去学习跳转）、未出勤（课程出勤初始状态）</span>
              </el-form-item>
            </el-form>
          </template>
        </el-step>
      </el-steps>
    </div>
    <el-dialog
      :visible.sync="isShowPreview"
      class="preview-box"
      :show-close="false"
      append-to-body
      width="375px"
      top="3vh"
    >
      <div class="preview-main">
        <div class="preview-header">
          <img src="../../../assets/imgs/guokai/moblie-head.png" alt="" />
        </div>
        <div class="preview-content">
          <h3>{{ form3.customGuidanceTitle }}</h3>
          <div v-html="form3.customGuidanceContent"></div>
        </div>
        <div class="preview-bot">
          <div v-if="form3.gotoThirdTeachPlatform" class="preview-btn">进入网站学习</div>
          <img src="../../../assets/imgs/guokai/moblie-bot.png" alt="" />
        </div>
        <i class="el-icon-circle-close" @click="isShowPreview = false"></i>
      </div>
    </el-dialog>
    <template-pop :visible.sync="isShowTemplate" />
    <!-- <preview-pop :visible.sync="isShowPreview" :form="form3" /> -->
  </common-dialog>
</template>

<script>
import templatePop from './templatePop.vue';
import { validate } from '@/utils/validate';
// import previewPop from './previewPop.vue';

const proxy = {
  // 国开项目
  gk: [
    '/gkTeachType/insertTeachTypeConf.do', // 新增上课方式配置
    '/gkTeachType/updateTeachTypeConf.do', // 编辑上课方式配置
    '/gkTeachType/getTeachTypeConfInfo.do', // 查询单个上课方式
    '/coursePlan/uploadPicture.do' // 富文本图片上传
  ],
  // bms 项目
  bms: [
    '/teachType/insertTeachTypeConf.do', // 新增上课方式配置
    '/teachType/updateTeachTypeConf.do', // 编辑上课方式配置
    '/teachType/getTeachTypeConfInfo.do', // 查询单个上课方式
    '/graduatePaper/uploadPicture.do' // 富文本图片上传
  ]
};
export default {
  components: {
    templatePop
    // previewPop
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const customLinkUrlCheck = (rule, value, callback) => {
      if (this.form2.gotoType === 2 && value === '') {
        callback(new Error('请输入自定义链接'));
      } else {
        callback();
      }
    };
    const couseTypeCheck = (rule, value, callback) => {
      if (!this.form3.gotoThirdTeachPlatform && !this.form3.existGuidance) {
        callback(new Error('请选择上课方式'));
        if (this.isClose) {
          this.$message.error('请选择上课方式');
        }
      } else if (this.form2.existGotoLink && this.form2.gotoType === 1 && !this.form3.existGuidance) {
        callback(new Error('请选择操作指引'));
        this.$message.error('请选择操作指引');
      } else {
        callback();
      }
    };
    const thirdGuidanceUrlCheck = (rule, value, callback) => {
      console.log(this.form3.guidanceType, 'this.form3.guidanceType');
      if (this.form3.guidanceType === 2) return callback();
      if (this.form3.existGuidance && value === '') {
        callback(new Error('请输入自定义链接'));
        this.$message.error('请输入自定义链接');
      } else {
        callback();
      }
    };
    const customGuidanceCheck = (rule, value, callback) => {
      if (this.form3.guidanceType === 2 && value === '') {
        callback(new Error('请输入操作指引'));
        this.$message.error('请输入操作指引');
      } else {
        callback();
      }
    };
    const teachAddressCheck = (rule, value, callback) => {
      console.log(value, 'teachAddressCheck');
      if (validate('patrn', value)) {
        console.log('请不要输入特殊字符');
        callback(new Error('内容存在特殊字符，无法保存'));
      } else {
        callback();
      }
    };
    return {
      isClose: false,
      uploadUrl: '',
      apiSource: '', // 项目类型
      show: false,
      isShowTemplate: false,
      isShowPreview: false,
      form1: {
        clientTeachTypeName: '', // 上课方式名称
        adminTeachTypeName: '' // 学员端上课方式名称
      },

      form2: {
        remindContent: '', // 提示文案
        teachAddress: '', // 上课地点
        teachMobile: '************',
        existGotoLink: true, // 是否有跳转链接
        gotoType: 1, // 跳转链接类型
        customLinkUrl: '' // 跳转链接
      },
      form3: {
        gotoThirdTeachPlatform: false, // 是否跳转第三方上课平台
        existGuidance: false, // 是否存在操作指引
        thirdGuidanceUrl: '', // 第三方操作链接
        guidanceType: 1, // 操作指引-类型
        customGuidanceTitle: '', // 操作指引-标题
        customGuidanceContent: '' // 操作指引-内容
      },
      form4: {
        recordsPunchCard: false, // 是否记录打卡时间
        openStatus: true // 开启状态
      },

      basicRules: {
        clientTeachTypeName: [
          { required: true, message: '这是必填字段', trigger: 'blur' }
        ],
        adminTeachTypeName: [
          { required: true, message: '这是必填字段', trigger: 'blur' }
        ],
        teachAddress: [
          { required: true, message: '这是必填字段', trigger: 'blur' },
          { required: true, validator: teachAddressCheck }
        ],
        teachMobile: [
          { required: true, message: '这是必填字段', trigger: 'blur' }
        ],
        existGotoLink: [
          { required: true, message: '这是必填字段', trigger: 'blur' }
        ],
        customLinkUrl: [
          { required: true, validator: customLinkUrlCheck, trigger: 'blur' }
        ],
        remindContent: [
          { required: true, message: '这是必填字段', trigger: 'blur' }
        ],
        gotoThirdTeachPlatform: [
          { required: true, validator: couseTypeCheck, trigger: 'blur' }
        ],
        thirdGuidanceUrl: [
          { required: true, validator: thirdGuidanceUrlCheck, trigger: 'blur' }
        ],
        customGuidanceTitle: [
          { required: true, validator: customGuidanceCheck, trigger: 'blur' }
        ],
        customGuidanceContent: [
          { required: true, validator: customGuidanceCheck, trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {
    this.apiSource = this.$route.query.apiSource || 'gk';
    this.uploadUrl = proxy[this.apiSource][3];
    console.log(this.uploadUrl, 'this.uploadUrl');
  },
  methods: {
    open() {
      this.isClose = true;
      this.apiSource = this.$route.query.apiSource || 'gk';
      if (this.row?.teachTypeConfId) {
        this.selectSingle();
      }
    },
    openTemplate() {
      this.isShowTemplate = true;
    },
    selectSingle() {
      const url = proxy[this.apiSource][2];
      this.$http.post(url, { teachTypeConfId: this.row?.teachTypeConfId }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.form1.clientTeachTypeName = body.clientTeachTypeName;
          this.form1.adminTeachTypeName = body.adminTeachTypeName;

          this.form2.remindContent = body?.teachRemindConfQuery.remindContent || '';
          this.form2.teachAddress = body?.teachRemindConfQuery.teachAddress || '';
          // this.form2.teachMobile = body?.teachRemindConfQuery.teachMobile || '';
          this.form2.existGotoLink = body?.teachRemindConfQuery.existGotoLink;
          if (body?.teachRemindConfQuery.existGotoLink) {
            this.form2.gotoType = body?.teachRemindConfQuery.gotoType || 1;
            this.form2.customLinkUrl = body?.teachRemindConfQuery.customLinkUrl;
          }
          this.form3.gotoThirdTeachPlatform = body.gotoThirdTeachPlatform;
          this.form3.existGuidance = body.existGuidance;
          if (body.existGuidance) {
            this.form3.guidanceType = body.guidanceType || 1;
            this.form3.thirdGuidanceUrl = body.thirdGuidanceUrl || '';
          }
          if (body.guidanceType === 2) {
            this.form3.customGuidanceTitle = body.customGuidanceTitle || '';
            this.$refs['courseEditor'].setContent(body.customGuidanceContent);
            // this.form3.customGuidanceContent = body.customGuidanceContent || '';
          }
          this.form4.recordsPunchCard = body.recordsPunchCard;
          // form1: {
          //   clientTeachTypeName: '', // 上课方式名称
          //   adminTeachTypeName: '' // 学员端上课方式名称
          // },

          // form2: {
          //   remindContent: '', // 提示文案
          //   teachAddress: '', // 上课地点
          //   teachMobile: '',
          //   existGotoLink: true, // 是否有跳转链接
          //   gotoType: '1', // 跳转链接类型
          //   customLinkUrl: '' // 跳转链接
          // },
          // form3: {
          //   gotoThirdTeachPlatform: false, // 是否跳转第三方上课平台
          //   existGuidance: false, // 是否存在操作指引
          //   thirdGuidanceUrl: '', // 第三方操作链接
          //   guidanceType: '1', // 操作指引-类型
          //   customGuidanceTitle: '', // 操作指引-标题
          //   customGuidanceContent: '' // 操作指引-内容
          // },
          // form4: {
          //   recordsPunchCard: true, // 是否记录打卡时间
          //   openStatus: true // 开启状态
          // },
        }
      });
    },
    // 使用模板编辑
    useTemplate() {
      this.form2.remindContent = '学习充电时间到啦！请同学们认真学习，一起做上进青年。';
      this.form2.teachAddress = '1、远智教育微信公众号--远智学堂--我的课表。2、电脑登录远智教育官网--远智学堂--我的课表。\n 温馨提示：账号是:[STDNO]，密码是ouchn@2021或Ouchn@2021，点击查看详情查看操作指引。';
      this.form2.teachMobile = '************';
    },
    previewFn() {
      console.log(this.form3.customGuidanceContent, '预览this.customGuidanceContent');
      console.log(this.isShowPreview);
      this.isShowPreview = true;
    },

    handleFormParams() {
      if (this.form3.guidanceType !== 1) {
        this.form3.thirdGuidanceUrl = '';
      }
      const teachRemindConfQuery = Object.assign(this.form2);
      const allForm = { ...this.form1, ...this.form3, ...this.form4 };
      allForm['teachRemindConfQuery'] = teachRemindConfQuery;
      console.log(allForm, 'allForm');
      return allForm;
    },
    submit() {
      const allForm = this.handleFormParams();
      const list = [];
      list.push(
        this.checkForm('form1'),
        this.checkForm('form2'),
        this.checkForm('form3')
      );
      Promise.all(list)
        .then(() => {
          console.log(this.row, this.row?.teachTypeConfId);
          console.log('通过检测1');
          console.log(this.apiSource, 'this.apiSource');
          let url = proxy[this.apiSource][0];
          if (this.row?.teachTypeConfId) {
            url = proxy[this.apiSource][1];
            allForm['teachTypeConfId'] = this.row?.teachTypeConfId;
          }
          console.log(url, allForm);
          this.$http.post(url, allForm, {
            headers: {
              'Content-Type': 'application/json'
            }
          }).then((res) => {
            const { fail, body } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.$emit('update:visible', false);
              this.$emit('getTableList');
            }
          });
        })
        .catch(() => {
          console.log('未通过');
        });
    },
    checkForm(formName) {
      console.log('formName', formName);
      return new Promise((resolve, reject) => {
        this.$refs[formName].validate(valid => {
          console.log('valid', valid);
          if (valid) {
            resolve();
          } else reject();
        });
      });
    },
    close() {
      console.log('关闭');
      this.isClose = true;
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-base-container{
  margin: 0 auto;
  padding-top: 33px;
  color: #000;
  // overflow-y: scroll;

  ::v-deep .yz-search-form .el-form-item {
    margin-top: 20px;

  }
  // ::v-deep .el-form-item--mini.el-form-item{
  //   display: flex;
  //   justify-content:flex-start;
  //   align-items: center;
  // }

  ::v-deep .el-step__description.is-finish{
    color: #000;
  }

  // ::v-deep .el-dialog{
  //   width:85vw;
  //   height:85vh;
  // }
  ::v-deep .el-step__description{
    font-size: medium;
  }
  ::v-deep .el-step.is-vertical .el-step__title{
    font-size: 18px;
    color: #000;
    font-weight: 600;
  }

  ::v-deep  .el-step:last-of-type .el-step__line{
    display: block;
    background-color: #409EFF;
  }

  ::v-deep .disabled .el-upload--picture-card {
    display: none !important;
  }

  ::v-deep .el-step__icon.is-text{
    width: 29px;
    height: 29px;
    background: #FFFFFF;
    box-shadow: 0px 3px 8px 0px rgba(152,152,152,0.3);
    border:none;
  }
  ::v-deep .el-step__line-inner{
    border-style: dashed;
  }
  .last-step ::v-deep  .el-step__line {
    display: none;
    border: none;
    // border-style: dashed;
  }
  ::v-deep .el-step.is-vertical .el-step__line{
    width: 1px;
    left: 13px;
  }

  ::v-deep .el-step {
    .el-step__description  {
      padding-right: 5%;
    }

    .el-step__title {
      font-size: 22px;
      font-weight: 600;
      color: #1A1B1D;
      line-height: 30px;
    }

    .el-input--mini .el-input__inner  {
      padding: 0 10px;
      height: 32px;
      line-height: 28px;
      font-size: 14px;
      color: #1F1F1F;
    }
    .el-input--mini .el-input__icon {
        line-height: 42px;
    }
    .el-date-editor {
      width: 200px;
      .el-input__inner  {
        padding-left: 30px;
      }

    }
    .el-textarea__inner {
      padding: 0 10px;
      line-height: 28px;
      font-size: 14px;
      color: #1F1F1F;
    }

    .el-form-item__label {
      margin-top: 7px;
      font-size: 14px;
      color: #606266;
      line-height: 20px;
    }
    .more-info .radio-class .el-form-item__label {
      margin-top: 5px;
    }
  }
  ::v-deep .el-step:last-child{
    .el-step__main{
      border-bottom: none;
    }
  }
  ::v-deep .el-step:last-of-type .el-step__line{
    display: none;
  }
  ::v-deep .el-step__main{
    border-bottom: 1px solid #EDEDED;
    margin-bottom: 30px;
    padding-bottom: 15px;
  }

  .exist-url,.operation{
    ::v-deep .el-input__inner{
      margin-left: 10px;
    }
  }
  .operation{
    margin-left: 20px;
  }
  .options-tip{
    margin-left: 80px;
    display: block;
    margin-top: 10px;
  }
  .operation ::v-deep .operation-tit .el-input__inner{
    width: 683px;
  }
  .record-time {
    ::v-deep .el-radio{
      margin-right: 20px;
    }
    .record-time-tip{
      color: #F34D4E;
    }
  }
  ::v-deep .el-form-item {
    .el-input__inner,.el-textarea__inner{
      width: 862px;
    }
  }
  .exist-url  ::v-deep .el-input__inner{
    width: 758px;
  }
  .custom-operation ::v-deep .el-input__inner{
    margin-left: 0;
    width: 100%;
  }
  ::v-deep .el-step__icon{
    i{
      width: 16px;
      height: 16px;
    }
    .yz-icon-tip{
      width: 16px;
      height: 20px;
    }
    .yz-icon-setting{
      width: 25px;
      height: 25px;
    }
  }

  .remarks-tip{
    font-size: 14px;
    color: #C2C2C2;
  }
  .course-info ::v-deep .el-form-item__content{
    margin-top: 2px;
  }

}
::v-deep .yz-common-dialog__footer{
  text-align: center;
  .el-button{
    margin-left: 30px;
  }
}
.iframe{
  width: 100%;
  height: 500px;
  overflow: auto;
}

.preview-box{
  ::v-deep .el-dialog__header{
    padding: 0;
  }
  ::v-deep .el-dialog__body{
    padding: 0;
    border-radius: 20px;
    color: #000;
  }
  .preview-main{
    position: relative;
    border-radius: 8px;
    height: 700px;
    .preview-header{
      width: 100%;
      height: 85px;
      img{
        width: 100%;
        height: 85px;
      }
    }
    .preview-bot{
      position: absolute;
      bottom: 0px;
      // position: fixed;
      // width: 100%;
      width: 100%;
      .preview-btn{
        border-top: 5px solid #FFFFFF;
        // cursor: pointer;
        height: 50px;
        line-height: 45px;
        background: linear-gradient(135deg, #F09190 0%, #F07877 66%, #F06E6C 100%);
        font-size: 15px;
        text-align: center;
        font-weight: 600;
        color: #FFFFFF;
      }
      img{
        width: 100%;
        height: 35px;
      }
    }
    .preview-content{
      padding:0px 16px 22px;
      overflow: auto;
      height: 535px;
      h3{
        margin: 16px 0 22px;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        color: #333333;
      }
      ::v-deep img{
        width: 100%!important;
        height: auto!important;
      }
      ::v-deep div{
        width: auto!important;
        line-height: 22px;
      }
    }
    i{
      cursor: pointer;
      font-size: 30px;
      color: #ddd;
      position:absolute;
      bottom: -45px;
      left: 50%;
      transform: translate(-50%);
    }
  }
}
</style>
