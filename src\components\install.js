import Vue from 'vue';
import DictSelect from './selects/dict-select';
import CityCascader from './selects/city-cascader';
import AllschoolSelect from './selects/allschool-select';
import MajorSelect from './selects/major';
import CommonDialog from './common-dialog';
import Pagination from './Pagination';
import openPackup from './open-packup';
import UploadFile from './UploadFile';
import InfiniteSelects from './InfiniteSelects';
import WangEditor from './WangEditor';
// 安装全局组件
const components = [
  DictSelect,
  CityCascader,
  AllschoolSelect,
  CommonDialog,
  MajorSelect,
  Pagination,
  openPackup,
  UploadFile,
  InfiniteSelects,
  WangEditor
];

const install = (list) => {
  list.forEach((item) => { Vue.component(item.name, item); });
};

install(components);

