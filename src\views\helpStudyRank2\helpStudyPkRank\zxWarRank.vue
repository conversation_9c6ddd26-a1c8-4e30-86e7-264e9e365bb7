<template>
  <div class="wrap">
    <div v-loading="tableLoading" element-loading-background="rgba(0, 0, 0, 0.8)" class="connect">
      <div v-for="(warItem, warIndex) in warList" :key="warIndex" class="block">
        <div class="load-more" @click="changeLoad(warItem)">
          <img v-if="!warItem.showMore" src="../../../assets/imgs/helpStudyPkRank/show-more1.png" alt="" />
          <img v-if="warItem.showMore" src="../../../assets/imgs/helpStudyPkRank/show-more2.png" alt="" />
        </div>
        <PkProgress :list="warItem.areaTeamList" :title="pkType | pkTypeToText" />
        <!-- 表格 -->
        <div v-if="warItem.showMore" class="table-box">
          <el-table
            v-sticky-scroller.always
            border
            size="small"
            style="width: 47%"
            header-cell-class-name='table-cell-header'
            :data="warItem.areaTeamList[0].areaStreamVO"
            class="first-table"
            show-summary
            :summary-method="getSummaries"
            :header-row-class-name="headerStyle"
          >
            <el-table-column prop="teamName" label="部门" width="150" align="center" />
            <el-table-column prop="empName" label="分校长" align="center" />
            <el-table-column prop="manPower" label="人力" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('tmDialogShow', warItem.areaTeamList[0])">
                  {{ scope.row.manPower }}
                </el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>
            <el-table-column prop="todayOrders" label="今日合计" align="center" />
            <el-table-column prop="performanceDeduction" label="调整业绩" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('lrDialogShow', warItem.areaTeamList[0])">
                  {{ scope.row.performanceDeduction }}
                </el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>
            <el-table-column prop="totalOrders" label="活动合计" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('resultsDialogShow', warItem.areaTeamList[0])">{{
                  scope.row.totalOrders }}</el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>
            <!-- 助学积分、助学人数、活动人均 -->
            <el-table-column align="center" prop="totalScore">
              <template slot="header">
                <span class="score-column-color">{{ pkType | pkTypeToText }}</span>
              </template>

              <template slot-scope="scope">
                <span class="score-column-color">{{ scope.row.totalScore }}</span>
              </template>
            </el-table-column>
          </el-table>

          <el-table
            v-sticky-scroller.always
            border
            size="small"
            style="width: 47%"
            header-cell-class-name='table-cell-header'
            :data="warItem.areaTeamList[1].areaStreamVO"
            class="second-table"
            show-summary
            :summary-method="getSummaries"
            :header-row-class-name="headerStyle"
          >
            <el-table-column prop="teamName" label="部门" width="150" align="center" />
            <el-table-column prop="empName" label="分校长" align="center" />
            <el-table-column prop="manPower" label="人力" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('tmDialogShow', warItem.areaTeamList[1])">{{
                  scope.row.manPower }}</el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>
            <el-table-column prop="todayOrders" label="今日合计" align="center" />
            <el-table-column prop="performanceDeduction" label="调整业绩" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('lrDialogShow', warItem.areaTeamList[1])">{{
                  scope.row.performanceDeduction }}</el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>
            <el-table-column prop="totalOrders" label="活动合计" align="center">
              <template v-slot="scope">
                <el-link class="link" @click="openDetailsDialog('resultsDialogShow', warItem.areaTeamList[1])">{{
                  scope.row.totalOrders }}</el-link>
                <i class="icon-arrow-right"></i>
              </template>
            </el-table-column>

            <!-- 助学积分、助学人数、活动人均 -->
            <el-table-column align="center" prop="totalScore">
              <template slot="header">
                <span class="score-column-color">{{ pkType | pkTypeToText }}</span>
              </template>

              <template slot-scope="scope">
                <span class="score-column-color">{{ scope.row.totalScore }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-if="warItem.showMore" class="details">
          <el-button class="pkDetails" type="primary" @click="toPkDetails(warItem)">
            详情数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- <div v-else class="data-null">
      <img src="../../../assets/imgs/helpStudyPkRank/data-null.png" alt="" />
      <p>暂无数据</p>
    </div> -->

    <!-- 人力详情弹窗 -->
    <team-member-dialog :visible.sync="tmDialogShow" :query="currentLookTeam" />
    <!-- 减业绩详情弹窗 -->
    <loss-results-dialog :visible.sync="lrDialogShow" :query="currentLookTeam" />
    <!-- 活动合计详情弹窗 -->
    <results-dialog :visible.sync="resultsDialogShow" :query="currentLookTeam" />
  </div>
</template>

<script>
import TeamMemberDialog from '../unionDetailed/team-member-dialog';
import LossResultsDialog from '../unionDetailed/loss-results-dialog';
import ResultsDialog from '../unionDetailed/results-dialog';
import PkProgress from './components/PkProgress.vue';
import {
  StickyScroller
} from '@cell-x/el-table-sticky';

export default {
  directives: {
    StickyScroller: new StickyScroller({ offsetBottom: '15px' }).init()
  },
  filters: {
    pkTypeToText(val) {
      const pkTypeEnum = {
        1: '助学积分',
        2: '助学人数',
        3: '活动人均'
      };
      return pkTypeEnum[val] || '/';
    }
  },
  components: {
    TeamMemberDialog,
    LossResultsDialog,
    ResultsDialog,
    PkProgress
  },
  props: {
    row: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      tableLoading: false,
      tmDialogShow: false, // 人力详情弹窗
      lrDialogShow: false, // 减业绩详情弹窗
      resultsDialogShow: false, // 活动合计详情弹窗
      currentLookTeam: {}, // 当前查看的团队信息

      endTime: 0, // 结束时间戳
      pkType: 0, // 1：助学积分 2：助学人数 3：活动人均
      pkRange: [], // pk范围，1：成教 2：国开 3：全日制 4：自考 5：研究生 6：职业教育 7：海外教育

      warList: []
    };
  },
  methods: {
    init() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.getChildActivityInfo(this.row.pkChildId);
      this.getPkStreamV2(this.row.pkChildId);
    },
    // 获取活动信息
    getChildActivityInfo(pkChildId) {
      this.$post('getChildActivityInfo', { pkChildId }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.pkType = body.pkType;
          this.endTime = body.endTime;
          this.pkRange = body.pkRange.split(',');
          this.$emit('endTime', body.endTime);
        }
      });
    },
    // 获取pk流水（助学榜单用）
    getPkStreamV2(pkChildId) {
      this.tableLoading = true;
      this.$post('getPkStreamV2', { pkChildId }).then(async res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.warList = this.handleCompareGrades(body);
          setTimeout(() => {
            const warItem = this.warList[0];
            this.changeLoad(warItem);
          }, 100);
        }
      });
    },
    // 处理比较成绩
    handleCompareGrades(data) {
      return data.map((item, index) => {
        item.showMore = false;
        const firstPk = item.areaTeamList[0];
        const secondPk = item.areaTeamList[1];
        const result = this.calculatePercentages(firstPk.totalOrders, secondPk.totalOrders);
        if (firstPk.totalOrders === secondPk.totalOrders) {
          firstPk.isWin = false;
          secondPk.isWin = false;
          firstPk.width = result.A;
          secondPk.width = result.B;
        } else if (firstPk.totalOrders > secondPk.totalOrders) {
          firstPk.isWin = true;
          secondPk.isWin = false;
          firstPk.width = result.A;
          secondPk.width = result.B;
        } else {
          firstPk.isWin = false;
          secondPk.isWin = true;
          firstPk.width = result.A;
          secondPk.width = result.B;
        }
        return item; // 返回修改后的元素
      });
    },
    // 处理成绩百分比
    calculatePercentages(A, B) {
      let percentageA = (A / (A + B)) * 100;
      let percentageB = (B / (A + B)) * 100;
      if (A === 0 && B === 0) {
        percentageA = 50;
        percentageB = 50;
      }
      // 保留两位小数
      percentageA = Math.round(percentageA * 100) / 100;
      percentageB = Math.round(percentageB * 100) / 100;

      return { A: percentageA, B: percentageB };
    },
    openDetailsDialog(field, areaTeamItem) {
      this.currentLookTeam = {
        pkActId: this.row.pkActId,
        pkChildId: this.row.pkChildId,
        pkType: this.pkType,
        pkRange: this.pkRange,
        isPerson: 0,
        dialogTitle: areaTeamItem.teamName,
        newTeamId: areaTeamItem.teamId
      };
      this[field] = true;
    },
    refresh() {
      if (this.row.pkChildId) {
        this.init();
      }
    },
    getSummaries(param) {
      const { columns, data } = param;
      // 合并前两列
      if (columns[0]) {
        columns[0].colSpan = 2;
      }
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
        }
      });
      // 移除第二列， 用来和第一列合并
      if (sums.length >= 2) {
        sums.splice(1, 1);
      }
      return sums;
    },
    changeLoad(warItem) {
      this.$set(warItem, 'showMore', !warItem.showMore);
    },
    toPkDetails(row) {
      this.$router.push({
        name: 'rankDetails',
        query: {
          groupId: row.groupId,
          pkActId: this.row.pkActId,
          pkChildId: this.row.pkChildId
        }
      });
    },
    headerStyle() {
      return 'tableStyle';
    }
  }
};
</script>

<style lang="scss" scoped>
/* 定义表格渐变背景颜色变量 */
$first-table-default-bg-color: linear-gradient(270deg, #2D2044 0%, #753B52 100%); // 第一个表格默认背景颜色
$first-table-hover-bg-color: linear-gradient(90deg, #A5505D 0%, #462949 100%); // 第一个表格鼠标悬停背景颜色
$second-table-default-bg-color: linear-gradient(270deg, #251E49 0%, #3A4D8C 100%); // 第二个表格默认背景颜色
$second-table-hover-bg-color: linear-gradient(90deg, #486AB8 0%, #2F3569 100%); // 第二个表格鼠标悬停背景颜色

.icon-arrow-right {
  width: 3px;
  height: 6px;
  background: url("../../../assets/imgs/helpStudyPkRank/icon-arrow-right.png") no-repeat;
  background-size: 100% 100%;
  display: inline-block;
  margin-left: 6px;
}

.link {
  color: #fff;

  &:hover {
    color: #fff;
  }

  &:hover:after {
    position: absolute;
    left: 0;
    right: 0;
    height: 0;
    bottom: 0;
    border-bottom: 1px solid #fff;
  }

}

.wrap {
  // flex: 1;
  background-color: #180E36;
  color: #fff;
  margin: 0;
  overflow: hidden;
  white-space: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.connect {
  width: 100%;
  padding: 10px;
  // height: 100%;
  // overflow-y: auto;
  /* height: 730px; */
  /* border: 1px solid #fff; */
}

.block {
  position: relative;
  // min-width: 1550px;
  width: 100%;
  background: rgba(255, 255, 255, .05);
  margin-bottom: 18px;
  border: 1px solid rgba(255, 255, 255, .4);
  border-radius: 5px;

  ::v-deep .tableStyle {
    background-color: #4731A6FF;
    color: #fff;
    font-weight: 400;
  }

  ::v-deep .el-table__header-wrapper {
    margin-bottom: 5px;
    border-radius: 2px;
  }

  /* 表格助学积分、助学人数、活动人均列特殊颜色 */
  ::v-deep .el-table__footer-wrapper tr td:nth-last-child(3) {
    color: #FFD387FF !important;
  }

  /* 表格助学积分、助学人数、活动人均列特殊颜色 */
  .score-column-color {
    color: #FFD387FF;
  }

  ::v-deep .el-table__body-wrapper {
    border-radius: 2px 2px 0 0;
  }

  ::v-deep .el-table__body-wrapper .el-table__row {
    margin-bottom: 10px;
  }

  ::v-deep .el-table__footer-wrapper {
    border-radius: 0 0 2px 2px;
  }

  ::v-deep .el-table__row {
    background-color: transparent;
    color: #fff;
    height: 41px;
  }

  ::v-deep .el-table__row:nth-child(1) {
    background-image: linear-gradient(90deg, rgba(#5F99FEFF, .5) 20%, rgba(#4977FEFF, .2) 100%);
    margin: 2px;
  }

  ::v-deep .el-table__row:nth-child(2) {
    background-image: linear-gradient(90deg, rgba(#5F99FEFF, .5) 20%, rgba(#4977FEFF, .2) 100%);
    margin-bottom: 2px;
  }

  ::v-deep .el-table__row:nth-child(3) {
    background-image: linear-gradient(90deg, rgba(#5F99FEFF, .5) 20%, rgba(#4977FEFF, .2) 100%);
    margin-bottom: 2px;
  }

  .first-table ::v-deep .el-table__row:hover td,
  .second-table ::v-deep .el-table__row:hover td {
    background: transparent !important;
    background-color: transparent !important;
    /* opacity: .8; */
  }

  .first-table ::v-deep .el-table__row:hover {
    background: $first-table-hover-bg-color;
  }

  .second-table ::v-deep .el-table__row:hover {
    background: $second-table-hover-bg-color;
  }

  .first-table ::v-deep .el-table__row {
    background: $first-table-default-bg-color;
  }

  .first-table ::v-deep .el-table__footer tr {
    background: $first-table-hover-bg-color;
  }

  .first-table ::v-deep .el-table__footer tr td:nth-child(1) {
    background-image: linear-gradient(90deg, rgba(#F09190FF, .5) 20%, rgba(#F06E6CFF, .5) 100%);
  }

  .second-table ::v-deep .el-table__row {
    background: $second-table-default-bg-color;
  }

  .second-table ::v-deep .el-table__footer tr {
    background: $second-table-hover-bg-color;
  }

  .second-table ::v-deep .el-table__footer tr td:nth-child(1) {
    background-image: linear-gradient(90deg, rgba(#5F99FEFF, .5) 20%, rgba(#4977FEFF, .5) 100%);
  }

  /* 滚动条样式 - @cell-x/el-table-sticky插件 */
  ::v-deep .gm-scrollbar {
    background-color: rgba(255, 255, 255, 0.1);

    .thumb {
      background-color: rgba(255, 255, 255, 0.3);

      &:active,
      &:hover {
        background-color: rgba(255, 255, 255, 0.3) !important;
      }
    }
  }

  ::v-deep .el-table,
  .el-table__expanded-cell {
    background-color: #180e36;
  }

  ::v-deep .el-table td,
  .el-table th.is-leaf {
    border: .5px solid rgba(255, 255, 255, 0.1);
  }

  ::v-deep .el-table td {
    border-right: none;
  }

  ::v-deep .el-table th,
  .el-table tr {
    background-color: transparent;
  }

  ::v-deep .table-cell-header {
    background-color: transparent !important;
    color: #fff !important;
    margin-bottom: 5px;
  }

  ::v-deep .el-table--border td,
  ::v-deep .el-table--border th,
  ::v-deep .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
    border-right: none;
  }

  ::v-deep .el-table td.el-table__cell,
  ::v-deep .el-table th.el-table__cell.is-leaf {
    border-bottom: 0;
  }

}

.load-more {
  width: 100px;
  height: 15px;
  padding-bottom: -1px;
  text-align: center;
  position: absolute;
  left: 50%;
  bottom: -15px;
  transform: translate(-50%);
  cursor: pointer;
}

.table-box {

  .first-table,
  .second-table {
    display: inline-block;
    /* margin: 0 1% 0 2%; */
  }

  .first-table {
    margin: 0 2%;
  }

  .second-table {
    vertical-align: top;
  }
}

.details {
  width: 114px;
  height: 50px;
  margin: 10px auto 0;

  .el-button {
    padding: 10px 30px;
  }

  .pkDetails {
    color: #fff;
    background: radial-gradient(rgba(95, 153, 254, 0.4) 0%, rgba(73, 119, 254, 0.8) 100%);
  }
}

/* 滚动条样式 - @cell-x/el-table-sticky插件 */
::v-deep .gm-scrollbar {
  background-color: rgba(255, 255, 255, 0.1);

  .thumb {
    background-color: rgba(255, 255, 255, 0.3);

    &:active,
    &:hover {
      background-color: rgba(255, 255, 255, 0.3) !important;
    }
  }
}

/* 去除border线 */
::v-deep .el-table--border,
.el-table--group {
  border: none;
}

::v-deep .el-table--border::after,
.el-table--group::after,
.el-table::before {
  background-color: transparent;
}

::v-deep .el-table--border th,
.el-table__fixed-right-patch {
  border: none;
}

::v-deep .el-table__footer tr {
  background: linear-gradient(90deg, #4731A6FF 60%, #2e2255 100%);
}

::v-deep .el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
  background-color: transparent;
  color: #fff;
}

.el-avatar {
  background: #fff;
}

.el-avatar>img {
  width: 100%;
  margin-top: 10px;
  transform: scale(1.1);
}

::v-deep .el-table__footer-wrapper {
  margin-top: 0px;
}

.data-null {
  width: 119px;
  height: 130px;
  text-align: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
</style>
