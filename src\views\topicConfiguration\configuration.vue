<template>
  <el-dialog title="跳转配置" :visible.sync="configurationVisible" width="30%" :before-close="configurationHandleClose">
    <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="120px">
      <el-form-item label="帖子链接：" prop="url">
        <el-input v-model="formInline.url" placeholder="请输入帖子链接" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cal">取 消</el-button>
      <el-button type="primary" @click="ok">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    configurationVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formInline: {
        url: ''
      }
    };
  },
  created() {
    this.edit();
  },
  methods: {
    configurationHandleClose() {
      this.$emit('configurationVisible', false);
    },
    cal() {
      this.configurationHandleClose();
    },
    ok() {
      this.$http.post(`/qaConfig/addJumpUrl/${this.formInline.url}`).then(res => {
        if (res.ok) {
          this.$message({ message: '保存成功', type: 'success' });
        }
      });
      this.$emit('configurationVisible', false);
    },
    edit() {
      this.$http.get(`/qaConfig/getJumpUrl`).then(res => {
        if (res.ok) {
          this.formInline.url = res.body;
        }
      });
      this.$emit('configurationVisible', false);
    }

  }
};
</script>

<style lang = "scss" scoped>
.el-input {
    width: 130%;
}
</style>
