<template>
    <div class="yz-base-container">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="100px">
            <el-form-item label="创建人：">
                <el-input v-model="formInline.createEmpName" placeholder="创建人"></el-input>
            </el-form-item>
            <el-form-item label="发送方式:">
                <el-select v-model="formInline.sendType" placeholder="发送方式" clearable>
                    <el-option label="远智成教君" value="1"></el-option>
                    <el-option label="远智开放君" value="2"></el-option>
                    <el-option label="远智全日制" value="3"></el-option>
                    <el-option label="远智自考君" value="4"></el-option>
                    <el-option label="远智研究生" value="5"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="消息状态：" v-if="this.$route.fullPath.indexOf('type=1') != -1">
                <el-select v-model="formInline.msgStatus" placeholder="消息状态" clearable>
                    <el-option label="待提交" value="0"></el-option>
                    <el-option label="驳回" value="2"></el-option>
                    <el-option label="撤回" value="5"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="消息状态：" v-if="this.$route.fullPath.indexOf('type=3') != -1">
                <el-select v-model="formInline.msgStatus" placeholder="消息状态" clearable>
                    <el-option label="待同步" value="3"></el-option>
                    <el-option label="已同步" value="4"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="消息类型：">
                <el-select v-model="formInline.msgType" placeholder="消息类型" clearable>
                    <el-option label="任务通知" value="1"></el-option>
                    <!-- <el-option label="缴费通知" value="2"></el-option> -->
                    <el-option label="作业提醒" value="2"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item class="formInline-btn">
                <el-button type="primary" @click="onSubmit">查询</el-button>
                <el-button @click="reset">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="btnList" v-if="this.$route.fullPath.indexOf('type=1') !== -1">
            <!-- <span v-for="(item,index) in btnList" :key="index">
             <el-button  :type="item.type" @click="btnClick(item.key)"> {{item.title }}</el-button>
           </span> -->
            <el-button type="primary" @click="btnClick('1')" v-if="btnCJ">新增：成教</el-button>
            <el-button type="primary" @click="btnClick('2')" v-if="btnGK">新增：国开</el-button>
            <el-button type="primary" @click="btnClick('3')" v-if="btnZK">新增：自考</el-button>
            <el-button type="primary" @click="btnClick('4')" v-if="btnYZJ">新增：研究生</el-button>
            <el-button type="success" @click="btnClick('5')">提交</el-button>
        </div>
        <el-table :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange"
            v-loading="loading">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="msgTypeName" label="消息类型" align="center">
            </el-table-column>
            <el-table-column prop="name" label="消息内容" align="center">
                <template slot-scope="scope">
                    <div>消息名称：{{ scope.row.msgName }}</div>
                    <div>消息详情：{{ scope.row.msgContent }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="sendTypeName" label="发送方式" align="center">
            </el-table-column>
            <el-table-column prop="address" label="计划发送时间" align="center" width="250">
                <template slot-scope="scope">
                    <div>计划发送时间：{{ new Date(scope.row.sendTime).toLocaleString() }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="createEmpName" label="创建人" align="center" width="160">
                <template slot-scope="scope">
                    <div>{{ scope.row.createEmpName }}</div>
                    <div>{{ new Date(scope.row.createTime).toLocaleString() }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="msgStatusName" label="消息状态" align="center">
                <template slot-scope="scope">
                    <div>{{ scope.row.msgStatusName }}</div>
                    <div v-if="scope.row.msgStatus == 2">原因：{{ scope.row.abortReason }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="address" label="目标学员" align="center">
                <template slot-scope="scope">
                    <el-button  @click="toConfigure(scope.row, 1)"
                        v-if="scope.row.msgStatus == 0">配置</el-button>

                    <el-button type="primary" plain @click="toConfigure(scope.row, 2)"
                        v-if="scope.row.msgStatus == 2 || scope.row.msgStatus == 5 || scope.row.msgStatus == 1">查看</el-button>
                    <div>目标人数：{{ scope.row.studentCount }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="address" label="发送情况" align="center" v-if="this.$route.fullPath.indexOf('type=3') !== -1">
                <template slot-scope="scope">
                    <div>成功数：{{ scope.row.successedCount }}</div>
                    <div>失败数：{{ scope.row.failedCount }}</div>
                    <div><a @click="exportname(scope.row)">导出失败名单</a></div>
                </template>
            </el-table-column>
            <el-table-column prop="address" label="操作" align="center">
                <template slot-scope="scope">
                    <a @click="btnScopeClick(1, scope.row)" size="small" style="margin-left: 10px;">查看</a>
                    <a @click="btnScopeClick(4, scope.row)" size="small" style="margin-left: 10px;color: red;"
                        v-if="$route.fullPath.indexOf('type=3') !== -1 && btnWithdraw && scope.row.btnShow">撤回</a>
                    <a @click="btnScopeClick(2, scope.row)" size="small" style="margin-left: 10px;"
                        v-if="$route.fullPath.indexOf('type=1') !== -1">编辑</a>
                    <a @click="btnScopeClick(3, scope.row)" size="small" style="margin-left: 10px;color: red;"
                        v-if="$route.fullPath.indexOf('type=1') !== -1">删除</a>
                </template>
            </el-table-column>
        </el-table>
        <div class="block">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page.sync="page.pageNum" layout="prev, pager, next, sizes,jumper"
                :page-sizes="[10, 20, 30, 40]" :total="page.total">
            </el-pagination>
        </div>
        <addOrEditOrLookPop :show="showAddPop" :title="titlePop" :formContent="formContent" @show="showAddPop = false"  @title="titlePop = ' '">
        </addOrEditOrLookPop>
        <!-- <filterOrImport :show="showFilterPop" title="配置学员" @show="showFilterPop = false"></filterOrImport> -->
        <task-import :show="showTaskPop" title="配置学员" @show="showTaskPop = false" :sendTypePop="sendTypePop"
            :stateType="stateType"></task-import>
        <!-- <el-dialog title="选择目标学员" :visible.sync="showImportPop" width="30%" :before-close="handleClose">
            <div style="text-align: center;">
                <el-button type="primary" @click="taskImportClick">任务导入</el-button>
                <el-button type="primary" @click="filterOrImportClick">筛选或导入</el-button>
            </div>
        </el-dialog> -->
    </div>
</template>

<script>
import addOrEditOrLookPop from './addOrEditOrLookPop';
import TaskImport from './taskImport.vue';
import filterOrImport from './filterOrImport.vue';
import { bmsURL } from '@/config/request';

export default {
    components: { addOrEditOrLookPop, TaskImport },
    data() {
        return {
            showAddPop: false,
            showImportPop: false,
            showTaskPop: false,
            showFilterPop: false,
            sendTypePop: {},
            stateType: 'edit',
            titlePop: '',
            title: '',
            btnWithdraw: false,
            page: {
                total: 0,
                pageSize: 10,
                pageNum: 1
            },
            formInline: {
                createEmpName: '',
                sendType: '',
                msgStatus: '',
                msgType: '',
            },
            btnListScope: [],
            tableData: [],
            loading: true,
            formContent: {},
            multipleSelection: [],
            btnCJ: false,
            btnZK: false,
            btnGK: false,
            btnYZJ: false,
        };
    },
    created() {
        if (this.$route.fullPath.indexOf('type=1') !== -1) {
        } else if (this.$route.fullPath.indexOf('type=2') !== -1) {
            this.showBtn = true
            this.btnListScope = [
                { title: '编辑', type: 'primary', key: '2', show: true }
            ];
        } else {
            this.showBtn = true
            this.btnListScope = [
                { title: '查看', type: 'primary', key: '1', show: true },
                { title: '撤回', type: 'primary', key: '4', show: false }
            ];
        }
        this.getJurisdiction()
        this.getMessageConfigurationList()
    },
    methods: {
        exportname(row){
            window.location.href = bmsURL + `/msgManage/exportFailedSendLog?id=${row.mpMsgId}`;
        },
        getJurisdiction() {
            this.$http.post('/puGoodsInstallment/getPermission')
                .then(res => {
                    let arr = []
                    arr.push(res.body)
                    arr.forEach(item => {
                        item.forEach(items => {
                            if (items.indexOf('msgManage:revocation') != -1) {
                                this.btnWithdraw = true
                            }
                            console.log(items.indexOf('msgManage:add:cj'), 'items.indexOf()');
                            if (items.indexOf('msgManage:add:cj') != -1) {
                                this.btnCJ = true
                            }
                            if (items.indexOf('msgManage:add:zk') != -1) {
                                this.btnZK = true
                            }
                            if (items.indexOf('msgManage:add:gk') != -1) {
                                this.btnGK = true
                            }
                            if (items.indexOf('msgManage:add:yjs') != -1) {
                                this.btnYZJ = true
                            }
                        })
                    })
                })
        },
        getMessageConfigurationList() {
            this.loading = true
            const data = {
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                createEmpName: this.formInline.createEmpName,
                sendType: this.formInline.sendType,
                msgStatus: this.formInline.msgStatus,
                msgType: this.formInline.msgType,
                listType: this.$route.fullPath.indexOf('type=1') !== -1 ? 1 : this.$route.fullPath.indexOf('type=2') !== -1 ? 2 : 3
            };

            this.$http.post('/msgManage/getList', data, { json: true }).then(res => {
                if (res.ok) {
                    const responseData = res.body.data;
                    responseData.forEach(item => {
                        this.setMsgTypeName(item);
                        this.setSendTypeName(item);
                        this.setMsgStatusName(item);
                    });
                    if (this.$route.fullPath.indexOf('type=3') !== -1) {
                        res.body.data.forEach(item => {
                            if (item.sendTime > Date.parse(new Date()) && item.msgStatus == 3) {
                                item.btnShow = true
                            } else {
                                item.btnShow = false
                            }
                        })
                    }
                    this.tableData = responseData;
                    this.page.total = res.body.recordsTotal
                    this.loading = false
                }
            });
        },

        setMsgTypeName(item) {
            const msgTypeMap = {
                1: '任务通知',
                2: '作业提醒',
            };
            item.msgTypeName = msgTypeMap[item.msgType] || '';
        },

        setSendTypeName(item) {
            const sendTypeMap = {
                1: '远智成教君',
                2: '远智开放君',
                3: '全日制',
                4: '远智自考君',
                5: '远智研究生',
                6: '中专'
            };
            item.sendTypeName = sendTypeMap[item.sendType] || '';
        },

        setMsgStatusName(item) {
            const msgStatusMap = {
                0: '待提交',
                1: '待审核',
                2: '驳回',
                3: '待同步',
                4: '已同步',
                5: '撤回'
            };
            item.msgStatusName = msgStatusMap[item.msgStatus] || '';
        },

        taskImportClick() {
            this.showTaskPop = true
            this.showImportPop = false
        },
        filterOrImportClick() {
            this.showFilterPop = true
            this.showImportPop = false
        },
        handleClose() {
            this.showImportPop = false
        },
        handleSizeChange(val) {
            this.loading = true
            this.page.pageSize = val
            this.getMessageConfigurationList()
        },
        handleCurrentChange(val) {
            this.loading = true
            this.page.pageNum = val
            this.getMessageConfigurationList()
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        onSubmit() {
            this.page.pageSize = 10
            this.page.pageNum = 1
            this.getMessageConfigurationList()
        },
        reset() {
            this.formInline = {};
        },
        toConfigure(item, key) {
            if (key == 2) {
                this.stateType = 'see'
            } else {
                this.stateType = 'edit'
            }
            this.showTaskPop = true
            this.sendTypePop = item
        },
        btnClick(key) {
            this.formContent = {};

            if (key === '1') {
                this.showAddPop = true
                this.titlePop = '新增成教'
            } else if (key === '2') {
                this.showAddPop = true
                this.titlePop = '新增国开'
            } else if (key === '3') {
                this.showAddPop = true
                this.titlePop = '新增自考'
            } else if (key === '4') {
                this.showAddPop = true
                this.titlePop = '新增研究生'
            } else {
                this.submitRegister()
            }
        },
        submitRegister() {
            if (this.multipleSelection.length == 0) {
                this.$message({ type: 'error', message: '请先选择要提交的消息' });
                return
            }
            let type = this.multipleSelection.every(item => {
                if (item.studentCount == 0) {
                    this.$message({ type: 'error', message: '请先配置学员' });
                }
                return item.studentCount !== 0;

            })
            let arr = []
            this.multipleSelection.forEach(item => {
                arr.push(item.mpMsgId)

            })
            const data = {
                mpMsgIdList: arr
            };

            if (type) {
                this.$confirm('确定要提交吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.post('/msgManage/submit', data, { json: true }).then(res => {
                    if (res.ok) {
                        this.getMessageConfigurationList()
                        this.$message({ type: 'success', message: '提交成功!' });
                    }
                });
                })
            }

        },
        btnScopeClick(key, row) {
            row.sendType = String(row.sendType)
            row.msgType = String(row.msgType)
            if (key == '1') {
                this.formContent = row;
                this.titlePop = '查看详情';
                this.showAddPop = true;
            } else if (key == '2') {
                this.formContent = row;
                this.titlePop = '编辑详情';
                this.showAddPop = true;
            } else if (key == '3') {
                this.del(row)
            } else {
                this.revocation(row)
            }
        },
        revocation(row) {
            this.$confirm('确定要撤回吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.post('/msgManage/revocation', { mpMsgId: row.mpMsgId }, { json: true }).then(res => {
                    if (res.ok) {
                        this.$message({ type: 'success', message: '撤回成功!' });
                        this.getMessageConfigurationList()
                    }
                })
            })
        },
        del(row) {
            this.$confirm('确认删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    let data = {
                        mpMsgId: row.mpMsgId
                    }
                    this.$http.post('/msgManage/delete', data, { json: true }).then(res => {
                        if (res.ok) {
                            this.$message({ type: 'success', message: '删除成功!' });
                            this.getMessageConfigurationList()
                        }
                    })
                }
                )
        }

    }
};
</script>

<style lang="scss" scoped>
.formInline-btn {
    float: right;
}

.btnList {
    margin-bottom: 20px;
}

.btnList,
.block {
    text-align: right;
}

.block {
    margin-top: 10px;

}
</style>