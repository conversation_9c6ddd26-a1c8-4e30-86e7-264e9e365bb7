<template>
  <div>
    <el-dialog
      :title="optionsType"
      :visible.sync="rankingShow"
      width="90%"
      :before-close="handleClose"
    >
      <el-form ref="form" :model="form" label-width="110px" class="yz-search-form">
        <el-form-item label="校区：">
          <el-select v-model="form.campusName" placeholder="请选择校区" :disabled="true">
            <!-- <el-option v-for="item in schoolInfo" :key="item.id" :label="item.campusName" :value="item.campusId" /> -->
          </el-select>
        </el-form-item>
        <el-form-item label="部门:">
          <el-select v-model="form.dpName" placeholder="请选择部门" :disabled="true">
            <!-- <el-option v-for="item in departmentInfo" :key="item.dpId" :label="item.dpName" :value="item.dpId" /> -->
          </el-select>
        </el-form-item>
        <el-form-item label="时间段:">
          <el-date-picker v-model="tiemValue" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="changeTiem" />
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" @click="query">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
      </el-form>
      <div style="float:right;margin:15px 25px 15px 0;">
        <!-- <el-button type="primary" @click="problemData()">问题数据</el-button> -->
        <el-button type="primary" @click="exportList()">导出Excel</el-button>
      </div>
      <el-table ref="multipleTable" :data="tableData" style="width: 100%; margin: 25px 0" border>
        <el-table-column prop="userName" label="姓名" align="center" />
        <!-- <el-table-column prop="dpName" label="部门" align="center" /> -->
        <el-table-column prop="searchTotal" label="关键词搜索" align="center" />
        <el-table-column prop="clickTotal" label="点击量" align="center" />
        <el-table-column prop="collectTotal" label="收藏量" align="center" />
        <el-table-column prop="questionTotal" label="发布次数" align="center" />
        <el-table-column prop="usefulTotal" label="问题（有用）" align="center" />
        <el-table-column prop="uselessTotal" label="问题（没用）" align="center" />
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <div class="iconStyle">
              <div class="el-icon-view" @click="handleEdit(scope.$index, scope.row)"></div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pageClass">
        <el-pagination
          :current-page.sync="page.currentPage"
          :page-size="page.pageSize"
          :total="page.total"
          :page-sizes="[10, 20, 30, 40]"
          layout="total, prev, pager, next, sizes,jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span> -->
    </el-dialog>
    <personalDetails :rankingShow="deatailsType" :optionsType="detailsTitle" :showList='detailsInfo' @dialogVisible='deatailsType=false' />
  </div>
</template>

<script>
import { ossUri, downUri } from '@/config/request';
import moment from 'moment';
import personalDetails from './personalDetails.vue';
export default {
  components: { personalDetails },
  props: {
    rankingShow: {
      type: Boolean,
      default: false
    },
    optionsType: {
      type: String,
      default: ''
    },
    showList: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        dpId: '',
        startSearchTime: '',
        endSearchTime: '',
        campusName: ''
      },
      tiemValue: {},
      tableData: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 1
      },
      deatailsType: false,
      detailsTitle: '',
      detailsInfo: {}
    };
  },
  watch: {
    showList(newVal, oldVal) {
      if (newVal) {
        this.loadForm();
      }
    }
  },
  created() {
    this.loadList();
    // this.form.campusName = this.showList.campusName;
  },
  methods: {
    handleClose() {
      this.$emit('dialogVisible', false);
    },
    changeTiem(val) {
      this.form.startSearchTime = moment(this.tiemValue[0]).format('YYYY-MM-DD');
      this.form.endSearchTime = moment(this.tiemValue[1]).format('YYYY-MM-DD');
      console.log(this.form.endSearchTime, this.form.startSearchTime, 'val');
    },
    query() {
      this.loadList();
    },
    reset() {
      this.tiemValue = {};
      this.loadList();
    },
    loadForm() {
      this.form.campusName = this.showList.campusName;
      this.form.dpName = this.showList.dpName;
      this.form.userName = this.showList.userName;
      console.log(this.showList, 'this');
    },
    loadList() {
      const params = {
        // campusId: this.form.searchWord,
        startSearchTime: this.form.startSearchTime ? moment(this.form.startSearchTime).format('YYYY-MM-DD') : '',
        endSearchTime: this.form.endSearchTime ? moment(this.form.endSearchTime).format('YYYY-MM-DD') : '',
        dpId: this.showList.dpId,
        start: this.page.currentPage,
        length: this.page.pageSize
      };
      this.$http.post('/question/report/getAllQuestionUserReport.do', params).then(res => {
        if (res.ok) {
          this.tableData = res.body.data;
          this.page.total = res.body.recordsTotal;
        }
      });
    },
    exportList() {
      var exportUrl = downUri + '/question/report/exportQuestionUser.do?dpId=' + this.showList.dpId + '&startSearchTime=' + this.form.startSearchTime + '&endSearchTime=' + this.form.endSearchTime;
      // 导出文件
      window.location.href = exportUrl;
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      console.log(`每页 ${val} 条`);
      this.loadList();
    },
    handleCurrentChange(val) {
      this.page.currentPage = val;
      console.log(`当前页: ${val}`);
      this.loadList();
    },
    handleEdit(index, row) {
      console.log(row, 'row');
      this.detailsInfo = row;
      this.detailsTitle = '数据报表（个人明细）';
      this.deatailsType = true;
    }
  }
};
</script>

<style>
  .pageClass{
  float: right;
  margin-top: -15px;
}
</style>
