<template>
  <common-dialog
    :show-footer="true"
    is-full
    width="1000px"
    title="审核评语设置"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='form'
        size='mini'
        :model='form'
        :rules="rules"
        label-width='120px'
      >
        <el-form-item label='审核通过评语' prop='passComment'>
          <wang-editor ref="pass" v-model="form.passComment" />
        </el-form-item>

        <el-form-item label='审核未通过评语' prop='faileComment'>
          <wang-editor ref="faile" v-model="form.faileComment" />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validatePass = (rule, value, callback) => {
      if (this.$refs['pass']) {
        const content = this.$refs['pass'].getContent('text').replace(/&nbsp;/ig, '');
        if (content.trim() === '') {
          callback(new Error('请填写审核评语'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const validateFaile = (rule, value, callback) => {
      if (this.$refs['faile']) {
        const content = this.$refs['faile'].getContent('text').replace(/&nbsp;/ig, '');
        if (content.trim() === '') {
          callback(new Error('请填写审核评语'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      show: false,
      form: {
        passComment: '',
        faileComment: ''
      },
      rules: {
        passComment: [
          { required: true, validator: validatePass, trigger: 'change' }
        ],
        faileComment: [
          { required: true, validator: validateFaile, trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {
      this.getInfo();
    },
    getInfo() {
      this.$post('getPhraseInfo')
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            body.forEach(item => {
              if (item.attrName === 'pass') {
                this.form.passComment = item.attrValue;
                this.$refs['pass'].setContent(this.form.passComment);
              }
              if (item.attrName === 'faile') {
                this.form.faileComment = item.attrValue;
                this.$refs['faile'].setContent(this.form.faileComment);
              }
            });
          }
        });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form
          };
          this.$post('updatePhrase', params)
            .then(res => {
              const { fail, body } = res;
              if (!fail) {
                this.show = false;
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
              }
            });
        }
      });
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>

body: {
  pass: {}
  faile: {}
}

