// 本地字典
export default {
  // 会员卡类型
  mebFreeType: [
    { dictName: '免费体验卡', dictValue: 'free' },
    { dictName: '付费卡', dictValue: 'pay' }
  ],
  // 会员卡种类
  mebPayType: [
    { dictName: '年卡', dictValue: 'year' },
    { dictName: '季卡', dictValue: 'season' },
    { dictName: '月卡', dictValue: 'mouth' },
    { dictName: '周卡', dictValue: 'week' }
  ],
  // 抵扣方式
  deductType: [
    { dictName: '智米', dictValue: '1' },
    { dictName: '滞留金', dictValue: '2' }
  ],
  // 抵扣方式
  refundType: [
    { dictName: '现金', dictValue: '1' },
    { dictName: '智米', dictValue: '2' },
    { dictName: '滞留金', dictValue: '3' }
  ],
  // 数据状态
  status: [
    { dictName: '启用', dictValue: '1' },
    { dictName: '禁用', dictValue: '2' }
  ],
  // 上课方式
  courseType: [
    { dictName: '直播', dictValue: '0' },
    { dictName: '录播', dictValue: '1' },
    { dictName: '硬盘推流', dictValue: '2' },
    { dictName: '回放', dictValue: '3' }
  ],
  // 上架类型
  ShelfType: [
    { dictName: '单个套餐', dictValue: '1' },
    { dictName: '组合套餐', dictValue: '2' }
  ],
  // 购买渠道
  platform: [
    { dictName: '微信', dictValue: 'WECHAT' },
    { dictName: 'APP', dictValue: 'APP' }
  ],
  readPlanList: {
    'code': '00',
    'body': {
      'data': [
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': 83,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 1, // 状态 1.启用 2.禁用
          'applyNum': 10, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '李四', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '王五', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        }
      ],
      'recordsTotal': 11,
      'recordsFiltered': 1
    },
    'msg': '',
    'ok': true
  },
  pkChildAct: {
    'cjScore': 0,
    'empId': '1',
    'gkScore': 100,
    'groupId': '',
    'pkActId': null,
    'pkChildId': '1',
    'pkChildName': '',
    'pkChildType': '',
    'startTime': 1626665767000,
    'endTime': 1626665767000,
    'pkRange': '1,2,3',
    'pkType': 1,
    'qrScore': 100,
    'teamId': '',
    'yjScore': 100,
    'zkScore': 100,
    'hwScore': 100
  },
  // 战区
  pkWarList: [
    {
      'groupId': '1',
      'pkChildId': '',
      'pkTeamDetailsVOS': [
        {
          'dpEmpName': '',
          'dpName': '',
          'empId': '9',
          'empName': '王煜宽',
          'jobTitle': '',
          'manPower': 1,
          'pkTeamDetailsVOS': [
            {
              'dpEmpName': '',
              'dpName': '',
              'empId': '4',
              'empName': '刘欣',
              'jobTitle': '',
              'manPower': 1,
              'pkTeamDetailsVOS': [
                {
                  'dpEmpName': '',
                  'dpName': '',
                  'empId': '2',
                  'empName': '吴帆',
                  'jobTitle': '',
                  'manPower': 1,
                  'pkTeamDetailsVOS': [],
                  'supTeamId': '4',
                  'teamAvatar': 'pk/teamAvatar/吴帆.jpg',
                  'teamId': '2',
                  'teamName': '吴帆',
                  'teamType': '1'
                }
              ],
              'supTeamId': '11',
              'teamAvatar': 'pk/teamAvatar/营销组.jpg',
              'teamId': '4',
              'teamName': '营销组',
              'teamType': '2'
            }
          ],
          'supTeamId': '',
          'teamAvatar': 'pk/teamAvatar/营销组.jpg',
          'teamId': '11',
          'teamName': '营销组',
          'teamType': '3'
        },
        {
          'dpEmpName': '',
          'dpName': '',
          'empId': '8',
          'empName': '林建彬',
          'jobTitle': '',
          'manPower': 2,
          'pkTeamDetailsVOS': [
            {
              'dpEmpName': '',
              'dpName': '',
              'empId': '5',
              'empName': '邱鹏',
              'jobTitle': '',
              'manPower': 1,
              'pkTeamDetailsVOS': [
                {
                  'dpEmpName': '',
                  'dpName': '',
                  'empId': '1',
                  'empName': '胡程',
                  'jobTitle': '',
                  'manPower': 1,
                  'pkTeamDetailsVOS': [],
                  'supTeamId': '7',
                  'teamAvatar': 'pk/teamAvatar/胡程.jpg',
                  'teamId': '1',
                  'teamName': '胡程',
                  'teamType': '1'
                }
              ],
              'supTeamId': '10',
              'teamAvatar': 'pk/teamAvatar/信息部.jpg',
              'teamId': '7',
              'teamName': '信息部',
              'teamType': '2'
            },
            {
              'dpEmpName': '',
              'dpName': '',
              'empId': '5',
              'empName': '周超',
              'jobTitle': '',
              'manPower': 1,
              'pkTeamDetailsVOS': [
                {
                  'dpEmpName': '',
                  'dpName': '',
                  'empId': '3',
                  'empName': '',
                  'jobTitle': '',
                  'manPower': 1,
                  'pkTeamDetailsVOS': [],
                  'supTeamId': '6',
                  'teamAvatar': 'pk/teamAvatar/荣欣.jpg',
                  'teamId': '3',
                  'teamName': '荣欣',
                  'teamType': '1'
                }
              ],
              'supTeamId': '10',
              'teamAvatar': 'pk/teamAvatar/后台组.jpg',
              'teamId': '6',
              'teamName': '后台组',
              'teamType': '2'
            }
          ],
          'supTeamId': '',
          'teamAvatar': 'pk/teamAvatar/BBB.jpg',
          'teamId': '10',
          'teamName': 'BBB',
          'teamType': '3'
        }
      ],
      'teamCode': '1V1'
    },
    {
      'groupId': '2',
      'pkChildId': '',
      'pkTeamDetailsVOS': [
        {
          'dpEmpName': '',
          'dpName': '',
          'empId': '9',
          'empName': '王煜宽2',
          'jobTitle': '',
          'manPower': 1,
          'pkTeamDetailsVOS': [
            {
              'dpEmpName': '',
              'dpName': '',
              'empId': '4',
              'empName': '刘欣',
              'jobTitle': '',
              'manPower': 1,
              'pkTeamDetailsVOS': [
                {
                  'dpEmpName': '',
                  'dpName': '',
                  'empId': '2',
                  'empName': '吴帆',
                  'jobTitle': '',
                  'manPower': 1,
                  'pkTeamDetailsVOS': [],
                  'supTeamId': '4',
                  'teamAvatar': 'pk/teamAvatar/吴帆.jpg',
                  'teamId': '2',
                  'teamName': '吴帆',
                  'teamType': '1'
                }
              ],
              'supTeamId': '11',
              'teamAvatar': 'pk/teamAvatar/营销组.jpg',
              'teamId': '4',
              'teamName': '营销组',
              'teamType': '2'
            }
          ],
          'supTeamId': '',
          'teamAvatar': 'pk/teamAvatar/营销组.jpg',
          'teamId': '11',
          'teamName': '营销组',
          'teamType': '3'
        },
        {
          'dpEmpName': '',
          'dpName': '',
          'empId': '8',
          'empName': '林建彬2',
          'jobTitle': '',
          'manPower': 2,
          'pkTeamDetailsVOS': [
            {
              'dpEmpName': '',
              'dpName': '',
              'empId': '5',
              'empName': '邱鹏2',
              'jobTitle': '',
              'manPower': 1,
              'pkTeamDetailsVOS': [
                {
                  'dpEmpName': '',
                  'dpName': '',
                  'empId': '1',
                  'empName': '胡程2',
                  'jobTitle': '',
                  'manPower': 1,
                  'pkTeamDetailsVOS': [],
                  'supTeamId': '7',
                  'teamAvatar': 'pk/teamAvatar/胡程.jpg',
                  'teamId': '1',
                  'teamName': '胡程2',
                  'teamType': '1'
                }
              ],
              'supTeamId': '10',
              'teamAvatar': 'pk/teamAvatar/信息部.jpg',
              'teamId': '7',
              'teamName': '信息部',
              'teamType': '2'
            },
            {
              'dpEmpName': '',
              'dpName': '',
              'empId': '5',
              'empName': '周超2',
              'jobTitle': '',
              'manPower': 1,
              'pkTeamDetailsVOS': [
                {
                  'dpEmpName': '',
                  'dpName': '',
                  'empId': '3',
                  'empName': '',
                  'jobTitle': '',
                  'manPower': 1,
                  'pkTeamDetailsVOS': [],
                  'supTeamId': '6',
                  'teamAvatar': 'pk/teamAvatar/荣欣.jpg',
                  'teamId': '3',
                  'teamName': '荣欣2',
                  'teamType': '1'
                }
              ],
              'supTeamId': '10',
              'teamAvatar': 'pk/teamAvatar/后台组.jpg',
              'teamId': '6',
              'teamName': '后台组2',
              'teamType': '2'
            }
          ],
          'supTeamId': '',
          'teamAvatar': 'pk/teamAvatar/BBB.jpg',
          'teamId': '10',
          'teamName': 'BBB',
          'teamType': '3'
        }
      ],
      'teamCode': '1V1'
    }
  ],
  // 部门
  pkDepartList: [
    {
      'groupId': '',
      'pkChildId': '',
      'pkTeamDetailsVOS': [
        {
          'dpEmpName': '',
          'dpName': '',
          'empId': '4',
          'empName': '刘欣',
          'jobTitle': '',
          'manPower': 1,
          'pkTeamDetailsVOS': [
            {
              'dpEmpName': '',
              'dpName': '',
              'empId': '2',
              'empName': '吴帆',
              'jobTitle': '',
              'manPower': 1,
              'pkTeamDetailsVOS': [],
              'supTeamId': '4',
              'teamAvatar': 'pk/teamAvatar/吴帆.jpg',
              'teamId': '2',
              'teamName': '吴帆',
              'teamType': '1'
            }
          ],
          'supTeamId': '11',
          'teamAvatar': 'pk/teamAvatar/营销组.jpg',
          'teamId': '4',
          'teamName': '营销组',
          'teamType': '2'
        },
        {
          'dpEmpName': '',
          'dpName': '',
          'empId': '5',
          'empName': '邱鹏',
          'jobTitle': '',
          'manPower': 2,
          'pkTeamDetailsVOS': [
            {
              'dpEmpName': '',
              'dpName': '',
              'empId': '1',
              'empName': '胡程',
              'jobTitle': '',
              'manPower': 1,
              'pkTeamDetailsVOS': [],
              'supTeamId': '7',
              'teamAvatar': 'pk/teamAvatar/胡程.jpg',
              'teamId': '1',
              'teamName': '胡程',
              'teamType': '1'
            }
          ],
          'supTeamId': '10',
          'teamAvatar': 'pk/teamAvatar/信息部.jpg',
          'teamId': '7',
          'teamName': '信息部',
          'teamType': '2'
        },
        {
          'dpEmpName': '',
          'dpName': '',
          'empId': '5',
          'empName': '周超',
          'jobTitle': '',
          'manPower': 1,
          'pkTeamDetailsVOS': [
            {
              'dpEmpName': '',
              'dpName': '',
              'empId': '3',
              'empName': '',
              'jobTitle': '',
              'manPower': 1,
              'pkTeamDetailsVOS': [],
              'supTeamId': '6',
              'teamAvatar': 'pk/teamAvatar/荣欣.jpg',
              'teamId': '3',
              'teamName': '荣欣',
              'teamType': '1'
            }
          ],
          'supTeamId': '10',
          'teamAvatar': 'pk/teamAvatar/后台组.jpg',
          'teamId': '6',
          'teamName': '后台组',
          'teamType': '2'
        }
      ],
      'teamCode': ''
    }
  ],
  pkTeamList: [
    {
      'groupId': '',
      'pkChildId': '',
      'pkTeamDetailsVOS': [
        {
          'dpEmpName': '胡程负责人',
          'dpName': '胡程部门',
          'empId': '1',
          'empName': '胡程',
          'jobTitle': '胡程岗位',
          'manPower': 1,
          'pkTeamDetailsVOS': [],
          'supTeamId': '7',
          'teamAvatar': 'pk/teamAvatar/胡程.jpg',
          'teamId': '1',
          'teamName': '胡程',
          'teamType': '1'
        },
        {
          'dpEmpName': '吴帆负责人',
          'dpName': '吴帆部门',
          'empId': '2',
          'empName': '吴帆',
          'jobTitle': '吴帆岗位',
          'manPower': 1,
          'pkTeamDetailsVOS': [],
          'supTeamId': '4',
          'teamAvatar': 'pk/teamAvatar/吴帆.jpg',
          'teamId': '2',
          'teamName': '吴帆',
          'teamType': '1'
        },
        {
          'dpEmpName': '荣欣负责人',
          'dpName': '荣欣部门',
          'empId': '3',
          'empName': '荣欣',
          'jobTitle': '荣欣岗位',
          'manPower': 1,
          'pkTeamDetailsVOS': [],
          'supTeamId': '6',
          'teamAvatar': 'pk/teamAvatar/荣欣.jpg',
          'teamId': '3',
          'teamName': '荣欣',
          'teamType': '1'
        }
      ],
      'teamCode': ''
    }
  ],
  pkjxList: {
    '1': [
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': null,
        'perforType': 1,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 2,
        'payTime': null,
        'perforType': 2,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': null,
        'perforType': 3,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': null,
        'perforType': 4,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 2,
        'payTime': null,
        'perforType': 5,
        'score': -1
      }
    ],
    '2': [
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': 1626492967000,
        'perforType': 1,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': 1626492966000,
        'perforType': 2,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': null,
        'perforType': 3,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': null,
        'perforType': 4,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': null,
        'perforType': 5,
        'score': 1
      }
    ],
    '3': [
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': null,
        'perforType': 1,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': 1625974567000,
        'perforType': 2,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': null,
        'perforType': 3,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': null,
        'perforType': 4,
        'score': 1
      },
      {
        'createTime': null,
        'empId': '1',
        'formanceSource': 1,
        'payTime': null,
        'perforType': 5,
        'score': 1
      }
    ]
  },
  getReadPlanList: {
    'code': '00',
    'body': {
      'createUserId': null,
      'createUserName': null,
      'createTime': null,
      'updateUserId': null,
      'updateUserName': null,
      'updateTime': null,
      'readPlanId': 9, // 读书计划id
      'readPlanName': '读书计划', // 读书计划名称
      'planSummary': '读书计划摘要', // 读书计划摘要
      'learnBaseNum': 1, // 学习基数
      'planDays': 180, // 计划天数
      'actPrice': 2.00, // 活动价格
      'marketPrice': 10.00, // 基本价格
      'refundRule': 'cash,zhimi,retention', // 退费规则
      'purchaseRule': 'zhimi,retention', // 购买规则
      'refundSupport': 1, // 是否支持退费
      'listPic': 'gs/5BEB53A72B1147B39C4C701A5A3D7493.jpg', // 列表图片
      'detailsPic': 'gs/1DECA0FF95AC463EAB745EA236F7DAA0.png', // 详情图片
      'featuredPic': 'gs/1DECA0FF95AC463EAB745EA236F7DAA0.png', // 精选图片
      'clockPic': 'gs/1DECA0FF95AC463EAB745EA236F7DAA0.png', // 如何打卡图片
      'backgroundPic': 'gs/1DECA0FF95AC463EAB745EA236F7DAA0.png', // 背景图片
      'clockText': '要记得打卡呦', // 打卡规则文案
      'shareTitle': '分享我分享我', // 分享标题
      'shareSummary': '康么', // 分享摘要
      'status': 1, // 状态
      'applyNum': 10, // 报名人数
      'menuDirectoryVos': [
        {
          'menuId': 7, // 菜单id
          'readPlanId': null, //
          'menuName': '菜单一', // 菜单名称
          'menuSummary': null, // 菜单摘要
          'sort': 1, // 排序
          'bookNum': null, // 书本数量
          'puMenuBookVOS': [
            {
              'menuId': 7, //
              'bId': null, // 书本id
              'bName': null, // 书本名称
              'sort': 1, // 排序
              'planDay': null// 章节数量
            },
            {
              'menuId': 29, //
              'bId': null, // 书本id
              'bName': null, // 书本名称
              'sort': 1, // 排序
              'planDay': null// 章节数量
            }
          ]
        }, {
          'menuId': 2, // 菜单id
          'readPlanId': null, //
          'menuName': '菜单一', // 菜单名称
          'menuSummary': null, // 菜单摘要
          'sort': 1, // 排序
          'bookNum': null, // 书本数量
          'puMenuBookVOS': [
            {
              'menuId': 7, //
              'bId': null, // 书本id
              'bName': null, // 书本名称
              'sort': 1, // 排序
              'planDay': null// 章节数量
            },
            {
              'menuId': 29, //
              'bId': null, // 书本id
              'bName': null, // 书本名称
              'sort': 1, // 排序
              'planDay': null// 章节数量
            }
          ]
        }
      ],
      'listPicFile': null,
      'detailsPicFile': null,
      'featuredPicFile': null,
      'clockPicFile': null,
      'backgroundPicFile': null
    },
    'msg': '',
    'ok': true
  }
};

