<template>
  <el-col :span="4">
    <el-tree
      ref="elTree"
      :data="data"
      node-key="id"
      :props="props"
      :highlight-current="true"
      :expand-on-click-node="false"
      :render-content="renderContent"
      :draggable="draggable"
      :default-expand-all="false"
      :default-expanded-keys="defaultExpandedKeys"
      :allow-drop="allowDrop"
      :indent="12"
      @node-drop="nodeDrop"
    />
    <!--  :default-expanded-keys="defaultExpandedKeys" -->
  </el-col>
</template>

<script>
export default {
  components: {},
  props: {
    isShowMore: {
      type: Boolean,
      default: false
    },
    draggable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      data: [],
      props: {
        label: 'name'
      },
      breadList: [],
      modifyNameDialogVisible: false,
      branchDialogVisible: false,
      defaultExpandedKeys: []
    };
  },
  created() {
    this.getProblemTreeList();
  },
  methods: {
    // 获取菜单树数据
    getProblemTreeList() {
      this.$http.get('/question/branch/selectQuestionBranchListGroupParentIdInclusionParent.do')
        .then(res => {
          if (res.ok) {
            this.data = res.body;
            // 默认选中第一个节点
            this.$emit('problemInfoList', res.body[0].id);
            this.handleSetCurrentKey(res.body[0].id);
            this.breadList = [];
            this.$nextTick(() => {
              this.defaultExpandedKeys = [res.body[0].id];
              this.getTreeNode(this.$refs.elTree.getNode(res.body[0].id));
              this.$emit('handleCrumbs', this.breadList);
            });
          }
        });
    },
    renderContent(h, { node, data, store }) {
      const sty = {
        paddingBottom: '5px',
        paddingTop: '5px',
        cursor: 'pointer'
      };
      return (
        <div class='custom-tree-node'>
          <div class='label' onClick={() => this.handleMenu(data.id)}>{node.label}</div>
          {
            this.isShowMore ? (
              <el-popover
                placement='right'
                width='150'
                trigger='hover'
              >
                { node.level !== 5 && <div onClick={() => this.addSon('add', data)} style={sty}><i class='el-icon-plus' />新建子级菜单</div> }
                <div onClick={() => this.addCharge(data)} style={sty}><i class='el-icon-user' />设置负责人</div>
                { node.level !== 1 && <div onClick={() => this.modifyName('edit', data)} style={sty}><i class='el-icon-edit-outline' />修改名称</div> }
                { node.level !== 1 && <div onClick={() => this.del(data) } style={sty}><i class='el-icon-delete' />删除</div> }
                { node.previousSibling && <div onClick={() => this.moveUp(node) } style={sty}><i class='el-icon-top' />上移</div> }
                { node.nextSibling && <div onClick={() => this.moveDown(node) } style={sty}><i class='el-icon-bottom' />下移</div> }
                <el-button
                  size='mini'
                  type='text'
                  onClick={(e) => this.moreHandle(e)}
                  style='margin-top:-6px;width:20px'
                  slot='reference'
                >···</el-button>
              </el-popover>
            ) : ''
          }
        </div>
      );
    },
    // 节点更多
    moreHandle() {},
    // 点击节点
    handleMenu(id) {
      this.$emit('problemInfoList', id);
      this.breadList = [];
      this.getTreeNode(this.$refs.elTree.getNode(id));
      this.$nextTick(() => {
        this.$emit('handleCrumbs', this.breadList);
      });
    },
    handleSetCurrentKey(key) {
      this.$nextTick(() => {
        this.$refs.elTree.setCurrentKey(key);
      });
    },
    getTreeNode(node) {
      // 获取当前树节点和其父级节点
      if (node) {
        if (node.label !== undefined) {
          this.breadList.unshift({
            name: node.label,
            id: node.key,
            isLeaf: node.isLeaf
          });
          this.getTreeNode(node.parent); // 递归
        }
      }
    },
    allowDrop(draggingNode, dropNode, type) {
      if (draggingNode.level !== dropNode.level) {
        return;
      }
      if (type === 'inner') {
        return;
      }
      return type !== 'inner';
    },
    nodeDrop(before, after) {
      const currentId = before.data.id;
      const currentSort = before.data.sort;
      const afterId = after.data.id;
      const afterSort = after.data.sort;
      const params = [
        {
          id: currentId,
          sort: afterSort
        },
        {
          id: afterId,
          sort: currentSort
        }
      ];
      this.$http.post('/question/branch/updateBatchQuestionBranchById.do', params, { json: true }).then(res => {
        if (res.ok) {
          this.$message.success('移动成功');
          this.getProblemTreeList();
        } else {
          this.$message.error('移动失败');
        }
      });
    },
    // 添加分支
    addSon(type, data) {
      this.$parent.$parent.newBranch = data;
      this.$parent.$parent.type = type;
      this.$parent.$parent.title = '添加分支';
      this.$parent.$parent.addSonDialogVisible = true;
    },
    // 设置负责人
    addCharge(data) {
      this.$parent.$parent.newBranch = data;
      this.$parent.$parent.addChargeDialogVisible = true;
    },
    // 修改分支
    modifyName(type, data) {
      this.$parent.$parent.newBranch = data;
      this.$parent.$parent.type = type;
      this.$parent.$parent.title = '修改分支';
      this.$parent.$parent.addSonDialogVisible = true;
    },
    // 删除
    del(data) {
      this.$confirm('是否删除', '删除分支', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.post('/question/branch/deleteById.do', { id: data.id }).then(res => {
          if (res.ok) {
            // 删除当前节点
            this.$refs.elTree.remove(data);
            this.$message.success('删除成功');
          } else {
            this.$message.error(res.msg);
          }
        });
      }).catch(() => {
        // this.$message({
        //   type: 'info',
        //   message: '已取消删除'
        // });
      });
    },
    // 上移
    moveUp(node) {
      const currentId = node.data.id;
      const currentSort = node.data.sort;
      const previousSiblingId = node.previousSibling.data.id;
      const previousSiblingSort = node.previousSibling.data.sort;
      const params = [
        {
          id: currentId,
          sort: previousSiblingSort
        },
        {
          id: previousSiblingId,
          sort: currentSort
        }
      ];
      this.$http.post('/question/branch/updateBatchQuestionBranchById.do', params, { json: true }).then(res => {
        if (res.ok) {
          this.$message.success('移动成功');
          const parentChildrenData = node.parent.data.children;
          const index = parentChildrenData.findIndex(d => d.id === node.data.id);
          const temp1 = parentChildrenData[index - 1];
          const temp2 = parentChildrenData[index];
          // 视图更新
          this.$set(parentChildrenData, index, { ...temp1, sort: temp2.sort });
          this.$set(parentChildrenData, index - 1, { ...temp2, sort: temp1.sort });
        } else {
          this.$message.error('移动失败');
        }
      });
    },
    // 下移
    moveDown(node) {
      const currentId = node.data.id;
      const currentSort = node.data.sort;
      const nextSiblingId = node.nextSibling.data.id;
      const nextSiblingSort = node.nextSibling.data.sort;
      const params = [
        {
          id: currentId,
          sort: nextSiblingSort
        },
        {
          id: nextSiblingId,
          sort: currentSort
        }
      ];
      this.$http.post('/question/branch/updateBatchQuestionBranchById.do', params, { json: true }).then(res => {
        if (res.ok) {
          this.$message.success('移动成功');
          const parentChildrenData = node.parent.data.children;
          const index = parentChildrenData.findIndex(d => d.id === node.data.id);
          const temp1 = parentChildrenData[index];
          const temp2 = parentChildrenData[index + 1];
          // 视图更新
          this.$set(parentChildrenData, index, { ...temp2, sort: temp1.sort });
          this.$set(parentChildrenData, index + 1, { ...temp1, sort: temp2.sort });
        } else {
          this.$message.error('移动失败');
        }
      });
    }
    // partialRefreshpartialRefresh(node) {
    //   // 设置loaded为false；模拟一次节点展开事件，加载重命名后的新数据；
    //   node.loaded = false;
    //   node.expand();
    //   // 新建子节点是刷新一次本节点的展开请求，而重命名和删除则需要刷新父级节点的的展开事件，
    //   // 可以设置node.parent.loaded = false;node.parent.expand();
    // }
  }
};
</script>

<style lang = 'scss' scoped>
::v-deep .el-tree {
  border-radius: 9px;
  overflow: scroll;
  padding: 10px 0 0 10px;
  max-height: 93vh;

  &::-webkit-scrollbar {
    width: 8px;
    background-color: #fff;
  }
  &:hover ::-webkit-scrollbar-track-piece {
    /*鼠标移动上去再显示滚动条*/
    background-color: #fff;
    /* 滚动条的背景颜色 */
    border-radius: 6px;
    /* 滚动条的圆角宽度 */
  }
  &:hover::-webkit-scrollbar-thumb:hover {
    background-color: #c0cecc;
  }
  &:hover::-webkit-scrollbar-thumb:vertical {
    background-color: #c0cedc;
    border-radius: 6px;
    outline: 2px solid #fff;
    outline-offset: -2px;
    border: 2px solid #fff;
  }
  .el-tree-node__content {
    height: auto;
    padding-top: 6px;
    padding-bottom: 6px;
  }
}
::v-deep .custom-tree-node {
  display: flex;
  /* align-items: center; */
  justify-content: space-between;
  width: calc(100% - 24px);
  .label {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 90%;

    /* overflow: hidden;
      white-space: normal;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical; */
  }
}
</style>
