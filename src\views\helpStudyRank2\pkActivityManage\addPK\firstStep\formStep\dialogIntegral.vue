<template>
  <common-dialog width="50%" class="add_integral" title="积分设置" :visible.sync="visible" @open="integralInit" @close="integralClose">
    <!-- 按钮 -->
    <div class="add_integral-btn">
      <el-button type="success" @click="integralDownload">下载PK商品</el-button>
      <el-button type="primary" @click="openIntExport">excel导入积分</el-button>
      <el-button type="primary" @click="integralSubmit">提交</el-button>
    </div>
    <!-- 表格 -->
    <el-form ref="integralRef" class="add_integral-main" size="mini" :model="integralFrom" :rules="integralRule">
      <el-table border size="small" :data="integralFrom.integralData" header-cell-class-name='table-cell-header'>
        <el-table-column label="商品ID" align="center" prop="joinId" />
        <el-table-column label="商品名称" align="center" prop="joinName" />
        <el-table-column label="积分" align="center" prop="score">
          <template slot-scope="scope">
            <el-form-item label='' :prop="`integralData.${scope.$index}.score`" :rules="integralRule.score(scope.$index)">
              <el-input v-model="scope.row.score" type="text" @input="(v)=>(scope.row.score=v.replace(/[^\d.]/g,''))" />
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <!-- 弹窗 - excel导入积分 -->
    <importIntegra :visible.sync="intShow" :name="intPath" @close="closeIntExport" />
  </common-dialog>
</template>

<script>
import importIntegra from './import-integra';

export default {
  components: { importIntegra },
  props: {
    visible: { type: Boolean, default: true },
    pkdata: { type: Array, default: () => [] }
  },
  data() {
    return {
      intShow: false,
      intPath: '',
      integralFrom: {
        integralData: []
      },
      integralRule: {
        score: (ins) => [{
          required: true,
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (value === '' || value === undefined) {
              callback(new Error('请输入'));
              return;
            }
            callback();
          }
        }]
      }
    };
  },
  methods: {
    // 初始化
    integralInit() {
      this.integralFrom = {
        integralData: JSON.parse(JSON.stringify(this.pkdata))
      };
    },
    // 下载PK商品
    integralDownload() {
      const newsarr = [['商品ID', '商品名称', '积分']];
      const dwnData = JSON.parse(JSON.stringify(this.integralFrom?.integralData));
      if (!dwnData.length) {
        this.$message({ type: 'warning', message: '请添加积分数据！' });
        return;
      }
      dwnData.forEach((item, inx) => {
        newsarr[inx + 1] = [item.joinId, item.joinName, item.score];
      });
      const XLSX = require('xlsx');
      try {
        const ws = XLSX.utils.aoa_to_sheet(newsarr);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
        XLSX.writeFile(wb, '下载PK商品.xlsx');
        this.$message({ type: 'success', message: '导出成功！' });
      } catch (error) {
        this.$message({ type: 'error', message: '导出失败！' });
      }
    },
    // 打开导入积分弹窗
    openIntExport() {
      this.intShow = true;
    },
    // 导入积分设置成功（只有覆盖）
    closeIntExport(obs) {
      if (obs) {
        const data = obs.data || [];
        this.intPath = obs.name;
        const errTip = [];
        const res = [];
        if (data && data.length) {
          // 取值赋值：商品ID、商品名称、积分
          data.forEach((item, inx) => {
            const target = { score: 0 };
            for (const key in item) {
              if (key == '商品ID') {
                target.joinId = item[key];
              } else if (key == '商品名称') {
                target.joinName = item[key];
                // 职业教育的转换
                switch (item[key]) {
                  case '成教':
                    target.shopKey = 'cjScore';
                    break;
                  case '国开':
                    target.shopKey = 'gkScore';
                    break;
                  case '全日制':
                    target.shopKey = 'qrScore';
                    break;
                  case '自考':
                    target.shopKey = 'zkScore';
                    break;
                  case '研究生':
                    target.shopKey = 'yjScore';
                    break;
                  case '海外教育':
                    target.shopKey = 'hwScore';
                    break;
                  default:
                    target.shopKey = '';
                    break;
                }
              } else if (key == '积分') {
                target.score = Number(item[key]);
              }
            }
            if (!target.joinId) {
              errTip.push(`第${inx + 2}行，商品ID填写有误。`);
              return;
            }

            if (!target.joinName) {
              errTip.push(`第${inx + 2}行，「商品名称」填写有误。`);
              return;
            }

            if (isNaN(Number(target.score))) {
              errTip.push(`第${inx + 2}行，「积分」填写有误。`);
              return;
            }

            res.push(target);
          });
          /*
            * 特殊处理：判断是否含有职业教育和PK范围
            * 有，则正常结束
            * 没有，则默认加入
            * 最后做去重处理
            */
          if (this.pkdata.length && res.length) {
            this.integralFrom.integralData = [...this.pkdata, ...res].reduce((olds, news) => {
              const _inx = olds.findIndex(item => item.joinId === news.joinId);
              if (_inx === -1) olds.push(news);
              else olds[_inx] = news;
              return olds;
            }, []);
            console.log(this.integralFrom.integralData);
          }
          // 提示
          if (res.length && !errTip.length) {
            this.$message.success(`成功导入 ${res.length} 条数据`);
          } else {
            const content = `<strong><span style="color: #67C23A;">成功导入${res.length}条数据。</span></strong><br>
              <span style="color: #f78989;">导入失败${errTip.length}条数据：</span>
              <br>${errTip.join('<br>')}`;
            this.$alert(content, '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定'
            });
          }
        } else this.$message.error('导入失败，请按照模板填写正确数据');
      }
      this.intShow = false;
    },
    // 提交：添加积分表单
    integralSubmit() {
      const { integralData } = JSON.parse(JSON.stringify(this.integralFrom));
      if (!integralData?.length) {
        this.$message({ type: 'warning', message: '请添加积分数据！' });
        return false;
      }
      this.$refs['integralRef'].validate((valid) => {
        if (valid) {
          // 提交
          this.$emit('close', integralData);
          // 归零
          setTimeout(() => {
            this.integralFrom = { integralData: [] };
          }, 10);
        }
      });
    },
    // 关闭弹窗
    integralClose() {
      this.intPath = '';
      this.integralFrom = { integralData: [] };
      this.$emit('close');
      this.$refs['integralRef'].resetFields();
    }
  }
};
</script>

<style lang="scss">
  .add_integral {
    &-btn {
      margin: 20px;
    }
    &-main {
      margin: 20px;
      min-height: 15px;
      .el-form-item {
        margin-bottom: 0;
      }
      .el-input__inner {
        text-align: center;
      }
    }
    .yz-common-dialog__footer {
      text-align: center;
    }
  }
</style>

