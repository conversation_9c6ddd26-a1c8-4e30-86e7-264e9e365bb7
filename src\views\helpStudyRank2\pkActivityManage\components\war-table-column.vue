<template>
  <el-table-column
    :label="label"
    :prop="prop"
    align="center"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <template slot-scope="scope">
      <div :class="{'totalColor': scope.row.isTotalRow}">
        {{ scope.row[prop] }}
      </div>
    </template>
  </el-table-column>
</template>

<script>
export default {
  props: {
    prop: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    }
  }
};
</script>

<style lang = "scss" scoped>
.totalColor {
  color: #f69;
  font-weight: 500;
}
</style>
