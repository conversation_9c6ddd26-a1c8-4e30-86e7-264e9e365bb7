<template>
  <div class="editor-list">
    <div v-for="(item, index) in list" :key="index">
      <anchored-heading :id="item.id" v-model="item.title" :level="item.level" />
      <wang-editor :content.sync="item.content" :menus="['image']" />
      <editor :tree="item.children" />
    </div>
  </div>
</template>

<script>
import AnchoredHeading from './anchored-heading';
import wangEditor from './wang-editor';
export default {
  name: 'Editor',
  components: {
    AnchoredHeading,
    wangEditor
  },
  props: {
    tree: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      list: this.tree,
      editors: []
    };
  },
  watch: {
    tree(value) {
      this.list = value;
    }
  },
  mounted() {},
  methods: {
  }
};
</script>

<style lang='scss' scoped>
.editor-list {
  .editor-container {
    position: relative;
    z-index: 10000;
    .placeholder {
      position: absolute;
      z-index: -1;
      user-select: none;
    }
  }

  ::v-deep .w-e-text p,
  .w-e-text h1,
  .w-e-text h2,
  .w-e-text h3,
  .w-e-text h4,
  .w-e-text h5,
  .w-e-text table,
  .w-e-text pre {
    margin: 13px 0;
    text-indent: 2em;
    color: #606266;
  }

  ::v-deep .w-e-text {
    color: #606266;
  }

  ::v-deep .w-e-text-container .placeholder {
    left: 2.2em;
  }

}
</style>
