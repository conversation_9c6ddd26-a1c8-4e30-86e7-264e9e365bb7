<template>
  <el-dialog
    :visible.sync="show"
    append-to-body
    width="375px"
    :before-close="handleClose"
  >
    <div class="template-pop">
      <div class="template-box">
        <h3>公众号课前提醒模板</h3>
        <div class="template-content">
          <div class="title">课前提醒</div>
          <p class="title-tip">学习充电时间到啦！请同学们认真学习，一起做个上进 的好青年。</p>
          <div class="template-item mt18">
            <div class="label">课程名称：</div>
            <div class="value">马克思主义基本原理概论</div>
          </div>
          <div class="template-item">
            <div class="label">上课时间：</div>
            <div class="value">2022-11-23 08:00 至 24:00</div>
          </div>
          <div class="template-item">
            <div class="label">上课地点：</div>
            <div class="value">
              1、远智教育微信公众号--远智学堂--我的课表。2、电脑登录远智教育官网--远智学堂--我的课表。<br />
              温馨提示：账号是:[STDNO]，密码是ouchn@2021或Ouchn@2021，点击查看详情查看操作指引。
            </div>
          </div>
          <div class="template-item">
            <div class="label">联系电话：</div>
            <div class="value">400-833-6013</div>
          </div>
          <div class="template-foot">
            <span>查看详情</span>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      <!-- <div class="template-box">
        <h3>APP课前提醒模板</h3>
        <div class="template-content">
          <div class="title">课前提醒</div>
          <p class="title-tip">22-11-22</p>
          <div class="template-item mt18">
            <div class="value" style="margin-left:0">
              课程名称：《马克思主义基本原理概论》上课时间：2022-11-23 08:00 至 24:00。上课地点：①远智教育微信公众号--远智学堂--我的课表。②电脑登录远智教育官网--远智学堂--我的课表。③次数越多，平时(期末)成绩越高喔。联系电话：400-833-6013。备…
            </div>
          </div>
          <div class="template-foot">
            <span>查看详情</span>
            <i class="el-icon-arrow-right" />
          </div>
          <img class="unread" src="../../../assets/imgs/guokai/unread.png" alt="">
        </div>
      </div> -->
    </div>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {},
    submit() {},
    handleClose() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

::v-deep .el-dialog{
  min-width: 370px;
  border-radius: 8px;

  // position: relative;
  // &::before{
  //   position:absolute;
  //   content: "";
  //   top: 0;
  //   display: block;
  //   height: 100%;
  //   width: 1px;
  //   text-align: center;
  //   left: 50%;
  //   background-color: #D6D6D6;
  //   opacity: 0.5;
  // }
}
::v-deep .el-dialog__body{
  padding: 0px 0px 30px;
  color: #000;
  border-radius: 8px;
  // position: relative;
  // &::before{
  //   position:absolute;
  //   content: "";
  //   top: 0;
  //   display: block;
  //   height: 100%;
  //   width: 1px;
  //   text-align: center;
  //   left: 50%;
  //   background-color: #D6D6D6;
  // }
}

.template-pop{
  display: flex;
  justify-content: space-around;
  .template-box{
    padding: 0 20px;
    width: 375px;
    h3{
      font-size: 18px;
      color: #333333;
      text-align: center;
    }
    .template-content{
      position: relative;
      padding: 14px 16px;
      border:5px solid #f3f3f3;
      border-radius: 12px;
      .title{
        font-weight: 600;
        font-size: 17px;
      }
      .title-tip{
        margin-top: 6px;
        font-size: 14px;
        color: #666666;
      }
      .template-item{
        display: flex;
        margin-top: 15px;
        font-size: 14px;
        .label{
          text-align: right;
          width: 70px;
          flex-shrink: 0;
          color: #666666;
        }
        .value{
          margin-left: 15px;
          line-height: 20px;
          color: #333333;
        }
      }
      .mt18{
        margin-top: 18px;
      }
      .template-foot{
        margin-top: 16px;
        padding-top: 16px;
        display: flex;
        border-top: 1px solid #EBEBEB;
        justify-content: space-between;
        span{
          font-size: 12px;
        }
      }
      .unread{
        position:absolute;
        top: 0;
        right: 16px;
        width: 24px;
        height: 24px;
      }
    }

  }
}
</style>
