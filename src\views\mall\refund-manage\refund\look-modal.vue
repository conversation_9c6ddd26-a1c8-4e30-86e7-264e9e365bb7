<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    :visible.sync="show"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <el-descriptions title="订单明细:" direction="vertical" :column="11" border>
        <el-descriptions-item label="订单号">{{ form.orderId }}</el-descriptions-item>
        <el-descriptions-item label="商品id">{{ form.productId }}</el-descriptions-item>
        <el-descriptions-item label="商品名称">{{ form.productName }}</el-descriptions-item>
        <el-descriptions-item label="商品类型">{{ form.productType | goodsTypeEnum }}</el-descriptions-item>
        <el-descriptions-item label="规格名称">{{ form.productSpecName }}</el-descriptions-item>
        <el-descriptions-item label="商品单价（元）">{{ form.marketPrice }}</el-descriptions-item>
        <el-descriptions-item label="购买数量">{{ form.amount }}</el-descriptions-item>
        <el-descriptions-item label="商品总价（元）">{{ form.totalPrice }}</el-descriptions-item>
        <el-descriptions-item label="优惠券抵扣（元）">-{{ form.couponDiscount }}</el-descriptions-item>
        <el-descriptions-item label="运费（元）">{{ form.freightAmount }}</el-descriptions-item>
        <el-descriptions-item label="下单用户信息">
          <p>远智编号: {{ form.yzCode }}</p>
          <p>真实姓名: {{ form.realName }}</p>
          <p>手机号: {{ form.mobile }}</p>
        </el-descriptions-item>
        <el-descriptions-item label="下单时间">{{ form.orderTime }}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="退款详情:" class="mt10" direction="vertical" :column="6" border>
        <el-descriptions-item label="智米抵扣（元）">{{ form.zmDiscount }}</el-descriptions-item>
        <el-descriptions-item label="余额抵扣（元）">{{ form.balanceDiscount }}</el-descriptions-item>
        <el-descriptions-item label="现金支付（元）">{{ form.paymentPrice }}</el-descriptions-item>
        <el-descriptions-item label="智米退款金额（元）">{{ form.refundZm }}</el-descriptions-item>
        <el-descriptions-item label="余额退款金额（元）">{{ form.refundBalance }}</el-descriptions-item>
        <el-descriptions-item label="现金退款金额（元）">{{ form.refundCash }}</el-descriptions-item>
      </el-descriptions>

      <el-form
        ref="formRef"
        :model="form"
        label-width="90px"
        size="small"
      >
        <el-form-item label="退款方式:" class="mt10">
          <span>原路退回</span>
        </el-form-item>

        <el-form-item label="退款原因:">
          <span>{{ form.refundReason }}</span>
        </el-form-item>

        <el-form-item label="审核进度:">
          <el-steps v-if="form.checkRecords" :active="form.steps" align-center finish-status="success">
            <!-- 申请人 -->
            <el-step :title="'申请：' + form.checkRecords[0].checkUser" :description="'申请时间：' + form.checkRecords[0].checkTime" />

            <!-- 审核 refundApprovalStatus => 【2:待审核，3:审核通过，4:审核驳回】 -->
            <el-step v-if="form.refundApprovalStatus == 2" title="未审批" status="wait" />
            <el-step v-else :title="'审批：' + form.checkRecords[1].checkUser" :status="form.refundApprovalStatus == 3 ? 'success' : 'error'">
              <template v-slot:description>
                <p>审批时间：{{ form.checkRecords[1].checkTime }}</p>
                <p v-if="form.refundApprovalStatus == 4" style="color: #f00">驳回原因：{{ form.checkRecords[1].reason }}</p>
              </template>
            </el-step>

            <!-- 退款 refundStatus =>退款状态 【0:未退款, 1:退款成功, 2:退款失败】==> 根据这个值展示审核进度 -->
            <el-step v-if="form.refundStatus == 0" title="未退款" status="wait" />
            <el-step v-else-if="form.refundStatus == 1" title="退款成功" status="success" />
            <el-step v-else-if="form.refundStatus == 2" title="退款失败" status="error" />
          </el-steps>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { goodsType } from '../../type';
import { arrToEnum } from '@/utils';
const goodsTypeEnum = arrToEnum(goodsType);

export default {
  filters: {
    goodsTypeEnum(val) {
      return goodsTypeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      show: false,
      form: {}
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框
    open() {
      if (this.currentId) {
        this.$http.get(`/mallRefund/getById/${this.currentId}`).then(res => {
          const { fail, body } = res;
          if (!fail) {
            const form = body;

            let steps = null;
            if (form.checkRecords.length == 1) {
              steps = 2;
            } else {
              steps = 3;
            }
            form.steps = steps;
            this.form = form;
          }
        });
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang = "scss" scoped>
</style>
