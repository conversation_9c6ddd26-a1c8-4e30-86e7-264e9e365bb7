<template>
  <div class="yz-base-container">
    <div style="float:right;margin-bottom:15px">
      <el-button type="primary" @click="exportList">导出</el-button>
    </div>
    <el-table ref="multipleTable" :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column prop="topicName" label="答疑专区话题" align="center" />
      <el-table-column prop="empName" label="教师" align="center" />
      <el-table-column prop="totalCount" label="所有帖子数量" align="center" />
      <el-table-column prop="unrepliedCount" label="未回复数量" align="center" />
    </el-table>
    <div class="page">
      <el-pagination layout="total, sizes, prev, pager, next, jumper" :total="page.total" :page-sizes="[10, 20, 30, 40]" :page-size="page.pageSize" :current-page="currentPage" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script>
import { bmsURL } from '@/config/request';
export default {
  data() {
    return {
      tableData: [],
      currentPage: 1,
      page: {
        total: 1,
        pageNum: 1,
        pageSize: 10
      },
      multipleSelection: []
    };
  },
  created() {
    this.list();
  },
  methods: {
    list() {
      const params = {
        pageNum: this.page.pageNum,
        pageSize: this.page.pageSize
      };
      this.$http.post('/qaMessage/list', params, { json: true }).then(res => {
        if (res.ok) {
          this.tableData = res.body.data;
          this.page.total = res.body.recordsTotal;
        }
      });
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.list();
    },
    handleCurrentChange(val) {
      this.page.pageNum = val;
      this.list();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    exportList() {
      if (this.multipleSelection.length > 0) {
        let businessIds = '';
        this.multipleSelection.forEach(item => {
          businessIds += item.businessId + ',';
        });
        businessIds = businessIds.slice(0, -1);
        window.open(bmsURL + `/qaMessage/export?businessIds=${businessIds}`);
        // this.$http.post(`/qaMessage/export?businessIds=${businessIds}`).then(res => {
        //   if (res.ok) {
        //     console.log(res, 'pp');
        //   }
        // });
      } else {
        this.$message({ message: '请选择导出数据', type: 'error' });
      }
    }
  }
};
</script>

<style>
.page{
  display: flex;
  justify-content: center;
  margin-top: 15px;
}
</style>
