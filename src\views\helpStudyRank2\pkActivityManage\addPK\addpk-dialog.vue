<template>
  <common-dialog
    :title="title"
    width="50%"
    height="50%"
    :visible.sync='show'
    @close='showParentType(1)'
  >
    <div class="main">
      <p>请选择pk方式，添加pk人员</p>
      <div class="pkBox">
        <div class="war" @click="openSelectModel(3)"></div>
        <div class="personal" @click="openSelectModel(1)"></div>
        <div class="department" @click="openSelectModel(2)"></div>
      </div>
      <!-- 添加 -->
      <firstAddPk :title="addTitle" :addType.sync="addType" :visible.sync="addVisible" @showParent='showParentType' />
    </div>
  </common-dialog>
</template>

<script>
import firstAddPk from './firstStep';

export default {
  components: { firstAddPk },
  props: {
    title: { type: String, default: '新增' },
    visible: { type: Boolean, default: false }
  },
  data() {
    return {
      show: false,
      addTitle: '',
      addType: '', // 1:个人, 2:部门, 3:战队
      addVisible: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开对应添加模式
    openSelectModel(type) {
      this.addVisible = true;
      this.addType = String(type);
    },
    // 关闭添加弹窗
    showParentType(ins = 0) {
      this.addVisible = false;
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      if (ins) this.$emit('close');
      else this.$parent.getTableList();
    }
  }
};
</script>

<style lang = "scss" scoped>
.main{
  padding: 80px 80px 100px;
  p{
    font-size: 16px;
    margin-bottom: 40px;
  }
  .pkBox{
    display: flex;
    justify-content: space-between;
    div{
      width: 220px;
      display: flex;
    }
    .war{
      background: url('~@/assets/imgs/helpStudyPkRank/warPK.png') no-repeat;
      background-size: contain;
      transition: all 0.2s ease-in;
      box-shadow: 0 0 30px rgba(0,0,0,0.15);
    }
    div:after {
      content: '';
      padding-top: 100%; // 通过计算来设置高度相对于宽度的百分比
    }
    .war:hover,.personal:hover,.department:hover{
      -webkit-box-shadow: 0 0 30px rgba(0,0,0,0.1);
      box-shadow: 0 0 30px rgba(0,0,0,0.3);
      -webkit-transform: translate3d(0, 0px, 3px);
      transform: translate3d(0, -5px, 3px);
    }
    .personal{
      background: url('~@/assets/imgs/helpStudyPkRank/personalPK.png') no-repeat;
      background-size: contain;
      transition: all 0.2s ease-in;
      box-shadow: 0 0 30px rgba(0,0,0,0.15);
    }
    .department{
      background: url('~@/assets/imgs/helpStudyPkRank/departmentPK.png') no-repeat;
      background-size: contain;
      transition: all 0.2s ease-in;
      box-shadow: 0 0 30px rgba(0,0,0,0.15);
    }
    div:nth-child(2){
      margin: 0 20px;
    }
  }
}
</style>

