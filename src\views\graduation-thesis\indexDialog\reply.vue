<template>
  <common-dialog
    is-full
    width="850px"
    title="答辩结果"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='form'
        size='mini'
        :model='form'
        :rules="rules"
        label-width='100px'
      >
        <el-form-item label='答辩结果' prop='status'>
          <el-radio-group v-model="form.status">
            <el-radio label="1">通过</el-radio>
            <el-radio label="0">不通过</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label='通知内容' prop='content' style="display: block">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="6"
            placeholder="请输入答辩结果具体内容"
          />
        </el-form-item>

        <el-form-item>
          <el-alert
            title="学员已关注远智教育公众号的情况下会接收到推送"
            type="info"
            :closable="false"
          />
          <el-button class="mt-10" type="primary" @click="submitForm('form')">提交</el-button>
        </el-form-item>

      </el-form>

      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="status" label="答辩结果" align="center">
          <template slot-scope="scope">
            {{ scope.row.status === '1' ? '未通过' : '通过' }}
          </template>
        </el-table-column>
        <el-table-column prop="content" label="通知内容" align="center" />
        <el-table-column prop="replyTime" label="通知时间" align="center" />
      </el-table>

    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: [],
      form: {
        status: '0',
        content: ''
      },
      rules: {
        status: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {
      this.getInfo();
    },
    // 获取答辩结果信息
    getInfo() {
      this.tableLoading = true;
      const params = {
        learnId: this.row.learnId
      };
      this.$post('getReplyRecord', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableLoading = false;
            this.tableData = body;
          }
        });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            learnId: this.row.learnId,
            ...this.form
          };
          this.$post('updateReplyResult', params)
            .then(res => {
              const { fail, body } = res;
              if (!fail) {
                if (body) {
                  this.$message({
                    message: '操作成功',
                    type: 'success'
                  });
                  this.getInfo();
                }
              }
            });
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.mt-10 {
  margin-top: 10px;
}

::v-deep .el-table td {
  padding: 8px 0 !important;
}
</style>
