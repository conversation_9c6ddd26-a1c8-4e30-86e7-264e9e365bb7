<template>
  <common-dialog
    show-footer
    :title="title"
    width="800px"
    :visible.sync='show'
    @open="init"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="activityForm"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='活动编码' prop='pkActCode'>
          <el-input v-model="form.pkActCode" placeholder="自动生成" disabled />
        </el-form-item>

        <el-form-item label='活动名称' prop='pkActName'>
          <el-input
            v-model="form.pkActName"
            placeholder="请输入"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="活动描述" prop="description">
          <el-input
            v-model="form.description"
            rows="5"
            maxlength="200"
            show-word-limit
            type="textarea"
          />
        </el-form-item>

        <el-form-item label="奖励规则描述" prop="rule">
          <el-input
            v-model="form.rule"
            rows="5"
            maxlength="500"
            show-word-limit
            type="textarea"
          />
        </el-form-item>

        <el-form-item label="活动开始时间" prop="startTime">
          <el-date-picker
            v-model="form.startTime"
            type="datetime"
            placeholder="选择日期"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item label="活动截止时间" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            class="date_picker"
            type="datetime"
            placeholder="选择日期"
            style="width: 100%;"
            :picker-options="timeOptions"
            :disabled='isStartTimeShow'
          />
        </el-form-item>

        <el-form-item label="活动文件" prop="fileUrl">
          <el-input
            v-model="form.pkAttrFileName"
            v-width="400"
            placeholder=""
            disabled
          />
          <el-upload
            class="upload"
            action="/file/webuploader.do"
            :before-remove="beforeRemove"
            :on-success="uploadSuccess"
            multiple
            :limit="1"
            accept=".pdf,.png"
            :show-file-list="false"
            :on-exceed="handleExceed"
            :file-list="fileList"
            :on-change="onChange"
          >
            <el-button size="mini" type="primary">选择文件</el-button>
          </el-upload>
        </el-form-item>

        <el-form-item label='团队营销地图是否开启' prop='sendMap'>
          <el-radio-group v-model="form.sendMap">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label='是否启用' prop='enable'>
          <el-radio-group v-model="form.enable">
            <el-radio label="1">是</el-radio>
            <el-radio label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { formatTimeStamp } from '@/utils';
import { validate } from '@/utils/validate';
export default {
  props: {
    title: {
      type: String,
      default: '新增'
    },
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    const pkActNameCheck = (rule, value, callback) => {
      var reg = /[^a-zA-Z0-9\u4e00-\u9fa5\u3002\uff0c\uff1a\uff08\uff09\uff1f\u201c\u201d\u3001\uff01,/.!:()?_""—-]/g;
      if (value === '') {
        callback('活动名称必填');
      } else if (reg.test(value)) {
        callback('禁止输入特殊符号');
      } else {
        callback();
      }
    };
    const endTimeCheck = (rule, value, callback) => {
      if (this.form.startTime === '') {
        callback('请先填开始时间');
      } else {
        if (value !== '') {
          // 新增
          if (!this.id) {
            if (this.form.startTime && (this.form.startTime.getTime() > value.getTime())) {
              callback('截止时间需大于开始时间！');
            } else {
              callback();
            }
            // 编辑
          } else {
            if (this.form.startTime && this.form.startTime > this.form.endTime) {
              callback('截止时间需大于开始时间！');
            } else { callback(); }
          }
        } else {
          callback('截止时间必填');
        }
      }
    };

    return {
      form: {
        pkActName: '', // 活动名称
        description: '', // 活动描述
        rule: '', // 奖励规则描述
        fileUrl: '', // 文件
        startTime: '',
        endTime: '',
        sendMap: '1',
        enable: '1',
        isAdd: 0,
        pkActCode: '',
        pkAttrFileName: '',
        startTimeNum: '',
        endTimeNum: ''
      },
      fileList: [],
      rules: {
        pkActName: [
          // { required: true, message: '活动名称必填', trigger: 'blur' }
          { required: true, validator: pkActNameCheck, trigger: 'blur' }
        ],
        description: [
          { required: true, message: '活动描述必填', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '活动开始时间必填', trigger: 'blur' }
        ],
        endTime: [
          // { required: true, message: '活动截止时间必填', trigger: 'blur' }
          { required: true, validator: endTimeCheck, trigger: 'blur' }
        ],
        sendMap: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        enable: [
          { required: true, message: '请选择', trigger: 'blur' }
        ]
      },
      isStartTimeShow: true,
      timeOptions: {
        disabledDate: (time) => {
          // 限制截止时间不能选择开始日期内
          if (this.form.startTime) {
            if (!this.id) {
              return this.form.startTime.getTime() > time.getTime();
            } else {
              return this.form.startTimeNum > time.getTime();
            }
          } else {
            return;
          }
        }
      },
      show: false
    };
  },
  watch: {

    visible(val) {
      this.show = val;
    },
    'form.startTime'(val) {
      if (val) {
        this.isStartTimeShow = false;
      }
    }
  },
  methods: {
    onChange(file, fileList) {
      if (file.raw.type === 'image/png' || file.raw.type === 'application/pdf') {
        this.fileList.push(file);
      } else {
        this.fileList = this.fileList.slice(0, 1);
        this.$message.error('请选择jpg或者pdf格式的文件');
      }
    },
    init() {
      // 获取活动
      if (this.id) {
        const params = {
          pkActId: this.id
        };
        this.$post('getActivity', params)
          .then(res => {
            const { fail, body } = res;
            if (!fail) {
              this.form.pkActCode = body.pkActCode;
              this.form.pkActName = body.pkActName;
              this.form.description = body.description;
              this.form.rule = body.rule;
              this.form.startTimeNum = body.startTime;
              this.form.endTimeNum = body.endTime;
              this.form.startTime = body.startTime;
              this.form.endTime = body.endTime;
              this.form.enable = body.enable;
              this.form.sendMap = body.sendMap ? '1' : '0';
              this.form.enable = body.enable;
              this.form.pkAttr = body.pkAttr;
              const pkAttrList = body.pkAttr.split('/');
              this.form.pkAttrFileName = pkAttrList[pkAttrList.length - 1];
            }
          });
      }
    },
    submit() {
      this.$refs['activityForm'].validate((valid) => {
        if (valid) {
          let keyName = 'addActivity';
          const formData = JSON.parse(JSON.stringify(this.form));
          formData.startTime = formatTimeStamp(new Date(formData.startTime).getTime(), 'yyyy/MM/DD HH:mm:ss');
          formData.endTime = formatTimeStamp(new Date(formData.endTime).getTime(), 'yyyy/MM/DD HH:mm:ss');
          // 编辑
          if (this.id) {
            formData.pkActId = this.id;
            keyName = 'updateActivity';
          }
          const params = {
            ...formData
          };

          this.$post(keyName, params)
            .then(res => {
              const { fail } = res;
              if (!fail) {
                this.show = false;
                this.$parent.getTableList();
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
              }
            });
        }
      });
    },
    uploadSuccess(url, file) {
      this.form.isAdd = 1;
      this.form.pkAttrFileName = file.name;
      this.form.fileUrl = url;
    },
    handleExceed(files, fileList) {
      this.$message.warning(`只能上传一个文件`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang = "scss" scoped>
.upload {
  display: inline-block;
  margin-left: 20px;
}
::v-deep .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
  display: none;
}
</style>

