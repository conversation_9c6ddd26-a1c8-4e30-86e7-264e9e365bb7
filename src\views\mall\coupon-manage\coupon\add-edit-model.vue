<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    :title="`${ isEdit ? '修改' : '新增'}优惠券配置页`"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        size="small"
        label-suffix=":"
      >
        <!-- 优惠券名称 -->
        <el-form-item label="优惠券名称" prop="couponName">
          <el-input v-model="form.couponName" placeholder="请输入优惠券名称" maxlength="10" show-word-limit clearable />
        </el-form-item>
        <!-- 优惠券类型 -->
        <el-form-item label="优惠券类型" required style="margin-bottom: 0;">
          <div class="coupon-type">
            <!-- 优惠券类型选择 -->
            <el-form-item prop="couponType">
              <el-radio-group v-model="form.couponType" :disabled="isEdit">
                <el-radio v-for="item in couponTypeList" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="form.couponType" class="ml10 flex-column">
              <!-- 满减 -->
              <el-form-item v-if="form.couponType == 1" prop="fullReduce">
                <span>满</span>
                <el-input-number
                  v-model="form.couponBeginThreshold"
                  :disabled="isEdit"
                  class="ml-mr"
                  placeholder="请输入"
                  :controls="false"
                  :min="0"
                  :precision="2"
                />
                <span>减</span>
                <el-input-number
                  v-model="form.couponValue"
                  :disabled="isEdit"
                  class="ml-mr"
                  placeholder="请输入"
                  :controls="false"
                  :min="0"
                  :precision="2"
                />
              </el-form-item>
              <!-- 抵扣 -->
              <el-form-item v-else prop="couponValueMap" class="mt-auto">
                <span>抵扣</span>
                <el-input-number
                  v-model="form.couponValueMap"
                  :disabled="isEdit"
                  class="ml-mr"
                  placeholder="请输入"
                  :controls="false"
                  :min="0"
                  :precision="2"
                />
                <span>元</span>
              </el-form-item>
            </div>
          </div>
        </el-form-item>
        <!-- 领券时间 -->
        <el-form-item label="领券时间" required style="margin-bottom: 0;">
          <div class="flex">
            <el-form-item prop="couponStartTime">
              <el-date-picker
                v-model="form.couponStartTime"
                :disabled="isEdit"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择开始日期时间"
                :picker-options="pickerOptionsCouponStartTime"
                @change="couponStartTimeChange"
              />
            </el-form-item>
            <span style="margin:0 10px;">至</span>
            <el-form-item prop="couponEndTime">
              <el-date-picker
                v-model="form.couponEndTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择结束日期时间"
                :picker-options="pickerOptionsCouponEndTime"
              />
            </el-form-item>
            <span class="ml10">(领券时间结束后，将自动下架优惠券)</span>
          </div>
        </el-form-item>
        <!-- 券有效期 -->
        <el-form-item label="券有效期" required style="margin-bottom: 0;">
          <div class="coupon-validity">
            <!-- 券有效期类型选择 -->
            <el-form-item prop="couponExpireType">
              <el-radio-group v-model="form.couponExpireType" :disabled="isEdit">
                <el-radio :label="1">领取后N天有效</el-radio>
                <el-radio :label="2">限定日期有效</el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="form.couponExpireType" class="ml10 flex-column">
              <!-- 天时分 -->
              <el-form-item v-if="form.couponExpireType == 1" prop="couponValidDays">
                <el-input-number
                  v-model="form.validDay"
                  :disabled="isEdit"
                  placeholder="请输入"
                  :controls="false"
                  :min="0"
                  :precision="0"
                />
                <span class="ml-mr">天</span>
                <el-input-number
                  v-model="form.validHour"
                  :disabled="isEdit"
                  placeholder="请输入"
                  :controls="false"
                  :min="0"
                  :precision="0"
                />
                <span class="ml-mr">时</span>
                <el-input-number
                  v-model="form.validMinute"
                  :disabled="isEdit"
                  placeholder="请输入"
                  :controls="false"
                  :min="0"
                  :precision="0"
                />
                <span class="ml-mr">分</span>
              </el-form-item>
              <!-- 券有效日期范围 -->
              <el-form-item v-else prop="couponExpireTime" class="mt-auto">
                <el-date-picker
                  v-model="form.couponExpireTime"
                  :disabled="isEdit"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :picker-options="pickerOptions"
                />
              </el-form-item>
            </div>
          </div>
        </el-form-item>
        <!-- 发放数量 -->
        <el-form-item label="发放数量" prop="couponNum">
          <el-input-number
            v-model="form.couponNum"
            :disabled="isEdit"
            placeholder="请输入发放数量"
            :controls="false"
            :min="0"
            :precision="0"
          />
          <span class="ml10">张</span>
        </el-form-item>
        <!-- 数量领完是否自动下架 -->
        <el-form-item label="数量领完是否自动下架" prop="ifAutomaticOffShelf">
          <el-radio-group v-model="form.ifAutomaticOffShelf">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 详情页是否展示 -->
        <el-form-item label="详情页是否展示" prop="ifDetailShow">
          <el-radio-group v-model="form.ifDetailShow">
            <el-radio v-for="item in couponDetailShowStatus" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 状态 -->
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status" :disabled="isEdit">
            <el-radio v-for="item in couponShelfStatus" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 适用用户 -->
        <el-form-item label="适用用户" prop="applyUser">
          <el-radio-group v-model="form.applyUser" :disabled="isEdit">
            <el-radio v-for="item in couponUserTypeList" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 每人限领 -->
        <el-form-item label="每人限领" prop="limitGetNum">
          <div class="flex">
            <el-input-number
              v-model="form.limitGetNum"
              :disabled="isEdit"
              placeholder="请输入每人限领"
              :controls="false"
              :min="1"
              :max="99"
              :precision="0"
            />
            <span class="ml-mr" style="font-weight: bold">张</span>
            <span>（请输入1-99数字）</span>
          </div>
        </el-form-item>
        <!-- 适用商品 -->
        <el-form-item label="适用商品" required style="margin-bottom: 0;">
          <div class="suitable-goods">
            <!-- 适用商品类型选择 -->
            <el-form-item prop="applyGoodsType">
              <el-radio-group v-model="form.applyGoodsType" :disabled="isEdit">
                <el-radio :label="1">指定类型可用</el-radio>
                <el-radio :label="2">指定商品可用</el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="form.applyGoodsType" class="ml10 flex-column">
              <el-form-item v-if="form.applyGoodsType == 1" prop="productTypeList">
                <el-checkbox-group v-model="form.productTypeList" :disabled="isEdit">
                  <el-checkbox v-for="item in goodsTypeList" :key="item.value" :label="item.value">{{ item.name }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item v-else prop="productIdList" class="mt-auto">
                <el-button type="primary" size="mini" :disabled="isEdit" @click="selectGoodsVisible = true">选择商品</el-button>
                <span class="ml-mr">已选择{{ tableData.length }}个商品</span>
              </el-form-item>
            </div>
          </div>
          <!-- 商品表格 -->
          <el-table v-if="form.applyGoodsType == 2" class="mt10" size="small" :data="tableData" border>
            <el-table-column prop="id" label="商品id" align="center" />
            <el-table-column prop="productName" label="商品名称" align="center" />
            <el-table-column prop="productType" label="商品类型" align="center">
              <template slot-scope="scope">
                {{ scope.row.productType | goodsTypeEnum2 }}
              </template>
            </el-table-column>
            <el-table-column prop="retailPrice" label="成本价" align="center" />
            <el-table-column prop="marketPrice" label="售价" align="center" />
            <el-table-column label="兑换起止时间" align="center">
              <template slot-scope="scope">
                <p>起：{{ scope.row.sellingStartTime }}</p>
                <p>止：{{ scope.row.sellingEndTime || '永久有效' }}</p>
              </template>
            </el-table-column>
            <el-table-column label="是否上架" align="center">
              <template slot-scope="scope">
                {{ scope.row.status | yesOrNoEnum }}
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="primary" size="small" :disabled="isEdit" @click="handleTableDelete(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </div>

    <select-goods-modal :visible.sync="selectGoodsVisible" :tablePropData="tableData" goodsStatus="" @confirm="updateTableData" />
  </common-dialog>
</template>

<script>
import moment from 'moment';
import selectGoodsModal from './../../components/select-goods-modal.vue';
import { arrToEnum } from '@/utils';
import {
  goodsType,
  couponType,
  couponUserType,
  couponShelfStatus,
  couponDetailShowStatus
} from './../../type';
const goodsTypeList = [
  {
    name: '自营好物',
    value: 1
  },
  {
    name: '虚拟产品',
    value: 2
  },
  {
    name: '京东百货',
    value: 3
  }
];
const goodsTypeEnum = arrToEnum(goodsTypeList);
const goodsTypeEnum2 = arrToEnum(goodsType);

export default {
  components: {
    selectGoodsModal
  },
  filters: {
    goodsTypeEnum(val) {
      return goodsTypeEnum[val] || '/';
    },
    goodsTypeEnum2(val) {
      return goodsTypeEnum2[val] || '/';
    },
    yesOrNoEnum(val) {
      const JudgeEnum = { 0: '下架', 1: '上架' };
      return JudgeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentRow: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isEdit: false, // 是否编辑
      couponShelfStatus: couponShelfStatus, // 优惠券上架状态
      couponDetailShowStatus: couponDetailShowStatus, // 优惠券详情页是否展示
      goodsTypeList: goodsTypeList,
      couponTypeList: couponType,
      couponUserTypeList: couponUserType,
      selectGoodsVisible: false, // 选择商品弹框
      confirmLoading: false,
      show: false,
      form: {
        productTypeList: [],
        productIdList: [],
        couponExpireTime: []
      },
      tableData: [],
      rules: {
        couponName: [{ required: true, message: '请输入优惠券名称', trigger: 'blur' }],
        couponType: [{ required: true, message: '请选择优惠券类型', trigger: 'change' }],
        fullReduce: [{ validator: this.checkFullReduce, trigger: 'blur' }], // 满减栏
        couponValueMap: [{ required: true, message: '请输入抵扣金额', trigger: 'blur' }],
        couponStartTime: [
          { required: true, message: '请选择领券开始日期时间', trigger: 'change' },
          { validator: this.checkCouponStartTime, trigger: 'change' }
        ],
        couponEndTime: [
          { required: true, message: '请选择领券结束日期时间', trigger: 'change' },
          { validator: this.checkCouponEndTime, trigger: 'change' }
        ],
        couponExpireType: [{ required: true, message: '请选择券有效期', trigger: 'change' }],
        couponValidDays: [{ validator: this.checkConponValidDays, trigger: 'blur' }],
        couponExpireTime: [
          { required: true, message: '请选择限定日期', trigger: 'change' },
          { validator: this.checkTimeIsPassed, trigger: 'change' }
        ],
        couponNum: [{ required: true, message: '请输入发放数量', trigger: 'blur' }],
        ifAutomaticOffShelf: [{ required: true, message: '请选择数量领完是否自动下架', trigger: 'change' }],
        ifDetailShow: [{ required: true, message: '请选择详情页是否展示', trigger: 'change' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        applyUser: [{ required: true, message: '请选择适用用户', trigger: 'change' }],
        limitGetNum: [{ required: true, message: '请输入每人限领', trigger: 'blur' }],
        applyGoodsType: [{ required: true, message: '请选择适用商品', trigger: 'change' }],
        productTypeList: [{ required: true, message: '请选择商品类型', trigger: 'change' }],
        productIdList: [{ required: true, message: '请选择商品', trigger: 'change' }]
      }
    };
  },
  computed: {
    // 禁止领券开始日期
    pickerOptionsCouponStartTime() {
      return {
        disabledDate: (time) => {
          if (!this.isEdit) {
            // 领券开始日期，新增的时候 今天之前都是不可选
            return time.getTime() < moment().startOf('day').valueOf();
          } else {
            // 编辑的时候不做限制
            return false;
          }
        }
      };
    },
    // 禁止领券结束日期
    pickerOptionsCouponEndTime() {
      return {
        disabledDate: (time) => {
          if (!this.isEdit) {
            // 领券结束日期，新增的时候 今天之前都是不可选
            return time.getTime() < moment().startOf('day').valueOf();
          } else {
            // 领券结束日期，编辑的时候 相对开始日期之前都是不可选
            return time.getTime() < new Date(this.form.couponStartTime).getTime();
          }
        }
      };
    },
    // 禁止限定日期
    pickerOptions() {
      return {
        disabledDate: (time) => {
          return time.getTime() < moment().startOf('day').valueOf();
        }
      };
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 校验领券开始日期时间
    checkCouponStartTime(rule, value, callback) {
      const now = new Date().getTime();
      const startTime = new Date(value).getTime();
      if (!this.isEdit && now > startTime) {
        callback(new Error('开始日期时间不能是历史时间'));
      } else {
        callback();
      }
    },
    // 校验领券开始日期时间
    checkCouponEndTime(rule, value, callback) {
      const now = new Date().getTime();
      const startTime = new Date(this.form.couponStartTime).getTime();
      const endTime = new Date(value).getTime();
      if (!this.isEdit && now > endTime) {
        callback(new Error('结束日期时间不能是历史时间'));
      } else if (endTime < startTime) {
        callback(new Error('结束日期时间不能小于开始日期时间'));
      } else {
        callback();
      }
    },
    // 校验限定日期有效时间是否是历史时间
    checkTimeIsPassed(rule, value, callback) {
      const now = new Date().getTime();
      const startTime = new Date(value[0]).getTime();
      const endTime = new Date(value[1]).getTime();
      if (!this.isEdit && (value.length > 0 && now > startTime || now > endTime)) {
        callback(new Error('开始时间或结束时间不能是历史时间'));
      } else {
        callback();
      }
    },
    // 校验优惠券领取后N天有效规则
    checkConponValidDays(rule, value, callback) {
      if (this.form.validDay == undefined || this.form.validHour == undefined || this.form.validMinute == undefined) {
        callback(new Error('请输入'));
      } else if (this.form.validHour >= 24) {
        callback(new Error('小时不能大于等于24小时'));
      } else if (this.form.validMinute >= 60) {
        callback(new Error('分钟不能大于等于60分钟'));
      } else if (this.form.validDay > 999) {
        callback(new Error('天数不能大于999天'));
      } else {
        callback();
      }
    },
    // 校验满减栏规则
    checkFullReduce(rule, value, callback) {
      if (!this.form.couponBeginThreshold || !this.form.couponValue) {
        callback(new Error('满X和减Y都是必填项'));
      } else if (this.form.couponBeginThreshold < this.form.couponValue) {
        callback(new Error('满X减Y，限制X>=Y'));
      } else {
        callback();
      }
    },
    // 领券开始时间改变回调
    couponStartTimeChange(value) {
      const startTime = new Date(value).getTime();
      const endTime = new Date(this.form.couponEndTime).getTime();
      if (startTime < endTime) {
        this.$refs.formModal.validateField('couponEndTime');
      }
    },
    // 天, 小时, 分钟转换为分钟
    convertToMinutes(days, hours, minutes) {
      const minutesInADay = 24 * 60;
      const minutesInAnHour = 60;
      const totalMinutes = (days * minutesInADay) + (hours * minutesInAnHour) + minutes;
      return totalMinutes;
    },
    // 将分钟转换为天, 小时, 分钟
    convertMinutesToDHM(totalMinutes) {
      const minutesInADay = 24 * 60;
      const minutesInAnHour = 60;
      const days = Math.floor(totalMinutes / minutesInADay); // 计算天数
      const hours = Math.floor((totalMinutes % minutesInADay) / minutesInAnHour); // 计算小时数
      const minutes = totalMinutes % minutesInAnHour; // 计算剩余的分钟数
      return { days, hours, minutes };
    },
    // 表格删除
    handleTableDelete(index) {
      this.tableData.splice(index, 1);
      this.form.productIdList = this.tableData.map(item => item.id);
    },
    // 更新表格数据
    updateTableData(data) {
      this.tableData = data;
      this.form.productIdList = data.map(item => item.id);
    },
    // 打开弹框
    open() {
      if (this.currentRow.couponId) {
        this.$http.get(`/productCoupon/getById/${this.currentRow.couponId}`).then(res => {
          this.isEdit = true;
          const { fail, body } = res;
          if (!fail) {
            if (body.couponType == 2) body.couponValueMap = body.couponValue;
            // 券有效期
            if (body.couponExpireType == 1) {
              const { days, hours, minutes } = this.convertMinutesToDHM(body.couponExpireDays);
              body.validDay = days;
              body.validHour = hours;
              body.validMinute = minutes;
            } else {
              body.couponExpireTime = [body.couponExpireStartTime, body.couponExpireEndTime];
            }
            this.form = body;
            this.tableData = body.productList;
            this.form.productIdList = this.tableData.map(item => item.id);
          }
        });
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        const form = JSON.parse(JSON.stringify(this.form));

        if (form.couponType == 2) form.couponValue = form.couponValueMap;
        delete form.couponValueMap;

        // 券有效期
        if (form.couponExpireType == 1) {
          form.couponExpireDays = this.convertToMinutes(form.validDay, form.validHour, form.validMinute);
          delete form.validDay;
          delete form.validHour;
          delete form.validMinute;
        } else {
          form.couponExpireStartTime = form.couponExpireTime[0];
          form.couponExpireEndTime = form.couponExpireTime[1];
        }

        // 券使用结束时间 < 领券结束时间
        if (form.couponExpireType == 2 && new Date(form.couponExpireEndTime).getTime() < new Date(form.couponEndTime).getTime()) {
          return this.$message.error('保存失败，券使用结束时间需大于等于领券结束时间！');
        }

        this.confirmLoading = true;

        // 删除无效大数据
        delete form.productList;
        if (this.isEdit) delete form.productIdList;

        const apiKey = this.isEdit ? 'updateZMproductCoupon' : 'addZMproductCoupon';

        this.$post(apiKey, form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
}
.ml-mr {
  margin: 0 10px;
}
.coupon-validity, .coupon-type, .suitable-goods {
  display: flex;
  flex-direction: row;

  .el-radio {
    display: block;
    line-height: 32px;
    &:not(:nth-last-of-type(1)) {
      margin-bottom: 20px;
    }
  }

  .flex-column {
    display: flex;
    flex-direction: column;
  }

  .mt-auto {
    margin-top: auto;
  }
}
</style>
