<template>
    <div>
        <el-dialog title="导入" :visible.sync="showInfo" width="40%" :before-close="handleClose"  top="5vh">
        <el-form label-width="80px">
            <el-form-item label="模板：">
                <el-button @click="downFile">下载模板</el-button>
                <div>说明：</div>
                <div>（关键列：学员姓名，学籍编码）</div>
                <div>请严格按照模板填写，否则可能导致无法准确导入</div>
            </el-form-item>
            <el-form-item label="导入：">
                <div style="display: flex;">
                    <!-- :data="uploadFileParams" -->
                    <el-upload class="upload-demo" ref="upload" action="/feeTypeChange/changeFeeTypeByImport" :on-change="handleChange"
                         :file-list="fileList" :auto-upload="false" :on-success="uploadSuccess"
                        @on-remove="handleRemove">
                        <el-button slot="trigger" size="small" type="primary">浏览文件</el-button>
                    </el-upload>
                    <el-input v-model="fileName" :disabled="true" style="width:200px"></el-input>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="submitUpload">开始导入</el-button>
                <el-button  @click="handleClose">取消</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
    <el-dialog title="导入提示" :visible.sync="showErr" width="30%" :before-close="handleClose1"  top="5vh">
        <h3 v-if="total >= 0&&failedCount>0">一共{{ total }}条数据：导入错误{{ failedCount }}条</h3>
        <h3 v-else >一共{{ total }}条数据：导入成功{{ total }}条</h3>
            <el-table :data="tableData" style="width: 100%" border  v-loading="loading" v-if="failedCount>0">
                <el-table-column prop="learnId" label="学业编码" align="center">
                </el-table-column>
                <el-table-column prop="stdName" label="姓名" align="center">
                </el-table-column>
                <el-table-column prop="grade" label="年级" align="center">
                </el-table-column>
                <el-table-column prop="unvsName" label="院校" align="center">
                </el-table-column>
                <el-table-column prop="itemCode" label="缴费科目" align="center">
                </el-table-column>
                <el-table-column prop="feeTypeName" label="收取方式" align="center">
                </el-table-column>
            </el-table>
            <div v-if="failedCount > 10" style="text-align: center;margin-top: 10px;font-size: 25px;">...</div>
            <div v-if="failedCount > 0" style="text-align: center;margin-top: 10px;font-size: 15px;">请修改后重新导入</div>
        </el-dialog>

    </div>
</template>

<script>
export default {
    props: {
        showInfo: {
            type: Boolean,
            default: false
        },
        id: {
            type: Number,
        }
    },
    data() {
        return {
            fileName: '',
            fileList: [],
            action: '',
            // uploadFileParams: {
            //     mpMsgId: '',

            // },
            tableData: [],
            succeedCount: 0,
            failedCount: 0,
            total: 0,
            showErr:false,
            loading:true

        };
    },
    methods: {
        handleRemove(file, fileList) {
            console.log(file, fileList,'file, fileList');


        },
        handleClose1(){
            this.tableData = []
            this.showErr = false
            this.fileName = ''
            this.fileList = []

        },
        handleClose() {
            console.log(this.fileList,'this.fileList');
            this.fileName = ''
            this.fileList = []
            this.$emit('showInfo', false)
            console.log(this.fileList,'this.fileList');

        },

        handleChange(file, fileList) {
            this.fileName = file.name
        },
        downFile() {
            window.open('https://yzims.oss-cn-shenzhen.aliyuncs.com/updateFeeTypeTemplate.xlsx')
        },

        submitUpload() {
            if(this.fileName){
            this.loading = true
            this.showErr = true
            // this.uploadFileParams.mpMsgId = this.id
            this.$refs.upload.submit();
            this.$emit('showInfo', false)
            }else{
                this.$message({ type: 'error', message: '请先选择上传文件' });
            }

        },
        uploadSuccess(response, file, fileList) {
            console.log(response,'response');
            if (response.ok) {
                this.loading = false
                this.succeedCount = 0
                this.failedCount = 0
                this.total = response.body.total
                this.tableData = []
                this.$message({ type: 'success', message:'导入成功' });
            } else {
                this.loading = false
                console.log(response, 'response, file, fileList');
                this.succeedCount = response.body.succeedCount
                this.failedCount = response.body.failedCount
                this.total = response.body.total
                this.tableData = response.body.failedList
                this.$message({ type: 'error', message: response.msg });
            }
            console.log(this.succeedCount, this.failedCount, this.total, this.tableData);


        }
    }
}
</script>

<style></style>