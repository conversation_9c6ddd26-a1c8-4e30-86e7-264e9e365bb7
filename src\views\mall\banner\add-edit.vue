<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    :title="`${ isEdit ? '修改' : '新增'}` + '商城banner配置'"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        size="small"
      >
        <el-form-item label="banner名称:" prop="bannerName">
          <el-input v-model="form.bannerName" placeholder="用于内部显示" />
        </el-form-item>

        <el-form-item label="展示时间:" required>
          <div style="display: flex;">
            <el-form-item prop="showTimeStatus">
              <el-radio-group v-model="form.showTimeStatus">
                <el-radio label="PERMANENT_EFFECTIVE">永久有效</el-radio>
                <el-radio label="TIME_EFFECTIVE">固定时间</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="form.showTimeStatus == 'TIME_EFFECTIVE'" class="ml10" prop="unveilTime">
              <el-date-picker
                v-model="form.unveilTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </div>
        </el-form-item>

        <el-form-item label="banner权重:" prop="weight">
          <div style="display: flex;">
            <el-input-number v-model="form.weight" :controls="false" :min="0" :max="9999" placeholder="请输入banner权重" style="width: 200px" />
            <span>（数字越大，排序越靠前）</span>
          </div>
        </el-form-item>

        <el-form-item label="banner图:" prop="imgUrl">
          <p>建议图片尺寸为686*270, 图片大小限制1M内</p>
          <upload-file
            :size="1"
            :max-limit="1"
            exts="jpg|png"
            :file-list="imgUrl"
            @remove="goodsImgRemove"
            @success="goodsImgSuccess"
          />
        </el-form-item>

        <el-form-item label="跳转页面:" required>
          <div class="banner-page">
            <el-form-item prop="jumpType">
              <el-radio-group v-model="form.jumpType" @change="jumpTypaChange">
                <el-radio label="ACTIVITY">商品活动页</el-radio>
                <el-radio label="PRODUCT">商品详情页</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="form.jumpType" prop="jumpId" class="banner-page-input-box">
              <el-input v-if="form.jumpType == 'ACTIVITY'" v-model="form.jumpId" placeholder="请输入活动id" />
              <el-input v-if="form.jumpType == 'PRODUCT'" v-model="form.jumpId" class="goods-id-input" placeholder="请输入商品id" />
            </el-form-item>
          </div>
        </el-form-item>

        <el-form-item label="活动状态:" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前id
    currentId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      confirmLoading: false,
      show: false,
      isEdit: false, // 是否编辑
      form: {
        imgUrl: '',
        jumpId: undefined
      },
      imgUrl: [],
      rules: {
        bannerName: [
          { required: true, message: '请输入banner名称', trigger: 'blur' }
        ],
        showTimeStatus: [
          { required: true, message: '请选择展示时间', trigger: 'change' }
        ],
        unveilTime: [
          { required: true, message: '请选择展示时间', trigger: 'change' }
        ],
        weight: [
          { required: true, message: '请输入banner权重', trigger: 'blur' }
        ],
        imgUrl: [
          { required: true, message: '请上传banner图片', trigger: 'change' }
        ],
        jumpType: [
          { required: true, message: '请选择跳转界面', trigger: 'change' }
        ],
        jumpId: [
          { required: true, message: '请输入id', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    jumpTypaChange() {
      this.form.jumpId = undefined;
    },
    // 商品图片删除
    goodsImgRemove({ file, fileList }) {
      this.form.imgUrl = '';
    },
    // 商品图片上传成功
    goodsImgSuccess({ response, file, fileList }) {
      this.form.imgUrl = response;
    },
    // 打开弹框
    open() {
      if (this.currentId) {
        this.isEdit = true;
        this.$http.get(`/mallBannerConfig/getById/${this.currentId}`).then(res => {
          const { fail, body } = res;
          if (!fail) {
            const { id, bannerName, imgUrl, weight, status, showTimeStatus, showStartTime, showEndTime, jumpType, jumpId } = body;
            this.form = {
              id,
              bannerName,
              imgUrl,
              weight,
              status,
              showTimeStatus,
              jumpType,
              jumpId,
              unveilTime: showTimeStatus == 'TIME_EFFECTIVE' ? [showStartTime, showEndTime] : []
            };
            this.imgUrl.push({ url: ossUri + imgUrl });
          }
        });
      }
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        this.confirmLoading = true;

        const form = JSON.parse(JSON.stringify(this.form));
        if (form.showTimeStatus == 'TIME_EFFECTIVE') {
          form.showStartTime = form.unveilTime[0];
          form.showEndTime = form.unveilTime[1];
        } else {
          delete form.showStartTime;
          delete form.showEndTime;
        }
        delete form.unveilTime;

        let apiKey = 'addZMBanner';
        if (this.isEdit) {
          apiKey = 'updateZMBanner';
        }
        this.$post(apiKey, form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.confirmLoading = false;
        });
      });
    }
  }
};
</script>

<style lang='scss' scoped>
.banner-page {
  display: flex;
  flex-direction: row;

  .el-radio {
    display: block;
    line-height: 32px;
  }
  .banner-page-input-box {
    flex: 1;
  }
  .goods-id-input {
    margin-top: 30px;
  }
}
</style>
