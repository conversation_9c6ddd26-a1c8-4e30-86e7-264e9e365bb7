<template>
  <common-dialog
    is-full
    title="选择老师"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='助学老师姓名' prop='empName'>
          <el-input v-model="form.empName" placeholder="请输入" maxlength="50" />
        </el-form-item>
        <el-form-item label='助学老师手机号' prop='mobile'>
          <el-input v-model="form.mobile" placeholder="请输入" maxlength="50" />
        </el-form-item>
        <el-form-item label='身份证号' prop='idCard'>
          <el-input v-model="form.idCard" placeholder="请输入" maxlength="50" />
        </el-form-item>
        <el-form-item label='是否在职' prop='empStatus'>
          <el-select
            v-model="form.empStatus"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label='远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder="请输入" maxlength="50" />
        </el-form-item>

        <el-form-item label='参加活动' prop='isJoin'>
          <el-select
            v-model="form.isJoin"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="参加" value="1" />
            <el-option label="不参加" value="0" />
          </el-select>
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>
      </el-form>

      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button size="small" type="primary" @click="toggleParticipate(1)">参加PK</el-button>
        <el-button size="small" type="danger" @click="toggleParticipate(2)">不参加PK</el-button>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="员工号" prop="staffNo" align="center" />
        <el-table-column label="远智编号" prop="yzCode" align="center" />
        <el-table-column label="助学老师姓名" prop="empName" align="center" />
        <el-table-column label="所属部门" prop="dpName" align="center" />
        <el-table-column label="岗位" prop="jobTitle" align="center" />
        <el-table-column label="手机号" prop="mobile" align="center" />
        <el-table-column label="身份证号" prop="idCard" align="center" />
        <el-table-column label="是否在职" prop="empStatus" align="center">
          <template v-slot="scope">
            <el-tag v-if="scope.row.empStatus === '1'" type="success">在职</el-tag>
            <el-tag v-if="scope.row.empStatus === '2'" type="danger">离职</el-tag>
            <el-tag v-if="scope.row.empStatus === '3'" type="warning">休假</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="参加PK" prop="isJoin" align="center">
          <template v-slot="scope">
            <el-tag v-if="scope.row.isJoin === '1'" type="success">参加</el-tag>
            <el-tag v-else type="danger">不参加</el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    pkChildId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      show: false,
      form: {
        empName: '',
        mobile: '',
        idCard: '',
        empStatus: '',
        yzCode: '',
        isJoin: ''
      },
      tableLoading: false,
      tableData: [],
      multipleSelection: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    getTableList() {
      this.tableLoading = true;
      const params = {
        ...this.form,
        pkChildId: this.pkChildId,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('personList', params).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    toggleParticipate(status) {
      if (this.multipleSelection.length === 0) {
        this.$message.error('请选择助学老师!');
        return;
      }

      const params = {
        type: status,
        pkChildId: this.pkChildId,
        empIdList: this.multipleSelection.map(item => item.empId)
      };
      this.$post('toggleParticipate', params, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          // 刷新列表
          this.getTableList();
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    open() {
      this.getTableList();
    },
    close() {
      this.$emit('refresh');
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
.yz-table-btnbox {
  margin-top: 10px;
}
</style>
