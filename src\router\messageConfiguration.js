// 存放所有新建的落地页面的路由文件
import Layout from '@/layout';
export default [
  {
    path: '/serviceNumber',
    component: Layout,
    meta: {
      title: '消息配置',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: '/messageConfiguration/type=1',
        component: () => import('@/views/serviceNumber/messageConfiguration/index'),
        meta: {
          title: '消息配置'
        }
      },
      {
        path: '/messageReview/type=2',
        component: () => import('@/views/serviceNumber/messageConfiguration/index'),
        meta: {
          title: '消息审核'
        }
      },
      {
        path: '/messageSynchronization/type=3',
        component: () => import('@/views/serviceNumber/messageConfiguration/index'),
        meta: {
          title: '消息同步'
        }
      },
      {
        path: '/messageRecord',
        component: () => import('@/views/serviceNumber/messageConfiguration/messageRecord'),
        meta: {
          title: '消息记录'
        }
      }
    ]
  },  
  {
    path: '/tuition',
    component: Layout,
    meta: {
      title: '学费收取方式变更',
      icon: 'el-icon-date',
      breadcrumb: false
    },
    children: [
      {
        path: '/collectionMethod',
        component: () => import('@/views/collectionMethod/index'),
        meta: {
          title: '学费收取方式'
        }
      },
    ]
  },
  {
    path: '/PayApplication',
    component: Layout,
    meta: {
      title: '国开申请',
      icon: 'el-icon-date',
      breadcrumb: false,
    },
    children: [
      {
        path: '/gkPayApplication',
        component: () => import('@/views/gkPayApplication/index'),
        meta: {
          title: '国开按学期缴费申请'
        }
      },
    ]
  },
];
