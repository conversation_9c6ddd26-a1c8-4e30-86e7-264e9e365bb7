<template>
  <div class="yz-base-container home">
    <h1>毕业论文格式刷</h1>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  mounted() {
  },
  beforeDestroy() {
  }
};
</script>

<style lang="scss" scoped>
.home {
  height: calc(100vh - 62px);
  h1 {
    position: relative;
    top: 40%;
    font-size: 36px;
    font-weight: 600;
    text-align: center;
    color:rgb(48,65,86);
  }
}
</style>
